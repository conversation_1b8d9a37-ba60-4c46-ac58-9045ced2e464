<?php
/**
 * The template for displaying search results pages
 *
 * @package Alachiq Group Pro
 * @since 1.0.0
 */

get_header(); ?>

<main class="alachiq-main-content">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="alachiq-content-area">
                    <header class="page-header">
                        <h1 class="page-title">
                            <?php
                            printf(
                                __('Axtarış nəticələri: "%s"', 'alachiqgroup-pro'),
                                '<span class="search-query">' . get_search_query() . '</span>'
                            );
                            ?>
                        </h1>
                        
                        <?php if (have_posts()) : ?>
                            <p class="search-results-count">
                                <?php
                                global $wp_query;
                                printf(
                                    _n(
                                        '%d nəticə tapıldı',
                                        '%d nəticə tapıldı',
                                        $wp_query->found_posts,
                                        'alachiqgroup-pro'
                                    ),
                                    $wp_query->found_posts
                                );
                                ?>
                            </p>
                        <?php endif; ?>
                    </header>

                    <?php if (have_posts()) : ?>
                        <div class="alachiq-search-results">
                            <?php while (have_posts()) : the_post(); ?>
                                <article id="post-<?php the_ID(); ?>" <?php post_class('alachiq-search-result'); ?>>
                                    
                                    <div class="search-result-content">
                                        <header class="search-result-header">
                                            <h2 class="search-result-title">
                                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                            </h2>
                                            
                                            <div class="search-result-meta">
                                                <span class="search-result-type">
                                                    <i class="fas fa-file-alt"></i>
                                                    <?php echo get_post_type_object(get_post_type())->labels->singular_name; ?>
                                                </span>
                                                
                                                <span class="search-result-date">
                                                    <i class="fas fa-calendar-alt"></i>
                                                    <?php echo get_the_date(); ?>
                                                </span>
                                                
                                                <?php if (get_the_category()) : ?>
                                                    <span class="search-result-category">
                                                        <i class="fas fa-folder"></i>
                                                        <?php the_category(', '); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </header>

                                        <div class="search-result-excerpt">
                                            <?php 
                                            if (has_excerpt()) {
                                                the_excerpt();
                                            } else {
                                                echo wp_trim_words(get_the_content(), 30, '...');
                                            }
                                            ?>
                                        </div>

                                        <footer class="search-result-footer">
                                            <a href="<?php the_permalink(); ?>" class="btn btn-outline-primary btn-sm">
                                                <?php pll_e_safe('Daha çox oxu'); ?>
                                                <i class="fas fa-arrow-right"></i>
                                            </a>
                                        </footer>
                                    </div>
                                </article>
                            <?php endwhile; ?>
                        </div>

                        <?php
                        // Pagination
                        the_posts_pagination(array(
                            'mid_size' => 2,
                            'prev_text' => '<i class="fas fa-chevron-left"></i> ' . pll___safe('Əvvəlki'),
                            'next_text' => pll___safe('Növbəti') . ' <i class="fas fa-chevron-right"></i>',
                            'class' => 'alachiq-pagination'
                        ));
                        ?>

                    <?php else : ?>
                        
                        <div class="alachiq-no-results">
                            <div class="no-results-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h2><?php pll_e_safe('Heç bir nəticə tapılmadı'); ?></h2>
                            <p><?php pll_e_safe('Təəssüf ki, axtarışınıza uyğun məzmun tapılmadı. Başqa açar sözlərlə cəhd edin.'); ?></p>
                            
                            <div class="search-suggestions">
                                <h3><?php pll_e_safe('Axtarış təklifləri:'); ?></h3>
                                <ul>
                                    <li><?php pll_e_safe('Daha ümumi açar sözlər istifadə edin'); ?></li>
                                    <li><?php pll_e_safe('Yazım səhvlərini yoxlayın'); ?></li>
                                    <li><?php pll_e_safe('Daha az açar söz istifadə edin'); ?></li>
                                </ul>
                            </div>
                            
                            <div class="new-search-form">
                                <h3><?php pll_e_safe('Yenidən axtarış edin:'); ?></h3>
                                <?php get_search_form(); ?>
                            </div>
                        </div>

                    <?php endif; ?>
                </div>
            </div>

            <div class="col-lg-4">
                <aside class="alachiq-sidebar">
                    <div class="widget search-widget">
                        <h3 class="widget-title"><?php pll_e_safe('Yeni axtarış'); ?></h3>
                        <?php get_search_form(); ?>
                    </div>
                    
                    <div class="widget">
                        <h3 class="widget-title"><?php pll_e_safe('Populyar səhifələr'); ?></h3>
                        <ul class="popular-pages">
                            <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('layiheler'))); ?>"><i class="fas fa-building"></i> <?php pll_e_safe('Layihələr'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('xidmetlerimiz'))); ?>"><i class="fas fa-tools"></i> <?php pll_e_safe('Xidmətlərimiz'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('dekorasiyalar'))); ?>"><i class="fas fa-paint-brush"></i> <?php pll_e_safe('Dekorasiyalar'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('haqqimizda'))); ?>"><i class="fas fa-info-circle"></i> <?php pll_e_safe('Haqqımızda'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('elaqe'))); ?>"><i class="fas fa-phone"></i> <?php pll_e_safe('Əlaqə'); ?></a></li>
                        </ul>
                    </div>

                    <?php
                    if (is_active_sidebar('sidebar-1')) {
                        dynamic_sidebar('sidebar-1');
                    }
                    ?>
                </aside>
            </div>
        </div>
    </div>
</main>



<?php get_footer(); ?>

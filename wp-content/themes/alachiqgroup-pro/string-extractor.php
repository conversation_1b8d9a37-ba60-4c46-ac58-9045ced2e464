<?php
/**
 * String Extractor and Polylang Database Importer
 * 
 * This script identifies all translatable strings in PHP files and adds them to Polylang database
 * Supports: _e(), __(), _x(), esc_html_e(), esc_attr_e(), pll_e_safe(), pll___safe()
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

class StringExtractor {
    
    private $theme_path;
    private $strings = [];
    private $stats = [
        'files_scanned' => 0,
        'strings_found' => 0,
        'strings_added' => 0,
        'strings_updated' => 0,
        'errors' => 0
    ];
    
    public function __construct() {
        $this->theme_path = get_template_directory();
    }
    
    /**
     * Main execution function
     */
    public function run() {
        echo "<h1>Alachiq Group Pro - String Extractor & Polylang Importer</h1>\n";
        echo "<p>Scanning theme files for translatable strings...</p>\n";
        
        // Step 1: Extract strings from PHP files
        $this->extractStringsFromFiles();
        
        // Step 2: Display found strings
        $this->displayFoundStrings();
        
        // Step 3: Add strings to Polylang database
        $this->addStringsToPolylang();
        
        // Step 4: Display statistics
        $this->displayStatistics();
    }
    
    /**
     * Extract strings from all PHP files in theme
     */
    private function extractStringsFromFiles() {
        $php_files = $this->getPhpFiles($this->theme_path);
        
        foreach ($php_files as $file) {
            $this->stats['files_scanned']++;
            $this->extractStringsFromFile($file);
        }
    }
    
    /**
     * Get all PHP files in directory recursively
     */
    private function getPhpFiles($directory) {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                // Skip backup files and this script
                $filename = $file->getFilename();
                if (strpos($filename, 'backup') === false && 
                    $filename !== 'string-extractor.php') {
                    $files[] = $file->getPathname();
                }
            }
        }
        
        return $files;
    }
    
    /**
     * Extract strings from a single file
     */
    private function extractStringsFromFile($file_path) {
        $content = file_get_contents($file_path);
        $relative_path = str_replace($this->theme_path . '/', '', $file_path);
        
        // Patterns for different translation functions
        $patterns = [
            // WordPress translation functions
            '/_e\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]alachiqgroup-pro[\'"]\s*\)/' => '_e',
            '/__\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]alachiqgroup-pro[\'"]\s*\)/' => '__',
            '/esc_html_e\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]alachiqgroup-pro[\'"]\s*\)/' => 'esc_html_e',
            '/esc_attr_e\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]alachiqgroup-pro[\'"]\s*\)/' => 'esc_attr_e',
            
            // Polylang safe functions
            '/pll_e_safe\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*\)/' => 'pll_e_safe',
            '/pll___safe\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*\)/' => 'pll___safe',
            '/pll_esc_html___safe\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*\)/' => 'pll_esc_html___safe',
            '/pll_esc_attr___safe\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*\)/' => 'pll_esc_attr___safe',
        ];
        
        foreach ($patterns as $pattern => $function_type) {
            if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $string = $match[1];
                    
                    // Skip empty strings and HTML tags
                    if (empty(trim($string)) || $this->isHtmlTag($string)) {
                        continue;
                    }
                    
                    // Clean the string
                    $clean_string = $this->cleanString($string);
                    
                    if (!isset($this->strings[$clean_string])) {
                        $this->strings[$clean_string] = [
                            'original' => $string,
                            'clean' => $clean_string,
                            'function' => $function_type,
                            'files' => [],
                            'context' => 'alachiqgroup-pro'
                        ];
                        $this->stats['strings_found']++;
                    }
                    
                    // Add file reference
                    if (!in_array($relative_path, $this->strings[$clean_string]['files'])) {
                        $this->strings[$clean_string]['files'][] = $relative_path;
                    }
                }
            }
        }
    }
    
    /**
     * Check if string is an HTML tag
     */
    private function isHtmlTag($string) {
        return preg_match('/<[^>]+>/', $string);
    }
    
    /**
     * Clean string for database storage
     */
    private function cleanString($string) {
        // Remove HTML tags but keep the text content
        $clean = strip_tags($string);
        
        // Decode HTML entities
        $clean = html_entity_decode($clean, ENT_QUOTES, 'UTF-8');
        
        // Trim whitespace
        $clean = trim($clean);
        
        return $clean;
    }
    
    /**
     * Display found strings in a table
     */
    private function displayFoundStrings() {
        echo "<h2>Found Strings ({$this->stats['strings_found']} total)</h2>\n";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='width:100%; border-collapse: collapse;'>\n";
        echo "<tr style='background-color: #f0f0f0;'>\n";
        echo "<th>String</th><th>Function</th><th>Files</th><th>Status</th>\n";
        echo "</tr>\n";
        
        foreach ($this->strings as $key => $data) {
            $files_list = implode(', ', array_slice($data['files'], 0, 3));
            if (count($data['files']) > 3) {
                $files_list .= '... +' . (count($data['files']) - 3) . ' more';
            }
            
            echo "<tr>\n";
            echo "<td>" . esc_html($data['clean']) . "</td>\n";
            echo "<td>" . esc_html($data['function']) . "</td>\n";
            echo "<td><small>" . esc_html($files_list) . "</small></td>\n";
            echo "<td id='status-" . md5($key) . "'>Pending...</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    }
    
    /**
     * Add strings to Polylang database
     */
    private function addStringsToPolylang() {
        echo "<h2>Adding Strings to Polylang Database</h2>\n";
        
        // Check if Polylang is active
        if (!function_exists('pll_register_string')) {
            echo "<p style='color: red;'>Error: Polylang plugin is not active or pll_register_string function is not available.</p>\n";
            return;
        }
        
        echo "<p>Processing " . count($this->strings) . " strings...</p>\n";
        echo "<div id='progress-container'>\n";
        
        foreach ($this->strings as $key => $data) {
            $string_id = md5($key);
            
            try {
                // Register string with Polylang
                pll_register_string(
                    $key, // String name (unique identifier)
                    $data['clean'], // String value
                    $data['context'], // Context (theme name)
                    false // Not multiline
                );
                
                $this->stats['strings_added']++;
                $status = "<span style='color: green;'>✓ Added</span>";
                
            } catch (Exception $e) {
                $this->stats['errors']++;
                $status = "<span style='color: red;'>✗ Error: " . esc_html($e->getMessage()) . "</span>";
            }
            
            // Update status in real-time using JavaScript
            echo "<script>
                if (document.getElementById('status-{$string_id}')) {
                    document.getElementById('status-{$string_id}').innerHTML = '{$status}';
                }
            </script>\n";
            
            // Flush output for real-time updates
            if (ob_get_level()) {
                ob_flush();
            }
            flush();
            
            // Small delay to prevent overwhelming the server
            usleep(10000); // 0.01 seconds
        }
        
        echo "</div>\n";
        echo "<p><strong>String registration completed!</strong></p>\n";
    }
    
    /**
     * Display final statistics
     */
    private function displayStatistics() {
        echo "<h2>Final Statistics</h2>\n";
        echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse;'>\n";
        echo "<tr><td><strong>Files Scanned:</strong></td><td>{$this->stats['files_scanned']}</td></tr>\n";
        echo "<tr><td><strong>Strings Found:</strong></td><td>{$this->stats['strings_found']}</td></tr>\n";
        echo "<tr><td><strong>Strings Added:</strong></td><td style='color: green;'>{$this->stats['strings_added']}</td></tr>\n";
        echo "<tr><td><strong>Errors:</strong></td><td style='color: red;'>{$this->stats['errors']}</td></tr>\n";
        echo "</table>\n";

        if ($this->stats['strings_added'] > 0) {
            echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
            echo "<h3>✅ Success!</h3>\n";
            echo "<p>All strings have been registered with Polylang. You can now:</p>\n";
            echo "<ul>\n";
            echo "<li>Go to <strong>Languages > String translations</strong> in WordPress admin</li>\n";
            echo "<li>Find your strings and add English/Russian translations</li>\n";
            echo "<li>The strings will be automatically available on your website</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        }

        echo "<h3>Next Steps:</h3>\n";
        echo "<ol>\n";
        echo "<li>Go to WordPress Admin → Languages → String translations</li>\n";
        echo "<li>Look for strings with context 'alachiqgroup-pro'</li>\n";
        echo "<li>Add English and Russian translations for each string</li>\n";
        echo "<li>Test your website in different languages</li>\n";
        echo "</ol>\n";

        echo "<h3>Optional: Convert WordPress Functions to Polylang</h3>\n";
        echo "<p>If you want to convert _e() and __() functions to pll_e_safe() and pll___safe():</p>\n";
        echo "<p><a href='?run=convert' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Convert Functions</a></p>\n";
    }

    /**
     * Convert WordPress translation functions to Polylang safe functions
     */
    public function convertFunctions() {
        echo "<h1>Converting WordPress Translation Functions to Polylang</h1>\n";
        echo "<p>Converting _e() to pll_e_safe() and __() to pll___safe()...</p>\n";

        $php_files = $this->getPhpFiles($this->theme_path);
        $converted_files = 0;
        $total_replacements = 0;

        foreach ($php_files as $file) {
            $content = file_get_contents($file);
            $original_content = $content;
            $file_replacements = 0;

            // Convert _e() to pll_e_safe()
            $content = preg_replace(
                '/_e\s*\(\s*([\'"][^\'\"]+[\'"])\s*,\s*[\'"]alachiqgroup-pro[\'"]\s*\)/',
                'pll_e_safe($1)',
                $content,
                -1,
                $count1
            );
            $file_replacements += $count1;

            // Convert __() to pll___safe()
            $content = preg_replace(
                '/__\s*\(\s*([\'"][^\'\"]+[\'"])\s*,\s*[\'"]alachiqgroup-pro[\'"]\s*\)/',
                'pll___safe($1)',
                $content,
                -1,
                $count2
            );
            $file_replacements += $count2;

            // Convert esc_html_e() to pll_esc_html_e_safe()
            $content = preg_replace(
                '/esc_html_e\s*\(\s*([\'"][^\'\"]+[\'"])\s*,\s*[\'"]alachiqgroup-pro[\'"]\s*\)/',
                'echo esc_html(pll___safe($1))',
                $content,
                -1,
                $count3
            );
            $file_replacements += $count3;

            if ($file_replacements > 0) {
                file_put_contents($file, $content);
                $converted_files++;
                $total_replacements += $file_replacements;

                $relative_path = str_replace($this->theme_path . '/', '', $file);
                echo "<p>✓ Converted {$file_replacements} functions in: {$relative_path}</p>\n";
            }
        }

        echo "<h2>Conversion Complete</h2>\n";
        echo "<p><strong>Files modified:</strong> {$converted_files}</p>\n";
        echo "<p><strong>Total replacements:</strong> {$total_replacements}</p>\n";

        if ($total_replacements > 0) {
            echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
            echo "<h3>✅ Conversion Successful!</h3>\n";
            echo "<p>All WordPress translation functions have been converted to Polylang safe functions.</p>\n";
            echo "<p>Your theme now uses consistent Polylang functions throughout.</p>\n";
            echo "</div>\n";
        }
    }
}

// Execute the script
$extractor = new StringExtractor();

if (isset($_GET['run']) && $_GET['run'] === 'true') {
    $extractor->run();
} elseif (isset($_GET['run']) && $_GET['run'] === 'convert') {
    $extractor->convertFunctions();
} else {
    echo "<h1>Alachiq Group Pro - String Extractor & Converter</h1>\n";
    echo "<p>This script will scan all PHP files in your theme and extract translatable strings.</p>\n";
    echo "<p><strong>What it does:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Scans all PHP files for _e(), __(), pll_e_safe(), pll___safe() functions</li>\n";
    echo "<li>Extracts the translatable strings</li>\n";
    echo "<li>Registers them with Polylang for translation</li>\n";
    echo "<li>Optionally converts WordPress functions to Polylang functions</li>\n";
    echo "</ul>\n";
    echo "<div style='margin: 20px 0;'>\n";
    echo "<a href='?run=true' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>Extract & Register Strings</a>\n";
    echo "<a href='?run=convert' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Convert Functions Only</a>\n";
    echo "</div>\n";
    echo "<p><strong>Recommended workflow:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>First run 'Extract & Register Strings' to add all strings to Polylang</li>\n";
    echo "<li>Then optionally run 'Convert Functions Only' to standardize your code</li>\n";
    echo "</ol>\n";
}
?>

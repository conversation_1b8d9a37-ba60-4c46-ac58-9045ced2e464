<?php get_header(); ?>

<main id="main-content" class="single-product-page">
    <?php while (have_posts()) : the_post(); ?>
        
        <!-- Product Hero Section -->
        <section class="product-hero">
            <div class="container">
                <div class="product-hero__content">
                    <div class="product-hero__breadcrumb">
                        <a href="<?php echo esc_url(home_url('/decorations/')); ?>">← <span><?php pll_e_safe('Dekorasiya Məhsulları'); ?></span></a>
                    </div>
                    
                    <div class="product-hero__grid">
                        <!-- Product Images -->
                        <div class="product-images">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="product-main-image">
                                    <a href="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'full')); ?>" 
                                       class="main-image-lightbox" 
                                       data-lightbox="product-main"
                                       title="<?php echo esc_attr(get_the_title()); ?>">
                                        <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                                        <div class="main-image-overlay">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $product_gallery = get_post_meta(get_the_ID(), '_product_gallery', true);
                            if ($product_gallery && is_array($product_gallery)) : ?>
                                <div class="product-gallery">
                                    <?php foreach ($product_gallery as $image_id) : 
                                        $image_url = wp_get_attachment_image_url($image_id, 'medium');
                                        $image_full = wp_get_attachment_image_url($image_id, 'large');
                                        $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                                        if ($image_url) : ?>
                                            <div class="gallery-thumbnail">
                                                <a href="<?php echo esc_url($image_full); ?>" 
                                                   class="gallery-lightbox" 
                                                   data-lightbox="product-gallery"
                                                   title="<?php echo esc_attr($image_alt ? $image_alt : get_the_title()); ?>">
                                                    <img src="<?php echo esc_url($image_url); ?>" 
                                                         alt="<?php echo esc_attr($image_alt ? $image_alt : get_the_title()); ?>" />
                                                    <div class="gallery-overlay">
                                                        <i class="fas fa-search-plus"></i>
                                                    </div>
                                                </a>
                                            </div>
                                        <?php endif;
                                    endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Product Info -->
                        <div class="product-info">
                            <?php
                            $product_categories = get_the_terms(get_the_ID(), 'product_category');
                            if ($product_categories && !is_wp_error($product_categories)) : ?>
                                <div class="product-category">
                                    <?php echo esc_html($product_categories[0]->name); ?>
                                </div>
                            <?php endif; ?>
                            
                            <h1 class="product-title"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title(get_the_ID())); ?></h1>
                            
                            <div class="product-description">
                                <?php
                                $translated_content = alachiqgroup_pro_get_translated_post_content(get_the_ID(), 'content');
                                if (!empty($translated_content)) {
                                    echo wp_kses_post($translated_content);
                                } else {
                                    the_content();
                                }
                                ?>
                            </div>
                            
                            <?php
                            $product_price = get_post_meta(get_the_ID(), '_product_price', true);
                            $product_currency = get_post_meta(get_the_ID(), '_product_currency', true) ?: 'AZN';
                            $product_duration = get_post_meta(get_the_ID(), '_product_duration', true);
                            ?>
                            
                            <div class="product-meta">
                                <?php if ($product_price) : ?>
                                    <div class="product-price">
                                        <span class="price-label"><?php pll_e_safe('Qiymət:'); ?></span>
                                        <span class="price-amount"><?php echo esc_html($product_price) . ' ' . esc_html($product_currency); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($product_duration) : ?>
                                    <div class="product-duration">
                                        <span class="duration-label"><?php pll_e_safe('Müddət:'); ?></span>
                                        <span class="duration-amount"><?php echo esc_html($product_duration); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php
                            $product_features = get_post_meta(get_the_ID(), '_product_features', true);
                            if ($product_features) : ?>
                                <div class="product-features">
                                    <h3><?php pll_e_safe('Xüsusiyyətlər'); ?></h3>
                                    <ul>
                                        <?php
                                        $features = explode("\n", $product_features);
                                        foreach ($features as $feature) {
                                            $feature = trim($feature);
                                            if (!empty($feature)) {
                                                echo '<li>' . esc_html($feature) . '</li>';
                                            }
                                        }
                                        ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="product-actions">
                                <a href="tel:<?php echo esc_attr(get_theme_mod('company_phone', '(+994 12) 514 39 06')); ?>" class="btn-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;">
                                        <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                                    </svg>
                                    <span><?php pll_e_safe('Zəng Et'); ?></span>
                                </a>
                                <a href="#" class="btn-secondary whatsapp-link" target="_blank" data-product-title="<?php echo esc_attr(get_the_title()); ?>">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                    </svg>
                                    <span><?php pll_e_safe('WhatsApp'); ?></span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Related Products -->
        <section class="related-products">
            <div class="container">
                <h2><?php pll_e_safe('Əlaqəli Məhsullar'); ?></h2>
                <div class="related-products-grid">
                    <?php
                    $related_products = new WP_Query(array(
                        'post_type' => 'decoration_products',
                        'posts_per_page' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'orderby' => 'rand'
                    ));
                    
                    if ($related_products->have_posts()) {
                        while ($related_products->have_posts()) {
                            $related_products->the_post();
                            ?>
                            <article class="related-product-card">
                                <div class="related-product-image">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('medium', array('alt' => get_the_title())); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="related-product-content">
                                    <h3><a href="<?php the_permalink(); ?>"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title(get_the_ID())); ?></a></h3>
                                    <p><?php
                                    $translated_excerpt = alachiqgroup_pro_get_translated_post_content(get_the_ID(), 'excerpt');
                                    if (!empty($translated_excerpt)) {
                                        echo wp_trim_words($translated_excerpt, 15);
                                    } else {
                                        echo wp_trim_words(get_the_excerpt(), 15);
                                    }
                                    ?></p>
                                </div>
                            </article>
                            <?php
                        }
                        wp_reset_postdata();
                    }
                    ?>
                </div>
            </div>
        </section>
        
    <?php endwhile; ?>
</main>

<style>
/* REMOVE ALL DEBUG COLORS - FORCE CLEAN STYLING */
body {
    border: none !important;
    background: white !important;
}

.product-actions {
    border: none !important;
    background: transparent !important;
    outline: none !important;
    box-shadow: none !important;
}

/* Single Product Page Styles */
.single-product-page {
    background: var(--color-background);
    min-height: 100vh;
}

.product-hero {
    padding: var(--space-4xl) 0;
    background: var(--color-surface);
}

.product-hero__breadcrumb {
    margin-bottom: var(--space-xl);
}

.product-hero__breadcrumb a {
    color: var(--alachiq-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-base);
}

.product-hero__breadcrumb a:hover {
    color: var(--alachiq-secondary);
}

.product-hero__grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: start;
}

.product-images {
    position: sticky;
    top: var(--space-xl);
}

.product-main-image {
    margin-bottom: var(--space-lg);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.product-main-image img {
    width: 100%;
    height: auto;
    display: block;
}

.product-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: var(--space-sm);
}

.gallery-thumbnail {
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    transition: transform var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.gallery-thumbnail:hover {
    transform: scale(1.05);
}

.gallery-thumbnail {
    position: relative;
    cursor: pointer;
}

.gallery-thumbnail img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(64, 49, 36, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-base);
}

.gallery-thumbnail:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: white;
    font-size: 1.2rem;
}

/* Main image lightbox styles */
.product-main-image {
    position: relative;
    cursor: pointer;
}

.main-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(64, 49, 36, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-base);
}

.product-main-image:hover .main-image-overlay {
    opacity: 1;
}

.main-image-overlay i {
    color: white;
    font-size: 2rem;
}

.product-info {
    padding: var(--space-lg);
}

.product-category {
    color: var(--alachiq-primary);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-sm);
}

.product-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--space-lg);
    line-height: 1.2;
}

.product-description {
    color: var(--color-text-secondary);
    font-size: var(--font-size-base);
    line-height: 1.7;
    margin-bottom: var(--space-xl);
}

.product-meta {
    display: flex;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: rgba(217, 196, 156, 0.1);
    border-radius: var(--radius-lg);
}

.product-price,
.product-duration {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.price-label,
.duration-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    font-weight: 500;
}

.price-amount {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--alachiq-primary);
}

.duration-amount {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text-primary);
}

.product-features {
    margin-bottom: var(--space-xl);
}

.product-features h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--space-md);
}

.product-features ul {
    list-style: none;
    padding: 0;
}

.product-features li {
    display: flex;
    align-items: center;
    font-size: var(--font-size-base);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-sm);
    padding: var(--space-sm) 0;
    border-bottom: 1px solid rgba(217, 196, 156, 0.2);
}

.product-features li:last-child {
    border-bottom: none;
}

.product-features li::before {
    content: '✓';
    color: var(--alachiq-primary);
    font-weight: bold;
    margin-right: var(--space-md);
    font-size: var(--font-size-lg);
}

.product-actions {
    display: flex !important;
    gap: 1rem !important;
    flex-wrap: wrap !important;
    border: none !important;
    background: transparent !important;
    outline: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 1rem 0 !important;
}

/* OVERRIDE COMPONENTS.CSS - MAXIMUM SPECIFICITY */
.single-product-page .product-hero .product-info .product-actions .btn-primary,
.single-product-page .product-hero .product-info .product-actions .btn-secondary,
.single-product-page .product-actions a.btn-primary,
.single-product-page .product-actions a.btn-secondary,
.product-hero .product-info .product-actions .btn-primary,
.product-hero .product-info .product-actions .btn-secondary {
    display: inline-flex !important;
    align-items: center !important;
    padding: 1rem 1.5rem !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    font-size: 0.9rem !important;
    border: 1px solid transparent !important;
    /* FORCE OVERRIDE COMPONENTS.CSS */
    background: none !important;
    color: inherit !important;
    box-shadow: none !important;
    filter: none !important;
    backdrop-filter: none !important;
}

/* BEAUTIFUL PRIMARY BUTTON - MAXIMUM SPECIFICITY TO OVERRIDE COMPONENTS.CSS */
.single-product-page .product-hero .product-info .product-actions .btn-primary,
.single-product-page .product-actions a.btn-primary,
.product-hero .product-info .product-actions .btn-primary {
    background: linear-gradient(135deg, #d9c49c 0%, #d9b79a 100%) !important;
    color: #403124 !important;
    flex: 1 !important;
    min-width: 150px !important;
    justify-content: center !important;
    border: 1px solid #d9c49c !important;
    box-shadow: 0 2px 8px rgba(217, 196, 156, 0.3) !important;
    /* FORCE OVERRIDE COMPONENTS.CSS PROPERTIES */
    filter: none !important;
    backdrop-filter: none !important;
}

.single-product-page .product-hero .product-info .product-actions .btn-primary:hover,
.single-product-page .product-actions a.btn-primary:hover,
.product-hero .product-info .product-actions .btn-primary:hover {
    background: linear-gradient(135deg, #403124 0%, #736356 100%) !important;
    color: #f2ece4 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(64, 49, 36, 0.4) !important;
    /* FORCE OVERRIDE COMPONENTS.CSS HOVER PROPERTIES */
    filter: none !important;
    text-decoration: none !important;
}

/* BEAUTIFUL SECONDARY BUTTON - MAXIMUM SPECIFICITY TO OVERRIDE COMPONENTS.CSS */
.single-product-page .product-hero .product-info .product-actions .btn-secondary,
.single-product-page .product-actions a.btn-secondary,
.product-hero .product-info .product-actions .btn-secondary {
    background: #f2ece4 !important;
    color: #403124 !important;
    border: 1px solid #736356 !important;
    min-width: 150px !important;
    justify-content: center !important;
    box-shadow: 0 2px 6px rgba(115, 99, 86, 0.2) !important;
    /* FORCE OVERRIDE COMPONENTS.CSS PROPERTIES */
    border-color: #736356 !important;
    backdrop-filter: none !important;
    font-weight: 600 !important;
}

.single-product-page .product-hero .product-info .product-actions .btn-secondary:hover,
.single-product-page .product-actions a.btn-secondary:hover,
.product-hero .product-info .product-actions .btn-secondary:hover {
    background: linear-gradient(135deg, #736356 0%, #403124 100%) !important;
    color: #f2ece4 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(115, 99, 86, 0.4) !important;
    /* FORCE OVERRIDE COMPONENTS.CSS HOVER PROPERTIES */
    text-decoration: none !important;
}

.related-products {
    padding: var(--space-4xl) 0;
    background: var(--color-background);
}

.related-products h2 {
    text-align: center;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--space-3xl);
}

.related-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
}

.related-product-card {
    background: var(--color-surface);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
}

.related-product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.related-product-image {
    height: 200px;
    overflow: hidden;
}

.related-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-base);
}

.related-product-card:hover .related-product-image img {
    transform: scale(1.05);
}

.related-product-content {
    padding: var(--space-lg);
}

.related-product-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--space-sm);
}

.related-product-content h3 a {
    color: inherit;
    text-decoration: none;
}

.related-product-content h3 a:hover {
    color: var(--alachiq-primary);
}

.related-product-content p {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-hero__grid {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }
    
    .product-images {
        position: static;
    }
    
    .product-meta {
        flex-direction: column;
        gap: var(--space-lg);
    }
    
    .product-actions {
        flex-direction: column;
    }
    
    .related-products-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .product-title {
        font-size: var(--font-size-2xl);
    }
    
    .product-meta {
        padding: var(--space-md);
    }
    
    .product-gallery {
        grid-template-columns: repeat(4, 1fr);
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Gallery thumbnail click
    $('.gallery-thumbnail').on('click', function() {
        var fullImage = $(this).find('img').data('full');
        var mainImage = $('.product-main-image img');
        
        // Swap images
        mainImage.attr('src', fullImage);
        
        // Update active state
        $('.gallery-thumbnail').removeClass('active');
        $(this).addClass('active');
    });
    
    // Set first thumbnail as active
    $('.gallery-thumbnail:first').addClass('active');
});
</script>

<?php get_footer(); ?> 
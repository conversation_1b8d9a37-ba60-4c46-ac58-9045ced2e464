<?php get_header(); ?>

<?php while (have_posts()) : the_post(); ?>

<main class="single-project-modern">
    <!-- E-commerce Style Hero Section -->
    <section class="project-hero-modern">
        <div class="container">
            <!-- Modern Breadcrumb -->
            <nav class="project-breadcrumb-modern">
                <div class="breadcrumb-container">
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        <span><?php pll_e_safe('Ana səhifə'); ?></span>
                    </a>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    <a href="<?php echo esc_url(get_post_type_archive_link('projects')); ?>" class="breadcrumb-item">
                        <i class="fas fa-building"></i>
                        <span><?php pll_e_safe('Layihələr'); ?></span>
                    </a>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    <span class="breadcrumb-current"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title(get_the_ID())); ?></span>
                </div>
            </nav>

            <div class="project-hero-modern__grid">
                <!-- Project Images -->
                <div class="project-hero-modern__images">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="project-main-image">
                            <a href="<?php echo esc_url(get_the_post_thumbnail_url(get_the_ID(), 'full')); ?>" 
                               class="project-main-image__link" 
                               data-lightbox="project-main"
                               title="<?php echo esc_attr(get_the_title()); ?>">
                                <?php the_post_thumbnail('large', array('class' => 'project-main-image__img')); ?>
                                <div class="project-main-image__overlay">
                                    <i class="fas fa-search-plus"></i>
                                </div>
                            </a>
                        </div>
                    <?php else : ?>
                        <div class="project-main-image project-main-image--placeholder">
                            <i class="fas fa-building"></i>
                            <span><?php pll_e_safe('Layihə Şəkli'); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Thumbnail Gallery -->
                    <?php
                    $gallery_images = get_post_meta(get_the_ID(), '_project_gallery', true);
                    if (empty($gallery_images)) {
                        $gallery_images = get_post_meta(get_the_ID(), 'project_gallery_backup', true);
                    }
                    if (empty($gallery_images)) {
                        $gallery_images = get_post_meta(get_the_ID(), '_gallery_test', true);
                    }
                    
                    // Ensure it's always an array
                    if (!is_array($gallery_images)) {
                        if (!empty($gallery_images)) {
                            if (is_string($gallery_images)) {
                                $gallery_images = explode(',', $gallery_images);
                                $gallery_images = array_filter(array_map('trim', $gallery_images));
                            } else {
                                $gallery_images = maybe_unserialize($gallery_images);
                                if (!is_array($gallery_images)) {
                                    $gallery_images = array();
                                }
                            }
                        } else {
                            $gallery_images = array();
                        }
                    }
                    
                    if ($gallery_images && is_array($gallery_images) && count($gallery_images) > 0) :
                        $thumbnail_count = min(4, count($gallery_images));
                    ?>
                    <div class="project-thumbnails">
                        <?php for ($i = 0; $i < $thumbnail_count; $i++) :
                            $image_id = $gallery_images[$i];
                            $image_url = wp_get_attachment_image_url($image_id, 'large');
                            $image_thumb = wp_get_attachment_image_url($image_id, 'thumbnail');
                            $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                            if ($image_url) : ?>
                        <div class="project-thumbnail">
                            <a href="<?php echo esc_url($image_url); ?>" 
                               class="project-thumbnail__link" 
                               data-lightbox="project-gallery"
                               title="<?php echo esc_attr($image_alt ? $image_alt : get_the_title()); ?>">
                                <img src="<?php echo esc_url($image_thumb); ?>" 
                                     alt="<?php echo esc_attr($image_alt ? $image_alt : get_the_title()); ?>" 
                                     class="project-thumbnail__img">
                                <div class="project-thumbnail__overlay">
                                    <i class="fas fa-search-plus"></i>
                                </div>
                            </a>
                        </div>
                        <?php endif; endfor; ?>
                        
                        <?php if (count($gallery_images) > 4) : ?>
                        <div class="project-thumbnail project-thumbnail--more">
                            <span class="project-thumbnail__count">+<?php echo count($gallery_images) - 4; ?></span>
                            <span class="project-thumbnail__text"><?php pll_e_safe('Daha çox'); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Project Info -->
                <div class="project-hero-modern__info">
                    <div class="project-info-card">
                        <!-- Project Category Badge -->
                        <?php
                        $project_categories = get_the_terms(get_the_ID(), 'project_category');
                        if ($project_categories && !is_wp_error($project_categories)) : ?>
                        <div class="project-category-badge">
                            <i class="fas fa-tag"></i>
                            <span><?php echo esc_html($project_categories[0]->name); ?></span>
                        </div>
                        <?php endif; ?>

                        <!-- Project Title -->
                        <h1 class="project-info-card__title"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title(get_the_ID())); ?></h1>

                        <!-- Project Excerpt -->
                        <?php
                        $translated_excerpt = alachiqgroup_pro_get_translated_post_content(get_the_ID(), 'excerpt');
                        if (!empty($translated_excerpt)) : ?>
                        <div class="project-info-card__excerpt">
                            <?php echo wp_kses_post($translated_excerpt); ?>
                        </div>
                        <?php endif; ?>

                        <!-- Project Stats -->
                        <div class="project-stats">
                            <?php
                            $project_area = get_post_meta(get_the_ID(), '_project_area', true);
                            $project_floors = get_post_meta(get_the_ID(), '_project_floors', true);
                            $project_year = get_post_meta(get_the_ID(), '_project_year', true);
                            $project_location = get_post_meta(get_the_ID(), '_project_location', true);
                            ?>
                            
                            <?php if ($project_area) : ?>
                            <div class="project-stat">
                                <div class="project-stat__icon">
                                    <i class="fas fa-ruler-combined"></i>
                                </div>
                                <div class="project-stat__content">
                                    <span class="project-stat__value"><?php echo esc_html($project_area); ?> m²</span>
                                    <span class="project-stat__label"><?php pll_e_safe('Sahə'); ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($project_floors) : ?>
                            <div class="project-stat">
                                <div class="project-stat__icon">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <div class="project-stat__content">
                                    <span class="project-stat__value"><?php echo esc_html($project_floors); ?></span>
                                    <span class="project-stat__label"><?php pll_e_safe('Mərtəbə'); ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($project_year) : ?>
                            <div class="project-stat">
                                <div class="project-stat__icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="project-stat__content">
                                    <span class="project-stat__value"><?php echo esc_html($project_year); ?></span>
                                    <span class="project-stat__label"><?php pll_e_safe('İl'); ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($project_location) : ?>
                            <div class="project-stat">
                                <div class="project-stat__icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="project-stat__content">
                                    <span class="project-stat__value"><?php echo esc_html($project_location); ?></span>
                                    <span class="project-stat__label"><?php pll_e_safe('Yer'); ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Project Actions -->
                        <div class="project-actions-modern">
                            <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="btn-primary-modern">
                                <i class="fas fa-envelope"></i>
                                <span><?php pll_e_safe('Layihə Başlat'); ?></span>
                            </a>
                            <a href="tel:<?php echo esc_attr(get_theme_mod('company_phone', '(+994 12) 514 39 06')); ?>" class="btn-secondary-modern">
                                <i class="fas fa-phone"></i>
                                <span><?php pll_e_safe('Zəng Et'); ?></span>
                            </a>
                            <button class="btn-outline-modern project-share-btn">
                                <i class="fas fa-share-alt"></i>
                                <span><?php pll_e_safe('Paylaş'); ?></span>
                            </button>
                        </div>

                        <!-- Project Meta -->
                        <div class="project-meta-modern">
                            <div class="project-meta-item">
                                <i class="fas fa-eye"></i>
                                <span><?php pll_e_safe('Baxış:'); ?> <?php echo get_post_views(get_the_ID()); ?></span>
                            </div>
                            <div class="project-meta-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo get_the_date(); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Content Tabs -->
    <section class="project-content-modern">
        <div class="container">
            <div class="project-tabs">
                <div class="project-tabs__nav">
                    <button class="project-tab-btn active" data-tab="description">
                        <i class="fas fa-file-alt"></i>
                        <span><?php pll_e_safe('Təsvir'); ?></span>
                    </button>
                    <button class="project-tab-btn" data-tab="details">
                        <i class="fas fa-info-circle"></i>
                        <span><?php pll_e_safe('Detallar'); ?></span>
                    </button>
                    <button class="project-tab-btn" data-tab="gallery">
                        <i class="fas fa-images"></i>
                        <span><?php pll_e_safe('Qalereya'); ?></span>
                    </button>
                </div>

                <div class="project-tabs__content">
                    <!-- Description Tab -->
                    <div class="project-tab-content active" id="description">
                        <div class="project-description-modern">
                            <?php
                            $translated_content = alachiqgroup_pro_get_translated_post_content(get_the_ID(), 'content');
                            if (!empty($translated_content)) {
                                echo wp_kses_post($translated_content);
                            } else {
                                the_content();
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Details Tab -->
                    <div class="project-tab-content" id="details">
                        <div class="project-details-modern">
                            <div class="project-details-grid">
                                <?php
                                $project_type = get_post_meta(get_the_ID(), '_project_type', true);
                                $project_duration = get_post_meta(get_the_ID(), '_project_duration', true);
                                $project_budget = get_post_meta(get_the_ID(), '_project_budget', true);
                                ?>
                                
                                <div class="project-detail-card">
                                    <div class="project-detail-card__icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="project-detail-card__content">
                                        <h4><?php pll_e_safe('Layihə Növü'); ?></h4>
                                        <p><?php echo $project_type ? esc_html($project_type) : pll___safe('Müəyyən edilməyib'); ?></p>
                                    </div>
                                </div>

                                <div class="project-detail-card">
                                    <div class="project-detail-card__icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="project-detail-card__content">
                                        <h4><?php pll_e_safe('Müddət'); ?></h4>
                                        <p><?php echo $project_duration ? esc_html($project_duration) : pll___safe('Müəyyən edilməyib'); ?></p>
                                    </div>
                                </div>

                                <div class="project-detail-card">
                                    <div class="project-detail-card__icon">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div class="project-detail-card__content">
                                        <h4><?php pll_e_safe('Büdcə'); ?></h4>
                                        <p><?php echo $project_budget ? esc_html($project_budget) : pll___safe('Müəyyən edilməyib'); ?></p>
                                    </div>
                                </div>

                                <div class="project-detail-card">
                                    <div class="project-detail-card__icon">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    <div class="project-detail-card__content">
                                        <h4><?php pll_e_safe('Tamamlanma Tarixi'); ?></h4>
                                        <p><?php echo get_the_date(); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Gallery Tab -->
                    <div class="project-tab-content" id="gallery">
                        <?php 
                        // Re-process gallery images for the gallery tab
                        $gallery_images_tab = get_post_meta(get_the_ID(), '_project_gallery', true);
                        if (empty($gallery_images_tab)) {
                            $gallery_images_tab = get_post_meta(get_the_ID(), 'project_gallery_backup', true);
                        }
                        if (empty($gallery_images_tab)) {
                            $gallery_images_tab = get_post_meta(get_the_ID(), '_gallery_test', true);
                        }
                        
                        // Ensure it's always an array
                        if (!is_array($gallery_images_tab)) {
                            if (!empty($gallery_images_tab)) {
                                if (is_string($gallery_images_tab)) {
                                    $gallery_images_tab = explode(',', $gallery_images_tab);
                                    $gallery_images_tab = array_filter(array_map('trim', $gallery_images_tab));
                                } else {
                                    $gallery_images_tab = maybe_unserialize($gallery_images_tab);
                                    if (!is_array($gallery_images_tab)) {
                                        $gallery_images_tab = array();
                                    }
                                }
                            } else {
                                $gallery_images_tab = array();
                            }
                        }
                        
                        if ($gallery_images_tab && is_array($gallery_images_tab) && count($gallery_images_tab) > 0) : ?>
                        <div class="project-gallery-modern">
                            <div class="project-gallery-grid">
                                <?php foreach ($gallery_images_tab as $image_id) :
                                    $image_url = wp_get_attachment_image_url($image_id, 'large');
                                    $image_thumb = wp_get_attachment_image_url($image_id, 'medium');
                                    $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                                    if ($image_url) : ?>
                                <div class="project-gallery-item">
                                    <a href="<?php echo esc_url($image_url); ?>" 
                                       class="project-gallery-link" 
                                       data-lightbox="project-gallery"
                                       title="<?php echo esc_attr($image_alt ? $image_alt : get_the_title()); ?>">
                                        <img src="<?php echo esc_url($image_thumb); ?>" 
                                             alt="<?php echo esc_attr($image_alt ? $image_alt : get_the_title()); ?>" 
                                             class="project-gallery-image">
                                        <div class="project-gallery-overlay">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                    </a>
                                </div>
                                <?php endif; endforeach; ?>
                            </div>
                        </div>
                        <?php else : ?>
                        <div class="project-gallery-empty">
                            <i class="fas fa-images"></i>
                            <h3><?php pll_e_safe('Qalereya Şəkilləri Yoxdur'); ?></h3>
                            <p><?php pll_e_safe('Bu layihə üçün hələ qalereya şəkilləri əlavə edilməyib.'); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Project Navigation -->
    <section class="project-navigation-modern">
        <div class="container">
            <div class="project-navigation-modern__content">
                <?php
                $prev_post = get_previous_post(false, '', 'project_category');
                $next_post = get_next_post(false, '', 'project_category');
                ?>
                
                <?php if ($prev_post) : ?>
                <a href="<?php echo get_permalink($prev_post->ID); ?>" class="project-nav-modern project-nav-modern--prev">
                    <div class="project-nav-modern__icon">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                    <div class="project-nav-modern__content">
                        <span class="project-nav-modern__label"><?php pll_e_safe('Əvvəlki Layihə'); ?></span>
                        <span class="project-nav-modern__title"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title($prev_post->ID)); ?></span>
                    </div>
                </a>
                <?php endif; ?>
                
                <?php if ($next_post) : ?>
                <a href="<?php echo get_permalink($next_post->ID); ?>" class="project-nav-modern project-nav-modern--next">
                    <div class="project-nav-modern__content">
                        <span class="project-nav-modern__label"><?php pll_e_safe('Növbəti Layihə'); ?></span>
                        <span class="project-nav-modern__title"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title($next_post->ID)); ?></span>
                    </div>
                    <div class="project-nav-modern__icon">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </section>
</main>

<?php endwhile; ?>

<?php get_footer(); ?>
# String Extraction and Polylang Integration Guide

## Overview
This guide explains how to use the `string-extractor.php` script to identify all translatable strings in your WordPress theme and add them to the Polylang database for translation.

## What the Script Does

### 1. String Detection
The script scans all PHP files in your theme and identifies strings using these functions:
- `_e('String', 'alachiqgroup-pro')` - WordPress echo translation
- `__('String', 'alachiqgroup-pro')` - WordPress return translation  
- `pll_e_safe('String')` - Polylang safe echo
- `pll___safe('String')` - Polylang safe return
- `pll_esc_html___safe('String')` - Polylang safe HTML escape
- `pll_esc_attr___safe('String')` - Polylang safe attribute escape

### 2. String Registration
Automatically registers all found strings with Polylang using `pll_register_string()` function.

### 3. Function Conversion (Optional)
Converts WordPress translation functions to Polylang equivalents:
- `_e()` → `pll_e_safe()`
- `__()` → `pll___safe()`
- `esc_html_e()` → `echo esc_html(pll___safe())`

## How to Use

### Step 1: Access the Script
Navigate to: `http://your-site.com/wp-content/themes/alachiqgroup-pro/string-extractor.php`

### Step 2: Extract and Register Strings
1. Click "Extract & Register Strings"
2. The script will:
   - Scan all PHP files in your theme
   - Display a table of found strings
   - Register each string with Polylang
   - Show real-time progress updates

### Step 3: Add Translations
1. Go to WordPress Admin → Languages → String translations
2. Look for strings with context "alachiqgroup-pro"
3. Add English and Russian translations for each string
4. Save your translations

### Step 4: Optional Function Conversion
If you want to standardize your code:
1. Click "Convert Functions Only"
2. This will replace all `_e()` and `__()` with `pll_e_safe()` and `pll___safe()`

## Expected Results

### Before Running Script
```php
<?php _e('Ana səhifə', 'alachiqgroup-pro'); ?>
<?php echo __('Layihələr', 'alachiqgroup-pro'); ?>
```

### After Function Conversion
```php
<?php pll_e_safe('Ana səhifə'); ?>
<?php echo pll___safe('Layihələr'); ?>
```

### In Polylang String Translations
You'll see entries like:
- **String**: "Ana səhifə" 
- **Context**: "alachiqgroup-pro"
- **English**: "Home"
- **Russian**: "Главная"

## Troubleshooting

### Common Issues

1. **"Polylang plugin is not active"**
   - Make sure Polylang plugin is installed and activated
   - Check that `pll_register_string` function exists

2. **Strings not appearing in translations**
   - Ensure strings are registered with correct context
   - Check Languages → String translations in admin
   - Look for context "alachiqgroup-pro"

3. **Permission errors**
   - Make sure PHP has write permissions to theme files
   - Check file ownership and permissions

### Verification Steps

1. **Check Registration Success**
   - Go to Languages → String translations
   - Search for "alachiqgroup-pro" context
   - Verify all strings are listed

2. **Test Translations**
   - Add test translations for a few strings
   - Switch language on frontend
   - Verify translations appear correctly

## File Structure

```
wp-content/themes/alachiqgroup-pro/
├── string-extractor.php          # Main extraction script
├── STRING_EXTRACTION_GUIDE.md    # This guide
├── functions.php                 # Contains Polylang helper functions
├── header.php                    # Contains navigation strings
├── footer.php                    # Contains footer strings
├── front-page.php               # Contains homepage strings
├── page-about.php               # Contains about page strings
├── page-services.php            # Contains services page strings
├── single-projects.php          # Contains project page strings
└── ... (other template files)
```

## String Categories Found

Based on the scan, your theme contains approximately **756 translatable strings** in categories:

### Navigation & Menu (Header)
- Ana səhifə, Haqqımızda, Xidmətlərimiz, Layihələr, Dekorasiyalar, Əlaqə

### Content Labels
- Sahə, Mərtəbə, Qiymət, Müddət, Xüsusiyyətlər, Qalereya

### Action Buttons  
- Layihə Başlat, Zəng Et, Əlaqə Saxlayın, Ətraflı, Paylaş

### Admin Interface
- Post type labels, taxonomy labels, meta box titles, field descriptions

### Error Messages
- Səhifə tapılmadı, Məhsul tapılmadı, Tərcümə yoxdur

## Next Steps After Extraction

1. **Prioritize Important Strings**
   - Start with navigation and main content
   - Focus on user-facing strings first
   - Admin strings can be translated later

2. **Quality Control**
   - Review translations for consistency
   - Use professional translation services if needed
   - Test all language switches thoroughly

3. **Maintenance**
   - Re-run extraction after adding new features
   - Keep translations updated with content changes
   - Monitor for missing translations

## Support

If you encounter issues:
1. Check WordPress debug.log for errors
2. Verify Polylang plugin compatibility
3. Ensure all helper functions are properly defined in functions.php
4. Test with a small subset of strings first

---

**Note**: Always backup your theme files before running the conversion function, as it modifies your PHP files directly.

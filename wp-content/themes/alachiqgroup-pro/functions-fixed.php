<?php
/**
 * SIMPLIFIED Alachiq Group Pro Theme Functions
 * Fixed version with guaranteed asset loading
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme setup
function alachiqgroup_pro_setup() {
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('menus');
    
    register_nav_menus(array(
        'primary' => pll___safe('Əsas Menyu'),
        'footer' => pll___safe('Footer Menyu'),
    ));
    
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'alachiqgroup_pro_setup');

// SIMPLIFIED ASSET LOADING - GUARANTEED TO WORK
function alachiqgroup_pro_scripts() {
    // External CDN resources first
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css', array(), '5.3.2');
    wp_enqueue_style('fontawesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap', array(), null);
    
    // Theme CSS files - ALWAYS LOAD CRITICAL ONES
    $theme_uri = get_template_directory_uri();
    $theme_dir = get_template_directory();
    
    // Critical CSS files that should always load
    $critical_css = array(
        'components' => '/assets/css/components.css',
        'header' => '/assets/css/header-modern.css',
        'footer' => '/assets/css/footer-modern.css'
    );
    
    foreach ($critical_css as $handle => $file) {
        $file_path = $theme_dir . $file;
        $version = file_exists($file_path) ? filemtime($file_path) : '1.0.0';
        wp_enqueue_style('alachiq-' . $handle, $theme_uri . $file, array('bootstrap'), $version);
    }
    
    // Homepage specific CSS
    if (is_front_page() || is_home()) {
        $homepage_css = array(
            'hero' => '/assets/css/hero.css',
            'services' => '/assets/css/services.css', 
            'projects' => '/assets/css/projects.css',
            'cta' => '/assets/css/cta.css'
        );
        
        foreach ($homepage_css as $handle => $file) {
            $file_path = $theme_dir . $file;
            $version = file_exists($file_path) ? filemtime($file_path) : '1.0.0';
            wp_enqueue_style('alachiq-' . $handle, $theme_uri . $file, array('alachiq-components'), $version);
        }
    }
    
    // Main theme stylesheet
    wp_enqueue_style('alachiq-style', get_stylesheet_uri(), array('alachiq-components'), filemtime($theme_dir . '/style.css'));
    
    // JavaScript
    wp_enqueue_script('jquery');
    wp_enqueue_script('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js', array('jquery'), '5.3.2', true);
    wp_enqueue_script('typed', 'https://cdn.jsdelivr.net/npm/typed.js@2.0.12/lib/typed.min.js', array(), '2.0.12', true);
    
    // Theme JavaScript
    $scripts_js = $theme_dir . '/assets/js/scripts.js';
    if (file_exists($scripts_js)) {
        wp_enqueue_script('alachiq-scripts', $theme_uri . '/assets/js/scripts.js', array('jquery', 'bootstrap'), filemtime($scripts_js), true);
    }
    
    // Homepage JavaScript
    if (is_front_page() || is_home()) {
        $homepage_js = $theme_dir . '/assets/js/homepage.js';
        if (file_exists($homepage_js)) {
            wp_enqueue_script('alachiq-homepage', $theme_uri . '/assets/js/homepage.js', array('jquery'), filemtime($homepage_js), true);
        }
    }
    
    // Localize script for AJAX
    wp_localize_script('alachiq-scripts', 'alachiq_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('alachiq_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'alachiqgroup_pro_scripts');

// Emergency inline styles for immediate loading
function alachiqgroup_pro_emergency_styles() {
    if (is_front_page() || is_home()) {
        echo '<style id="alachiq-emergency-styles">
        /* Emergency fallback styles */
        body { 
            font-family: "Montserrat", sans-serif !important; 
            margin: 0; 
            padding: 0; 
            line-height: 1.6;
        }
        .hero-clean { 
            background: #403124; 
            color: white; 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            padding: 60px 0;
        }
        .alachiq-container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 0 20px; 
        }
        .hero-title { 
            font-size: 3rem; 
            font-weight: 700; 
            margin-bottom: 1rem; 
            color: #D9C49C;
        }
        .hero-text { 
            font-size: 1.2rem; 
            margin-bottom: 2rem; 
            opacity: 0.9; 
        }
        .btn-primary { 
            background: #D9C49C; 
            color: #403124; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 5px; 
            font-weight: 600; 
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: #c5b088;
            transform: translateY(-2px);
        }
        .services-clean { 
            padding: 80px 0; 
            background: #f8f9fa;
        }
        .services-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 30px; 
            margin-top: 50px;
        }
        .service-item { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1); 
            text-align: center; 
            transition: transform 0.3s ease;
        }
        .service-item:hover {
            transform: translateY(-5px);
        }
        .service-icon {
            font-size: 3rem;
            color: #D9C49C;
            margin-bottom: 20px;
        }
        .projects-clean {
            padding: 80px 0;
        }
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        .project-item {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .project-item:hover {
            transform: translateY(-5px);
        }
        </style>' . "\n";
    }
}
add_action('wp_head', 'alachiqgroup_pro_emergency_styles', 1);

// Custom post types
function alachiqgroup_pro_custom_post_types() {
    register_post_type('projects', array(
        'labels' => array(
            'name' => pll___safe('Layihələr'),
            'singular_name' => pll___safe('Layihə'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-building',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'projects'),
    ));
}
add_action('init', 'alachiqgroup_pro_custom_post_types');

// Home page data functions
function alachiqgroup_pro_get_home_services() {
    return array(
        array(
            'title' => pll___safe('Memarlıq və Dizayn'),
            'description' => pll___safe('Müasir memarlıq həlləri və yaradıcı dizayn layihələri. Hər layihə üçün fərdi yanaşma.'),
            'icon' => 'fas fa-drafting-compass',
            'url' => home_url('/services/'),
            'featured' => true,
            'color' => 'primary'
        ),
        array(
            'title' => pll___safe('Tikinti Xidmətləri'),
            'description' => pll___safe('Villa, kottec və kommersiya obyektlərinin tikintisi. Yüksək keyfiyyət və müasir texnologiyalar.'),
            'icon' => 'fas fa-hard-hat',
            'url' => home_url('/services/'),
            'featured' => false,
            'color' => 'secondary'
        ),
        array(
            'title' => pll___safe('Təmir və Yenilənmə'),
            'description' => pll___safe('Keyfiyyətli təmir və yenilənmə işləri. Köhnə binaların müasir standartlara uyğun yenilənməsi.'),
            'icon' => 'fas fa-tools',
            'url' => home_url('/services/'),
            'featured' => false,
            'color' => 'accent'
        ),
        array(
            'title' => pll___safe('İnteryer Dizaynı'),
            'description' => pll___safe('Müasir və funksional interyer həlləri. Yaşayış və iş məkanlarının estetik tərtibatı.'),
            'icon' => 'fas fa-couch',
            'url' => home_url('/services/'),
            'featured' => false,
            'color' => 'neutral-light'
        ),
        array(
            'title' => pll___safe('Hovuz Tikintisi'),
            'description' => pll___safe('Müasir hovuz dizaynı və tikintisi. Fərdi və kommersiya hovuzları üçün tam həllər.'),
            'icon' => 'fas fa-swimming-pool',
            'url' => home_url('/services/'),
            'featured' => false,
            'color' => 'neutral-dark'
        ),
        array(
            'title' => pll___safe('Abadlaşdırma'),
            'description' => pll___safe('Bağ və həyət dizaynı, abadlaşdırma işləri. Yaşıl məkanların yaradılması və tərtibatı.'),
            'icon' => 'fas fa-seedling',
            'url' => home_url('/services/'),
            'featured' => false,
            'color' => 'primary'
        )
    );
}

function alachiqgroup_pro_get_home_projects() {
    $projects = array();
    
    $recent_projects = get_posts(array(
        'post_type' => 'projects',
        'posts_per_page' => 6,
        'post_status' => 'publish'
    ));

    foreach ($recent_projects as $project) {
        $project_image = get_the_post_thumbnail_url($project->ID, 'medium');
        $project_category = get_the_terms($project->ID, 'project_category');
        $category_name = $project_category && !is_wp_error($project_category) ? $project_category[0]->name : pll___safe('Layihə');
        $category_slug = $project_category && !is_wp_error($project_category) ? $project_category[0]->slug : 'project';
        
        $projects[] = array(
            'title' => $project->post_title,
            'excerpt' => wp_trim_words($project->post_excerpt ?: $project->post_content, 20),
            'image' => $project_image ?: get_template_directory_uri() . '/assets/images/placeholder.jpg',
            'url' => get_permalink($project->ID),
            'category' => $category_slug,
            'category_name' => $category_name,
            'location' => get_post_meta($project->ID, '_project_location', true) ?: pll___safe('Bakı')
        );
    }

    if (empty($projects)) {
        $projects = array(
            array(
                'title' => pll___safe('Müasir Villa Layihəsi'),
                'excerpt' => pll___safe('Bakının ən yaxşı məhəlləsində tikilən müasir villa layihəsi.'),
                'image' => get_template_directory_uri() . '/assets/images/placeholder.jpg',
                'url' => home_url('/projects/'),
                'category' => 'villa',
                'category_name' => pll___safe('Villa'),
                'location' => pll___safe('Bakı')
            ),
            array(
                'title' => pll___safe('Kottec Kompleksi'),
                'excerpt' => pll___safe('Yüksək keyfiyyətli kottec kompleksi layihəsi.'),
                'image' => get_template_directory_uri() . '/assets/images/placeholder.jpg',
                'url' => home_url('/projects/'),
                'category' => 'cottage',
                'category_name' => pll___safe('Kottec'),
                'location' => pll___safe('Bakı')
            ),
            array(
                'title' => pll___safe('Kommertsiya Obyekti'),
                'excerpt' => pll___safe('Müasir kommertsiya obyekti tikintisi və dizaynı.'),
                'image' => get_template_directory_uri() . '/assets/images/placeholder.jpg',
                'url' => home_url('/projects/'),
                'category' => 'commercial',
                'category_name' => pll___safe('Kommertsiya'),
                'location' => pll___safe('Bakı')
            )
        );
    }

    return $projects;
}

// Performance optimizations
function alachiqgroup_pro_performance_optimizations() {
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'alachiqgroup_pro_performance_optimizations');

// Debug function for administrators
function alachiq_debug_assets() {
    if (current_user_can('administrator') && isset($_GET['debug_assets'])) {
        add_action('wp_footer', function() {
            global $wp_styles, $wp_scripts;
            echo '<script>console.log("=== ALACHIQ ASSET DEBUG ===");';
            
            if (!empty($wp_styles->queue)) {
                echo 'console.log("Enqueued Styles:");';
                foreach ($wp_styles->queue as $handle) {
                    if (isset($wp_styles->registered[$handle])) {
                        $style = $wp_styles->registered[$handle];
                        echo 'console.log("' . $handle . ': ' . $style->src . '");';
                    }
                }
            }
            
            if (!empty($wp_scripts->queue)) {
                echo 'console.log("Enqueued Scripts:");';
                foreach ($wp_scripts->queue as $handle) {
                    if (isset($wp_scripts->registered[$handle])) {
                        $script = $wp_scripts->registered[$handle];
                        echo 'console.log("' . $handle . ': ' . $script->src . '");';
                    }
                }
            }
            
            echo '</script>';
        });
    }
}
add_action('init', 'alachiq_debug_assets');
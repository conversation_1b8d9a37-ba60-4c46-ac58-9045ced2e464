<?php
/**
 * Automatic Translation System
 * Handles bulk translation of Polylang strings using DeepL and Google Translate APIs
 * 
 * @package Alachiqgroup_Pro
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Prevent class redeclaration
if (!class_exists('Alachiqgroup_Pro_Auto_Translator')) {

class Alachiqgroup_Pro_Auto_Translator {
    
    /**
     * Instance of this class
     */
    private static $instance = null;
    
    /**
     * DeepL API settings
     */
    private $deepl_api_key = '5cb84418-b2d4-427e-b621-9c048b121814:fx';
    private $deepl_api_url = 'https://api-free.deepl.com/v2/translate';
    
    /**
     * Google Translate API settings
     */
    private $google_api_key = '';
    private $google_api_url = 'https://translation.googleapis.com/language/translate/v2';
    
    /**
     * Translation cache
     */
    private $translation_cache = array();
    
    /**
     * Supported languages
     */
    private $supported_languages = array(
        'en' => array(
            'deepl' => 'EN-US',
            'google' => 'en',
            'name' => 'English'
        ),
        'ru' => array(
            'deepl' => 'RU',
            'google' => 'ru', 
            'name' => 'Russian'
        )
    );
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the auto translator
     */
    private function init() {
        // Load API keys from WordPress options
        $this->deepl_api_key = get_option('alachiqgroup_deepl_api_key', '');
        $this->google_api_key = get_option('alachiqgroup_google_api_key', '');
        
        // Add admin menu with higher priority to ensure Polylang is loaded
        add_action('admin_menu', array($this, 'add_admin_menu'), 20);

        // Fallback: Also try adding to admin_menu with even higher priority
        add_action('admin_menu', array($this, 'add_fallback_menu'), 99);

        // Additional hook to ensure menu is added after all plugins are loaded
        add_action('admin_init', array($this, 'ensure_menu_exists'));
        
        // Add AJAX handlers
        add_action('wp_ajax_alachiq_translate_strings', array($this, 'ajax_translate_strings'));
        add_action('wp_ajax_alachiq_save_api_keys', array($this, 'ajax_save_api_keys'));
        add_action('wp_ajax_alachiq_test_api_keys', array($this, 'ajax_test_api_keys'));
        add_action('wp_ajax_alachiq_get_translation_progress', array($this, 'ajax_get_translation_progress'));
        add_action('wp_ajax_alachiq_export_csv', array($this, 'ajax_export_csv'));
        add_action('wp_ajax_alachiq_import_csv', array($this, 'ajax_import_csv'));
        add_action('wp_ajax_alachiq_sync_translations', array($this, 'ajax_sync_translations'));
        add_action('wp_ajax_alachiq_diagnose_polylang', array($this, 'ajax_diagnose_polylang'));
        add_action('wp_ajax_alachiq_import_to_polylang_db', array($this, 'ajax_import_to_polylang_db'));

        // Add test AJAX handler for debugging
        add_action('wp_ajax_alachiq_test_connection', array($this, 'ajax_test_connection'));
        
        // Add admin scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Note: Disabled excessive registration hooks - using direct database import instead
        // add_action('init', array($this, 'register_strings_for_polylang_admin'), 20);
        // add_action('pll_init', array($this, 'register_strings_for_polylang_admin'), 20);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Debug: Check Polylang status
        $polylang_active = function_exists('PLL');
        $has_languages = $polylang_active && PLL() && PLL()->model && PLL()->model->has_languages();

        // Always try to add under Languages menu first (Polylang creates this menu even without languages)
        if ($polylang_active && PLL()) {
            add_submenu_page(
                'languages',
                __('Auto Translator', 'alachiqgroup-pro'),
                __('Auto Translator', 'alachiqgroup-pro'),
                'manage_options',
                'alachiq-auto-translator',
                array($this, 'admin_page')
            );
        } else {
            // Fallback: Add under Tools menu if Polylang is not available
            add_submenu_page(
                'tools.php',
                __('Auto Translator', 'alachiqgroup-pro'),
                __('Auto Translator', 'alachiqgroup-pro') . ' (Polylang Required)',
                'manage_options',
                'alachiq-auto-translator',
                array($this, 'admin_page')
            );
        }
    }

    /**
     * Add fallback menu (in case main menu doesn't work)
     */
    public function add_fallback_menu() {
        // Only add if no menu was added yet
        global $submenu;

        $menu_exists = false;
        if (isset($submenu['languages']) && is_array($submenu['languages'])) {
            foreach ($submenu['languages'] as $item) {
                if (isset($item[2]) && $item[2] === 'alachiq-auto-translator') {
                    $menu_exists = true;
                    break;
                }
            }
        }

        if (isset($submenu['tools.php']) && is_array($submenu['tools.php'])) {
            foreach ($submenu['tools.php'] as $item) {
                if (isset($item[2]) && $item[2] === 'alachiq-auto-translator') {
                    $menu_exists = true;
                    break;
                }
            }
        }

        if (!$menu_exists) {
            // Force add under Tools as last resort
            add_submenu_page(
                'tools.php',
                __('Auto Translator (Fallback)', 'alachiqgroup-pro'),
                __('Auto Translator', 'alachiqgroup-pro'),
                'manage_options',
                'alachiq-auto-translator-fallback',
                array($this, 'admin_page')
            );
        }
    }

    /**
     * Ensure menu exists (additional check)
     */
    public function ensure_menu_exists() {
        global $submenu;

        // Check if our menu item exists in either location
        $menu_exists = false;

        // Check Languages submenu
        if (isset($submenu['languages']) && is_array($submenu['languages'])) {
            foreach ($submenu['languages'] as $item) {
                if (isset($item[2]) && $item[2] === 'alachiq-auto-translator') {
                    $menu_exists = true;
                    break;
                }
            }
        }

        // Check Tools submenu
        if (!$menu_exists && isset($submenu['tools.php']) && is_array($submenu['tools.php'])) {
            foreach ($submenu['tools.php'] as $item) {
                if (isset($item[2]) && $item[2] === 'alachiq-auto-translator') {
                    $menu_exists = true;
                    break;
                }
            }
        }

        // If menu doesn't exist, force add it
        if (!$menu_exists) {
            $this->add_admin_menu();
        }
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        // Debug: Log the hook name to help troubleshoot
        if (current_user_can('manage_options') && isset($_GET['debug']) && $_GET['debug'] === '1') {
            error_log('Auto Translator Hook: ' . $hook);
        }

        // Check for our page in various possible hook formats
        $valid_hooks = array(
            'languages_page_alachiq-auto-translator',
            'tools_page_alachiq-auto-translator',
            'tools_page_alachiq-auto-translator-fallback',
            'admin_page_alachiq-auto-translator',
            'toplevel_page_alachiq-auto-translator'
        );

        // Also check if we're on our page by checking the page parameter
        $is_our_page = isset($_GET['page']) && (
            $_GET['page'] === 'alachiq-auto-translator' ||
            $_GET['page'] === 'alachiq-auto-translator-fallback'
        );

        if (!in_array($hook, $valid_hooks) && !$is_our_page) {
            return;
        }
        
        wp_enqueue_script('jquery');
        wp_enqueue_script(
            'alachiq-auto-translator',
            get_template_directory_uri() . '/assets/js/auto-translator.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('alachiq-auto-translator', 'alachiqTranslator', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('alachiq_translator_nonce'),
            'strings' => array(
                'translating' => __('Translating...', 'alachiqgroup-pro'),
                'completed' => __('Translation completed!', 'alachiqgroup-pro'),
                'error' => __('Translation error occurred', 'alachiqgroup-pro'),
                'progress' => __('Progress:', 'alachiqgroup-pro')
            )
        ));
        
        wp_enqueue_style(
            'alachiq-auto-translator',
            get_template_directory_uri() . '/assets/css/auto-translator.css',
            array(),
            '1.0.0'
        );
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        // Handle auto-setup request
        if (isset($_GET['setup_languages']) && $_GET['setup_languages'] === '1' && current_user_can('manage_options')) {
            $this->auto_setup_languages();
        }

        // Handle form submission (fallback if AJAX fails)
        if (isset($_POST['save_api_keys']) && current_user_can('manage_options')) {
            if (wp_verify_nonce($_POST['_wpnonce'], 'alachiq_save_api_keys')) {
                $deepl_key = isset($_POST['deepl_api_key']) ? sanitize_text_field($_POST['deepl_api_key']) : '';
                $google_key = isset($_POST['google_api_key']) ? sanitize_text_field($_POST['google_api_key']) : '';

                update_option('alachiqgroup_deepl_api_key', $deepl_key);
                update_option('alachiqgroup_google_api_key', $google_key);

                $this->deepl_api_key = $deepl_key;
                $this->google_api_key = $google_key;

                echo '<div class="notice notice-success"><p>API keys saved successfully!</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Security check failed. Please try again.</p></div>';
            }
        }

        $translation_manager = Alachiqgroup_Pro_Translation_Manager::get_instance();
        $stats = $translation_manager->get_translation_stats();
        
        ?>
        <div class="wrap">
            <h1><?php _e('Automatic Translation System', 'alachiqgroup-pro'); ?></h1>

            <?php
            // Show diagnostic information
            $polylang_active = function_exists('PLL');
            $pll_available = $polylang_active && PLL();
            $has_model = $pll_available && PLL()->model;
            $has_languages = $has_model && PLL()->model->has_languages();

            if (!$polylang_active) {
                echo '<div class="notice notice-error"><p><strong>Error:</strong> Polylang plugin is not active. Please install and activate Polylang to use the Auto Translator.</p></div>';
            } elseif (!$pll_available) {
                echo '<div class="notice notice-error"><p><strong>Error:</strong> Polylang is not properly initialized. Please check your Polylang installation.</p></div>';
            } elseif (!$has_model) {
                echo '<div class="notice notice-error"><p><strong>Error:</strong> Polylang model is not available. Please check your Polylang configuration.</p></div>';
            } elseif (!$has_languages) {
                echo '<div class="notice notice-warning"><p><strong>Setup Required:</strong> Polylang is active but no languages are configured. Please go to <a href="' . admin_url('admin.php?page=mlang') . '">Languages → Settings</a> to add languages first.</p></div>';
                echo '<div class="notice notice-info"><p><strong>Quick Setup:</strong> Go to <strong>Languages → Settings</strong> and add at least Azerbaijani (az) as your default language, then add English (en) and Russian (ru) as additional languages.</p></div>';
                echo '<div class="notice notice-info"><p><strong>Auto Setup:</strong> <a href="' . admin_url('admin.php?page=alachiq-auto-translator&setup_languages=1') . '" class="button button-primary">Auto-Configure Basic Languages</a></p></div>';
            } else {
                $languages = PLL()->model->get_languages_list();
                echo '<div class="notice notice-success"><p><strong>Ready:</strong> Polylang is properly configured with ' . count($languages) . ' languages. You can now use the Auto Translator.</p></div>';

                // Show configured languages
                echo '<div class="notice notice-info"><p><strong>Configured Languages:</strong> ';
                $lang_names = array();
                foreach ($languages as $lang) {
                    $lang_names[] = $lang->name . ' (' . $lang->slug . ')';
                }
                echo implode(', ', $lang_names);
                echo '</p></div>';
            }

            // Debug information for menu location
            if (current_user_can('manage_options') && isset($_GET['debug']) && $_GET['debug'] === '1') {
                echo '<div class="notice notice-info">';
                echo '<h3>🔍 Debug Information</h3>';
                echo '<p><strong>Menu Location:</strong> ';
                if ($polylang_active && PLL()) {
                    echo 'Languages → Auto Translator';
                } else {
                    echo 'Tools → Auto Translator';
                }
                echo '</p>';
                echo '<p><strong>Direct URL:</strong> <a href="' . admin_url('admin.php?page=alachiq-auto-translator') . '">' . admin_url('admin.php?page=alachiq-auto-translator') . '</a></p>';
                echo '<p><strong>Polylang Status:</strong> Active: ' . ($polylang_active ? 'Yes' : 'No') . ', PLL Available: ' . ($pll_available ? 'Yes' : 'No') . ', Has Model: ' . ($has_model ? 'Yes' : 'No') . ', Has Languages: ' . ($has_languages ? 'Yes' : 'No') . '</p>';

                // Test string retrieval
                $test_strings = $this->get_all_strings();
                echo '<p><strong>Available Strings:</strong> ' . count($test_strings) . ' strings found</p>';
                if (count($test_strings) > 0) {
                    echo '<p><strong>Sample Strings:</strong> ';
                    $sample_count = min(3, count($test_strings));
                    for ($i = 0; $i < $sample_count; $i++) {
                        echo '"' . esc_html(substr($test_strings[$i]['string'], 0, 30)) . '..." ';
                    }
                    echo '</p>';
                }

                echo '</div>';
            }
            ?>

            <div class="alachiq-translator-dashboard">
                <!-- Statistics Card -->
                <div class="alachiq-card">
                    <h2><?php _e('Translation Statistics', 'alachiqgroup-pro'); ?></h2>
                    <div class="alachiq-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $stats['total_strings']; ?></span>
                            <span class="stat-label"><?php _e('Total Strings', 'alachiqgroup-pro'); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo count($this->supported_languages); ?></span>
                            <span class="stat-label"><?php _e('Target Languages', 'alachiqgroup-pro'); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $stats['total_strings'] * count($this->supported_languages); ?></span>
                            <span class="stat-label"><?php _e('Total Translations Needed', 'alachiqgroup-pro'); ?></span>
                        </div>
                    </div>
                </div>
                
                <!-- API Configuration Card -->
                <div class="alachiq-card">
                    <h2><?php _e('API Configuration', 'alachiqgroup-pro'); ?></h2>
                    <form id="api-keys-form" method="post" action="">
                        <?php wp_nonce_field('alachiq_save_api_keys'); ?>
                        <input type="hidden" name="save_api_keys" value="1" />
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('DeepL API Key', 'alachiqgroup-pro'); ?></th>
                                <td>
                                    <input type="password" id="deepl_api_key" name="deepl_api_key"
                                           value="<?php echo esc_attr($this->deepl_api_key); ?>"
                                           class="regular-text" />
                                    <p class="description">
                                        <?php _e('Get your free API key from', 'alachiqgroup-pro'); ?>
                                        <a href="https://www.deepl.com/pro-api" target="_blank">DeepL Pro API</a>
                                        <?php _e('(Recommended - Best quality for business terms)', 'alachiqgroup-pro'); ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php _e('Google Translate API Key', 'alachiqgroup-pro'); ?></th>
                                <td>
                                    <input type="password" id="google_api_key" name="google_api_key"
                                           value="<?php echo esc_attr($this->google_api_key); ?>"
                                           class="regular-text" />
                                    <p class="description">
                                        <?php _e('Get your API key from', 'alachiqgroup-pro'); ?>
                                        <a href="https://cloud.google.com/translate" target="_blank">Google Cloud Translate</a>
                                        <?php _e('(Fallback option)', 'alachiqgroup-pro'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                        <p class="submit">
                            <button type="submit" class="button-primary"><?php _e('Save API Keys', 'alachiqgroup-pro'); ?></button>
                            <button type="button" id="test-api-keys" class="button-secondary" style="margin-left: 10px;"><?php _e('Test API Keys', 'alachiqgroup-pro'); ?></button>
                            <span class="description" style="margin-left: 10px;">
                                <?php _e('Note: If AJAX fails, the form will submit normally.', 'alachiqgroup-pro'); ?>
                            </span>
                        </p>
                    </form>
                </div>
                
                <!-- Translation Control Card -->
                <div class="alachiq-card">
                    <h2><?php _e('Automatic Translation', 'alachiqgroup-pro'); ?></h2>
                    
                    <div class="translation-options">
                        <h3><?php _e('Select Languages to Translate', 'alachiqgroup-pro'); ?></h3>
                        <?php foreach ($this->supported_languages as $code => $lang): ?>
                        <label>
                            <input type="checkbox" name="target_languages[]" value="<?php echo $code; ?>" checked>
                            <?php echo $lang['name']; ?> (<?php echo strtoupper($code); ?>)
                        </label><br>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="translation-method">
                        <h3><?php _e('Translation Method', 'alachiqgroup-pro'); ?></h3>
                        <label>
                            <input type="radio" name="translation_method" value="deepl" checked>
                            <?php _e('DeepL API (Recommended)', 'alachiqgroup-pro'); ?>
                        </label><br>
                        <label>
                            <input type="radio" name="translation_method" value="google">
                            <?php _e('Google Translate API', 'alachiqgroup-pro'); ?>
                        </label><br>
                        <label>
                            <input type="radio" name="translation_method" value="both">
                            <?php _e('DeepL with Google Fallback', 'alachiqgroup-pro'); ?>
                        </label>
                    </div>

                    <div class="translation-limit">
                        <h3><?php _e('Translation Limit', 'alachiqgroup-pro'); ?></h3>
                        <label>
                            <input type="number" id="translation_limit" name="translation_limit"
                                   value="" min="1" max="357" placeholder="All strings"
                                   class="small-text" />
                            <?php _e('Number of strings to translate (leave empty for all)', 'alachiqgroup-pro'); ?>
                        </label>
                        <p class="description">
                            <?php _e('Enter a number to translate only that many strings randomly. Useful for testing or partial translations.', 'alachiqgroup-pro'); ?>
                        </p>
                    </div>

                    <div class="translation-controls">
                        <button id="start-translation" class="button-primary button-large">
                            <?php _e('Start Automatic Translation', 'alachiqgroup-pro'); ?>
                        </button>
                        <button id="stop-translation" class="button-secondary" style="display:none;">
                            <?php _e('Stop Translation', 'alachiqgroup-pro'); ?>
                        </button>
                        <button id="test-connection" class="button-secondary" style="margin-left: 10px;">
                            <?php _e('Test AJAX Connection', 'alachiqgroup-pro'); ?>
                        </button>
                        <button id="sync-translations" class="button-secondary" style="margin-left: 10px;">
                            <?php _e('Sync with Polylang', 'alachiqgroup-pro'); ?>
                        </button>
                        <button id="diagnose-polylang" class="button-secondary" style="margin-left: 10px;">
                            <?php _e('Diagnose Polylang', 'alachiqgroup-pro'); ?>
                        </button>
                        <button id="import-to-polylang-db" class="button-primary" style="margin-left: 10px;">
                            <?php _e('Import to Polylang Database', 'alachiqgroup-pro'); ?>
                        </button>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div id="translation-progress" style="display:none;">
                        <h3><?php _e('Translation Progress', 'alachiqgroup-pro'); ?></h3>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="progress-info">
                            <span id="progress-text">0 / 0</span>
                            <span id="progress-percentage">0%</span>
                        </div>
                        <div id="translation-log"></div>
                    </div>
                </div>
                
                <!-- Manual Translation Card -->
                <div class="alachiq-card">
                    <h2><?php _e('Manual Translation Options', 'alachiqgroup-pro'); ?></h2>
                    <p><?php _e('Alternative methods for translation:', 'alachiqgroup-pro'); ?></p>
                    
                    <div class="manual-options">
                        <button id="export-csv" class="button">
                            <?php _e('Export to CSV', 'alachiqgroup-pro'); ?>
                        </button>
                        <button id="import-csv" class="button">
                            <?php _e('Import from CSV', 'alachiqgroup-pro'); ?>
                        </button>
                        <input type="file" id="csv-file" accept=".csv" style="display:none;">
                    </div>
                    
                    <p class="description">
                        <?php _e('Export strings to CSV, translate in Excel/Google Sheets, then import back.', 'alachiqgroup-pro'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Debug Information -->
        <script>
        jQuery(document).ready(function($) {
            console.log('Auto Translator: jQuery loaded');
            console.log('Auto Translator: alachiqTranslator object:', typeof alachiqTranslator !== 'undefined' ? alachiqTranslator : 'NOT FOUND');

            // Test if our form exists
            if ($('#api-keys-form').length) {
                console.log('Auto Translator: Form found');
            } else {
                console.error('Auto Translator: Form not found');
            }
        });
        </script>

        <?php
    }
    
    /**
     * AJAX: Save API keys
     */
    public function ajax_save_api_keys() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
                wp_send_json_error(__('Security check failed', 'alachiqgroup-pro'));
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Insufficient permissions', 'alachiqgroup-pro'));
                return;
            }

            // Validate and sanitize input
            $deepl_key = isset($_POST['deepl_api_key']) ? sanitize_text_field($_POST['deepl_api_key']) : '';
            $google_key = isset($_POST['google_api_key']) ? sanitize_text_field($_POST['google_api_key']) : '';

            // Save options
            $deepl_saved = update_option('alachiqgroup_deepl_api_key', $deepl_key);
            $google_saved = update_option('alachiqgroup_google_api_key', $google_key);

            // Update instance variables
            $this->deepl_api_key = $deepl_key;
            $this->google_api_key = $google_key;

            // Log for debugging
            error_log('Auto Translator: API keys saved - DeepL: ' . (!empty($deepl_key) ? 'Set' : 'Empty') . ', Google: ' . (!empty($google_key) ? 'Set' : 'Empty'));

            wp_send_json_success(__('API keys saved successfully', 'alachiqgroup-pro'));

        } catch (Exception $e) {
            error_log('Auto Translator AJAX Error: ' . $e->getMessage());
            wp_send_json_error(__('An error occurred while saving API keys', 'alachiqgroup-pro'));
        }
    }

    /**
     * AJAX: Test API keys
     */
    public function ajax_test_api_keys() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
                wp_send_json_error(__('Security check failed', 'alachiqgroup-pro'));
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Insufficient permissions', 'alachiqgroup-pro'));
                return;
            }

            $deepl_key = isset($_POST['deepl_api_key']) ? sanitize_text_field($_POST['deepl_api_key']) : '';
            $google_key = isset($_POST['google_api_key']) ? sanitize_text_field($_POST['google_api_key']) : '';

            $results = array();

            // Test DeepL API
            if (!empty($deepl_key)) {
                $test_translation = $this->test_deepl_api($deepl_key);
                if ($test_translation) {
                    $results[] = 'DeepL API: Working ✅';
                } else {
                    $results[] = 'DeepL API: Failed ❌';
                }
            }

            // Test Google API
            if (!empty($google_key)) {
                $test_translation = $this->test_google_api($google_key);
                if ($test_translation) {
                    $results[] = 'Google API: Working ✅';
                } else {
                    $results[] = 'Google API: Failed ❌';
                }
            }

            if (empty($results)) {
                wp_send_json_error(__('No API keys provided for testing', 'alachiqgroup-pro'));
            } else {
                wp_send_json_success(implode(', ', $results));
            }

        } catch (Exception $e) {
            error_log('Auto Translator Test Error: ' . $e->getMessage());
            wp_send_json_error(__('Test failed: ', 'alachiqgroup-pro') . $e->getMessage());
        }
    }
    
    /**
     * Get all translatable strings
     */
    private function get_all_strings() {
        try {
            $strings = array();

            // Check if Polylang is available
            if (!function_exists('PLL') || !PLL() || !PLL()->model) {
                error_log('Auto Translator: Polylang not available for getting strings');
                return $strings;
            }

            // Force register all strings
            error_log('Auto Translator: Checking Translation Manager class');
            if (class_exists('Alachiqgroup_Pro_Translation_Manager')) {
                error_log('Auto Translator: Translation Manager class exists, getting instance');
                $translation_manager = Alachiqgroup_Pro_Translation_Manager::get_instance();
                if ($translation_manager) {
                    error_log('Auto Translator: Calling force_register_all()');
                    $translation_manager->force_register_all();
                    error_log('Auto Translator: force_register_all() completed');
                } else {
                    error_log('Auto Translator: Translation Manager instance is null');
                }
            } else {
                error_log('Auto Translator: Translation Manager class does not exist');
            }

            // Get registered strings from Polylang Admin Strings
            error_log('Auto Translator: Getting strings from PLL_Admin_Strings');
            error_log('Auto Translator: Checking if PLL_Admin_Strings class exists...');

            // Make sure Polylang admin classes are loaded
            if (function_exists('PLL') && PLL() instanceof PLL_Admin_Base) {
                error_log('Auto Translator: PLL Admin Base is available');

                // Try to load the admin strings class if not already loaded
                if (!class_exists('PLL_Admin_Strings')) {
                    $polylang_path = WP_PLUGIN_DIR . '/polylang/admin/admin-strings.php';
                    if (file_exists($polylang_path)) {
                        require_once $polylang_path;
                        error_log('Auto Translator: Loaded PLL_Admin_Strings manually');
                    }
                }
            }

            if (class_exists('PLL_Admin_Strings')) {
                error_log('Auto Translator: PLL_Admin_Strings class is available');
                $polylang_strings = PLL_Admin_Strings::get_strings();
                error_log('Auto Translator: PLL_Admin_Strings returned ' . count($polylang_strings) . ' strings');
            } else {
                error_log('Auto Translator: PLL_Admin_Strings class not found, trying alternative method');
                // Alternative method: get strings directly from our translation files
                $polylang_strings = $this->get_strings_from_files();
                error_log('Auto Translator: Alternative method returned ' . count($polylang_strings) . ' strings');
            }

            if (empty($polylang_strings)) {
                error_log('Auto Translator: No strings found in Polylang');
                return $strings;
            }

            foreach ($polylang_strings as $string) {
                if (isset($string['name']) && isset($string['string'])) {
                    $strings[] = array(
                        'name' => $string['name'],
                        'string' => $string['string'],
                        'context' => isset($string['context']) ? $string['context'] : '',
                        'multiline' => isset($string['multiline']) ? $string['multiline'] : 0
                    );
                }
            }

            error_log('Auto Translator: Found ' . count($strings) . ' translatable strings');
            return $strings;

        } catch (Exception $e) {
            error_log('Auto Translator: Error getting strings - ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Get strings directly from translation files (fallback method)
     */
    private function get_strings_from_files() {
        $strings = array();
        $translations_dir = get_template_directory() . '/includes/translations/';

        $translation_files = array(
            'navigation',
            'homepage',
            'about',
            'services',
            'contact',
            'decorations',
            'single-project',
            'projects-archive',
            'search-archive',
            'common',
            'admin'
        );

        foreach ($translation_files as $context) {
            $file_path = $translations_dir . $context . '-strings.php';

            if (file_exists($file_path)) {
                $file_strings = include $file_path;

                if (is_array($file_strings)) {
                    foreach ($file_strings as $string_text => $string_context) {
                        if (!empty($string_text) && is_string($string_text)) {
                            $strings[] = array(
                                'name' => $string_text, // Use the string itself as the name
                                'string' => $string_text,
                                'context' => $context,
                                'multiline' => (strlen($string_text) > 100) ? 1 : 0 // Assume long strings are multiline
                            );
                        }
                    }
                }
            }
        }

        return $strings;
    }

    /**
     * AJAX: Translate strings
     */
    public function ajax_translate_strings() {
        try {
            // Debug logging
            error_log('Auto Translator AJAX: Starting ajax_translate_strings');
            error_log('Auto Translator AJAX: POST data: ' . print_r($_POST, true));

            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
                error_log('Auto Translator AJAX: Nonce verification failed');
                wp_send_json_error(__('Security check failed', 'alachiqgroup-pro'));
                return;
            }

            if (!current_user_can('manage_options')) {
                error_log('Auto Translator AJAX: Permission check failed');
                wp_send_json_error(__('Insufficient permissions', 'alachiqgroup-pro'));
                return;
            }

            // Validate input
            if (!isset($_POST['target_languages']) || !is_array($_POST['target_languages'])) {
                wp_send_json_error(__('No target languages specified', 'alachiqgroup-pro'));
                return;
            }

            if (!isset($_POST['translation_method'])) {
                wp_send_json_error(__('No translation method specified', 'alachiqgroup-pro'));
                return;
            }

            $target_languages = $_POST['target_languages'];
            $method = $_POST['translation_method'];
            $translation_limit = isset($_POST['translation_limit']) && !empty($_POST['translation_limit']) ? intval($_POST['translation_limit']) : null;
            $batch_size = 10; // Process 10 strings at a time
            $offset = intval($_POST['offset']);

            // Validate API keys based on method
            if ($method === 'deepl' && empty($this->deepl_api_key)) {
                wp_send_json_error(__('DeepL API key is required for this translation method', 'alachiqgroup-pro'));
                return;
            }

            if ($method === 'google' && empty($this->google_api_key)) {
                wp_send_json_error(__('Google Translate API key is required for this translation method', 'alachiqgroup-pro'));
                return;
            }

            if ($method === 'both' && empty($this->deepl_api_key) && empty($this->google_api_key)) {
                wp_send_json_error(__('At least one API key is required for translation', 'alachiqgroup-pro'));
                return;
            }

            // Get strings
            error_log('Auto Translator AJAX: About to call get_all_strings()');
            $all_strings = $this->get_all_strings();
            error_log('Auto Translator AJAX: get_all_strings() completed');

            // Apply translation limit if specified
            if ($translation_limit && $translation_limit > 0) {
                // Shuffle the strings to get random selection
                $shuffled_strings = $all_strings;
                shuffle($shuffled_strings);
                // Take only the specified number of strings
                $strings = array_slice($shuffled_strings, 0, $translation_limit);
                error_log("Auto Translator: Limited to $translation_limit random strings out of " . count($all_strings) . " total strings");
            } else {
                $strings = $all_strings;
            }

            $total_strings = count($strings);

            // Log for debugging
            error_log('Auto Translator: Processing batch - Offset: ' . $offset . ', Total: ' . $total_strings . ', Method: ' . $method . ($translation_limit ? ', Limit: ' . $translation_limit : ''));

            if ($total_strings === 0) {
                wp_send_json_error(__('No translatable strings found. Please ensure Polylang is configured and strings are registered.', 'alachiqgroup-pro'));
                return;
            }

            if ($offset >= $total_strings) {
                wp_send_json_success(array(
                    'completed' => true,
                    'message' => __('All translations completed!', 'alachiqgroup-pro')
                ));
                return;
            }

            $batch_strings = array_slice($strings, $offset, $batch_size);
            $translated_count = 0;
            $errors = array();

            error_log("Auto Translator DEBUG: Processing batch of " . count($batch_strings) . " strings");
            error_log("Auto Translator DEBUG: Target languages: " . implode(', ', $target_languages));

            foreach ($batch_strings as $index => $string_data) {
                error_log("Auto Translator DEBUG: Processing string #" . ($index + 1) . " - Name: '{$string_data['name']}', Content: '" . substr($string_data['string'], 0, 100) . "...'");
                foreach ($target_languages as $target_lang) {
                    try {
                        $translation = $this->translate_string(
                            $string_data['string'],
                            'az', // Source language (Azerbaijani)
                            $target_lang,
                            $method
                        );

                        if ($translation) {
                            error_log("Auto Translator DEBUG: Got translation for '{$string_data['name']}' to '$target_lang': '$translation'");

                            // Save translation to Polylang
                            $save_result = $this->save_polylang_translation(
                                $string_data['name'],
                                $translation,
                                $target_lang
                            );

                            if ($save_result) {
                                $translated_count++;
                                error_log("Auto Translator SUCCESS: Translation saved and counted");
                            } else {
                                error_log("Auto Translator ERROR: Translation failed to save");
                                $errors[] = sprintf(
                                    __('Failed to save translation for "%s" to %s', 'alachiqgroup-pro'),
                                    substr($string_data['string'], 0, 50),
                                    $target_lang
                                );
                            }
                        } else {
                            error_log("Auto Translator ERROR: No translation returned for '{$string_data['name']}' to '$target_lang'");
                            $errors[] = sprintf(
                                __('No translation returned for "%s" to %s', 'alachiqgroup-pro'),
                                substr($string_data['string'], 0, 50),
                                $target_lang
                            );
                        }
                    } catch (Exception $e) {
                        $errors[] = sprintf(
                            __('Error translating "%s" to %s: %s', 'alachiqgroup-pro'),
                            substr($string_data['string'], 0, 50),
                            $target_lang,
                            $e->getMessage()
                        );
                        error_log('Auto Translator Error: ' . $e->getMessage());
                    }
                }
            }

            // Log batch summary
            error_log("Auto Translator BATCH SUMMARY: Processed " . count($batch_strings) . " strings, Successfully translated: $translated_count, Errors: " . count($errors));
            if (!empty($errors)) {
                error_log("Auto Translator BATCH ERRORS: " . implode('; ', $errors));
            }

            wp_send_json_success(array(
                'completed' => false,
                'offset' => $offset + $batch_size,
                'total' => $total_strings,
                'translated' => $translated_count,
                'errors' => $errors,
                'progress' => round(($offset + $batch_size) / $total_strings * 100, 2)
            ));

        } catch (Exception $e) {
            error_log('Auto Translator AJAX Error: ' . $e->getMessage());
            wp_send_json_error(__('Translation process failed: ', 'alachiqgroup-pro') . $e->getMessage());
        }
    }

    /**
     * AJAX: Test connection (for debugging)
     */
    public function ajax_test_connection() {
        error_log('Auto Translator: Test connection AJAX called');
        wp_send_json_success('Connection test successful');
    }

    /**
     * Translate a single string
     */
    private function translate_string($text, $source_lang, $target_lang, $method = 'deepl') {
        // Check cache first
        $cache_key = md5($text . $source_lang . $target_lang);
        if (isset($this->translation_cache[$cache_key])) {
            return $this->translation_cache[$cache_key];
        }

        $translation = false;

        switch ($method) {
            case 'deepl':
                $translation = $this->translate_with_deepl($text, $source_lang, $target_lang);
                break;
            case 'google':
                $translation = $this->translate_with_google($text, $source_lang, $target_lang);
                break;
            case 'both':
                $translation = $this->translate_with_deepl($text, $source_lang, $target_lang);
                if (!$translation) {
                    $translation = $this->translate_with_google($text, $source_lang, $target_lang);
                }
                break;
        }

        // Cache the result
        if ($translation) {
            $this->translation_cache[$cache_key] = $translation;
        }

        return $translation;
    }

    /**
     * Translate using DeepL API
     */
    private function translate_with_deepl($text, $source_lang, $target_lang) {
        if (empty($this->deepl_api_key)) {
            return false;
        }

        // Map language codes to DeepL format
        $deepl_source = $source_lang === 'az' ? 'EN' : $this->supported_languages[$source_lang]['deepl'];
        $deepl_target = $this->supported_languages[$target_lang]['deepl'];

        $data = array(
            'auth_key' => $this->deepl_api_key,
            'text' => $text,
            'source_lang' => $deepl_source,
            'target_lang' => $deepl_target,
            'preserve_formatting' => '1',
            'tag_handling' => 'html'
        );

        $response = wp_remote_post($this->deepl_api_url, array(
            'body' => $data,
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);

        if (isset($result['translations'][0]['text'])) {
            return $result['translations'][0]['text'];
        }

        return false;
    }

    /**
     * Translate using Google Translate API
     */
    private function translate_with_google($text, $source_lang, $target_lang) {
        if (empty($this->google_api_key)) {
            return false;
        }

        $google_source = $source_lang === 'az' ? 'az' : $this->supported_languages[$source_lang]['google'];
        $google_target = $this->supported_languages[$target_lang]['google'];

        $url = $this->google_api_url . '?key=' . $this->google_api_key;

        $data = array(
            'q' => $text,
            'source' => $google_source,
            'target' => $google_target,
            'format' => 'html'
        );

        $response = wp_remote_post($url, array(
            'body' => $data,
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);

        if (isset($result['data']['translations'][0]['translatedText'])) {
            return html_entity_decode($result['data']['translations'][0]['translatedText']);
        }

        return false;
    }

    /**
     * Test DeepL API with a simple translation
     */
    private function test_deepl_api($api_key) {
        $data = array(
            'auth_key' => $api_key,
            'text' => 'Hello',
            'source_lang' => 'EN',
            'target_lang' => 'RU'
        );

        $response = wp_remote_post('https://api-free.deepl.com/v2/translate', array(
            'body' => $data,
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);

        return isset($result['translations'][0]['text']);
    }

    /**
     * Test Google Translate API with a simple translation
     */
    private function test_google_api($api_key) {
        $url = 'https://translation.googleapis.com/language/translate/v2?key=' . $api_key;

        $data = array(
            'q' => 'Hello',
            'source' => 'en',
            'target' => 'ru'
        );

        $response = wp_remote_post($url, array(
            'body' => $data,
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);

        return isset($result['data']['translations'][0]['translatedText']);
    }

    /**
     * Save translation to Polylang using the correct API
     */
    private function save_polylang_translation($string_name, $translation, $language) {
        error_log("Auto Translator DEBUG: Attempting to save translation - String: '$string_name', Language: '$language', Translation: '$translation'");

        if (!function_exists('PLL')) {
            error_log('Auto Translator ERROR: PLL() function not available');
            return false;
        }

        if (!function_exists('pll_set_string_translation')) {
            error_log('Auto Translator ERROR: pll_set_string_translation() function not available');

            // Let's check what Polylang functions ARE available
            $available_functions = array();
            $polylang_functions = array(
                'pll_register_string', 'pll_translate_string', 'pll__', 'pll_e',
                'pll_the_languages', 'pll_current_language', 'pll_default_language',
                'pll_get_post_language', 'pll_set_post_language', 'pll_get_term_language'
            );

            foreach ($polylang_functions as $func) {
                if (function_exists($func)) {
                    $available_functions[] = $func;
                }
            }

            error_log('Auto Translator DEBUG: Available Polylang functions: ' . implode(', ', $available_functions));

            // Since pll_set_string_translation is not available, try WordPress options approach
            error_log("Auto Translator DEBUG: Trying WordPress options approach since Polylang tables don't exist");
            return $this->save_translation_wordpress_options($string_name, $translation, $language);
        }

        // Check if Polylang is properly initialized
        $pll = PLL();
        if (!$pll) {
            error_log('Auto Translator ERROR: PLL() returned null');
            return false;
        }

        error_log("Auto Translator DEBUG: Polylang is available, attempting to save translation");

        try {
            // Check if the string is registered in Polylang
            $registered_strings = pll_the_languages(array('raw' => 1));
            error_log("Auto Translator DEBUG: Available languages: " . print_r($registered_strings, true));

            // First, make sure the string is registered for translation
            // This is crucial - strings must be registered before they can be translated
            if (function_exists('pll_register_string')) {
                pll_register_string($string_name, $string_name, 'alachiqgroup-pro');
                error_log("Auto Translator DEBUG: Registered string '$string_name' for translation");
            }

            // Use Polylang's official API to save string translations
            $result = pll_set_string_translation($string_name, $language, $translation);

            error_log("Auto Translator DEBUG: pll_set_string_translation returned: " . var_export($result, true));

            if ($result) {
                error_log("Auto Translator SUCCESS: Translation saved for '$string_name' in '$language': '$translation'");

                // Verify the translation was saved by trying to retrieve it
                $saved_translation = pll__($string_name);
                error_log("Auto Translator VERIFY: Retrieved translation: '$saved_translation'");

                return true;
            } else {
                error_log("Auto Translator FAILED: pll_set_string_translation returned false for '$string_name' in '$language'");
                return false;
            }

        } catch (Exception $e) {
            error_log('Auto Translator ERROR: Exception saving translation - ' . $e->getMessage());
            error_log('Auto Translator ERROR: Exception trace - ' . $e->getTraceAsString());
        }

        return false;
    }

    /**
     * WordPress options method to save translations when Polylang database tables don't exist
     */
    private function save_translation_wordpress_options($string_name, $translation, $language) {
        error_log("Auto Translator DEBUG: WordPress options method called for string '$string_name', language '$language'");

        try {
            // Store translations in WordPress options table
            $option_name = "alachiq_translations_{$language}";

            // Get existing translations for this language
            $existing_translations = get_option($option_name, array());

            // Add/update the translation
            $existing_translations[$string_name] = $translation;

            // Save back to options
            $result = update_option($option_name, $existing_translations);

            if ($result) {
                error_log("Auto Translator SUCCESS: WordPress options method saved translation for '$string_name' to '$language': '$translation'");

                // Also try to register the string with Polylang if possible
                if (function_exists('pll_register_string')) {
                    pll_register_string($string_name, $string_name, 'alachiqgroup-pro');
                    error_log("Auto Translator DEBUG: Registered string '$string_name' with Polylang");
                }

                return true;
            } else {
                error_log("Auto Translator ERROR: WordPress options method failed to save translation for '$string_name' to '$language'");
                return false;
            }

        } catch (Exception $e) {
            error_log("Auto Translator ERROR: WordPress options method exception: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get translation from WordPress options
     */
    public function get_translation_from_options($string_name, $language) {
        $option_name = "alachiq_translations_{$language}";
        $translations = get_option($option_name, array());

        return isset($translations[$string_name]) ? $translations[$string_name] : null;
    }

    /**
     * Get all translations for a language from WordPress options
     */
    public function get_all_translations_for_language($language) {
        $option_name = "alachiq_translations_{$language}";
        return get_option($option_name, array());
    }

    /**
     * Sync our saved translations with Polylang's system
     */
    public function sync_translations_with_polylang() {
        error_log("Auto Translator DEBUG: Attempting to sync translations with Polylang");

        // Check what Polylang functions are available
        $pll_register_available = function_exists('pll_register_string');
        $pll_set_translation_available = function_exists('pll_set_string_translation');

        error_log("Auto Translator DEBUG: pll_register_string available: " . ($pll_register_available ? 'YES' : 'NO'));
        error_log("Auto Translator DEBUG: pll_set_string_translation available: " . ($pll_set_translation_available ? 'YES' : 'NO'));

        // If pll_register_string is available, use it to register strings first
        if ($pll_register_available) {
            error_log("Auto Translator DEBUG: Using pll_register_string to register strings");
            return $this->sync_with_pll_register_only();
        }

        // Check if we have the necessary Polylang functions
        if (!$pll_register_available || !$pll_set_translation_available) {
            error_log("Auto Translator DEBUG: Required Polylang functions not available for sync");

            // Try alternative method - direct database insertion
            return $this->sync_translations_direct_database();
        }

        $languages = ['en', 'ru'];
        $synced_count = 0;

        foreach ($languages as $language) {
            $translations = $this->get_all_translations_for_language($language);
            error_log("Auto Translator DEBUG: Found " . count($translations) . " translations for language '$language'");

            foreach ($translations as $string_name => $translation) {
                // Register the string with Polylang
                pll_register_string($string_name, $string_name, 'alachiqgroup-pro');

                // Try to set the translation
                $result = pll_set_string_translation($string_name, $language, $translation);

                if ($result) {
                    $synced_count++;
                    error_log("Auto Translator SUCCESS: Synced '$string_name' -> '$translation' for language '$language'");
                } else {
                    error_log("Auto Translator WARNING: Failed to sync '$string_name' for language '$language'");
                }
            }
        }

        error_log("Auto Translator DEBUG: Synced $synced_count translations with Polylang");
        return $synced_count;
    }

    /**
     * Sync using only pll_register_string (when pll_set_string_translation is not available)
     */
    public function sync_with_pll_register_only() {
        error_log("Auto Translator DEBUG: Syncing with pll_register_string only");

        $languages = ['en', 'ru'];
        $registered_count = 0;

        // First, register all strings with Polylang
        foreach ($languages as $language) {
            $translations = $this->get_all_translations_for_language($language);
            error_log("Auto Translator DEBUG: Registering " . count($translations) . " strings for language '$language'");

            foreach ($translations as $string_name => $translation) {
                // Register the original string with Polylang
                if (function_exists('pll_register_string')) {
                    pll_register_string($string_name, $string_name, 'alachiqgroup-pro');
                    $registered_count++;
                    error_log("Auto Translator DEBUG: Registered string '$string_name' with Polylang");
                }
            }
        }

        // Since we can't use pll_set_string_translation, we'll create a custom translation system
        // that works with the registered strings
        $this->setup_custom_translation_system();

        error_log("Auto Translator SUCCESS: Registered $registered_count strings with Polylang and set up custom translation system");
        return $registered_count;
    }

    /**
     * Set up custom translation system that works with registered Polylang strings
     */
    public function setup_custom_translation_system() {
        error_log("Auto Translator DEBUG: Setting up custom translation system");

        // Hook into WordPress to override translations
        add_filter('gettext', array($this, 'custom_translate_text'), 10, 3);
        add_filter('gettext_with_context', array($this, 'custom_translate_text_with_context'), 10, 4);

        // Also hook into Polylang's translation functions if they exist
        if (function_exists('pll__')) {
            add_filter('pll_translate_string', array($this, 'bridge_translate_string'), 10, 3);
        }

        // Force Polylang to refresh its string cache
        $this->force_polylang_refresh();

        error_log("Auto Translator DEBUG: Custom translation system hooks installed");
    }

    /**
     * Force Polylang to refresh its string cache and show our registered strings
     */
    public function force_polylang_refresh() {
        error_log("Auto Translator DEBUG: Forcing Polylang refresh");

        // Clear Polylang's string cache if possible
        if (function_exists('pll_cache_delete')) {
            pll_cache_delete('strings');
            error_log("Auto Translator DEBUG: Cleared Polylang string cache");
        }

        // Try to trigger Polylang's string scanning
        if (class_exists('PLL_Admin_Strings')) {
            try {
                $pll_admin_strings = new PLL_Admin_Strings();
                if (method_exists($pll_admin_strings, 'register_strings')) {
                    $pll_admin_strings->register_strings();
                    error_log("Auto Translator DEBUG: Triggered Polylang string registration");
                }
            } catch (Exception $e) {
                error_log("Auto Translator DEBUG: Could not trigger Polylang string registration: " . $e->getMessage());
            }
        }

        // Set a flag to indicate strings need to be visible in admin
        update_option('alachiq_polylang_strings_registered', time());
        error_log("Auto Translator DEBUG: Set Polylang refresh flag");
    }

    /**
     * Register strings for Polylang admin interface
     */
    public function register_strings_for_polylang_admin() {
        // Only run if we have registered strings and Polylang is active
        if (!function_exists('pll_register_string') || !get_option('alachiq_polylang_strings_registered')) {
            return;
        }

        error_log("Auto Translator DEBUG: Registering strings for Polylang admin interface");

        $languages = ['en', 'ru'];
        $registered_count = 0;

        foreach ($languages as $language) {
            $translations = $this->get_all_translations_for_language($language);

            foreach ($translations as $string_name => $translation) {
                // Register each unique string with Polylang
                pll_register_string($string_name, $string_name, 'alachiqgroup-pro');
                $registered_count++;
            }
        }

        if ($registered_count > 0) {
            error_log("Auto Translator DEBUG: Registered $registered_count strings for Polylang admin interface");
        }
    }

    /**
     * Custom translation function for gettext
     */
    public function custom_translate_text($translation, $text, $domain) {
        if ($domain === 'alachiqgroup-pro') {
            $current_language = $this->get_current_language();
            if ($current_language && $current_language !== 'az') {
                $our_translation = $this->get_translation_from_options($text, $current_language);
                if ($our_translation) {
                    error_log("Auto Translator DEBUG: Custom gettext provided '$text' -> '$our_translation' for '$current_language'");
                    return $our_translation;
                }
            }
        }
        return $translation;
    }

    /**
     * Custom translation function for gettext with context
     */
    public function custom_translate_text_with_context($translation, $text, $context, $domain) {
        if ($domain === 'alachiqgroup-pro') {
            $current_language = $this->get_current_language();
            if ($current_language && $current_language !== 'az') {
                $our_translation = $this->get_translation_from_options($text, $current_language);
                if ($our_translation) {
                    error_log("Auto Translator DEBUG: Custom gettext_with_context provided '$text' -> '$our_translation' for '$current_language'");
                    return $our_translation;
                }
            }
        }
        return $translation;
    }

    /**
     * Get current language (works with or without Polylang)
     */
    public function get_current_language() {
        // Try Polylang first
        if (function_exists('pll_current_language')) {
            return pll_current_language();
        }

        // Fallback to locale
        $locale = get_locale();
        if (strpos($locale, 'en') === 0) return 'en';
        if (strpos($locale, 'ru') === 0) return 'ru';

        return 'az'; // Default
    }

    /**
     * Sync translations using direct database method
     */
    public function sync_translations_direct_database() {
        global $wpdb;

        error_log("Auto Translator DEBUG: Attempting direct database sync");

        $languages = ['en', 'ru'];
        $synced_count = 0;

        // Check if Polylang tables exist
        $strings_table = $wpdb->prefix . 'polylang_strings';
        $mlt_table = $wpdb->prefix . 'polylang_mlt';

        $strings_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$strings_table'") == $strings_table;
        $mlt_table_exists = $wpdb->get_var("SHOW TABLES LIKE '$mlt_table'") == $mlt_table;

        error_log("Auto Translator DEBUG: Polylang strings table exists: " . ($strings_table_exists ? 'YES' : 'NO'));
        error_log("Auto Translator DEBUG: Polylang MLT table exists: " . ($mlt_table_exists ? 'YES' : 'NO'));

        if (!$strings_table_exists || !$mlt_table_exists) {
            error_log("Auto Translator WARNING: Polylang database tables don't exist. Creating translation bridge instead.");
            return $this->create_translation_bridge();
        }

        // If tables exist, try to insert translations directly
        foreach ($languages as $language) {
            $translations = $this->get_all_translations_for_language($language);
            error_log("Auto Translator DEBUG: Found " . count($translations) . " translations for language '$language'");

            foreach ($translations as $string_name => $translation) {
                // Try to insert/update in Polylang tables
                $result = $this->insert_polylang_translation($string_name, $translation, $language);

                if ($result) {
                    $synced_count++;
                    error_log("Auto Translator SUCCESS: Direct DB synced '$string_name' -> '$translation' for language '$language'");
                } else {
                    error_log("Auto Translator WARNING: Failed to direct DB sync '$string_name' for language '$language'");
                }
            }
        }

        error_log("Auto Translator DEBUG: Direct database synced $synced_count translations");
        return $synced_count;
    }

    /**
     * Create a translation bridge that hooks into Polylang's translation functions
     */
    public function create_translation_bridge() {
        error_log("Auto Translator DEBUG: Creating translation bridge");

        // Hook into Polylang's translation functions
        add_filter('pll_translate_string', array($this, 'bridge_translate_string'), 10, 3);

        $languages = ['en', 'ru'];
        $total_translations = 0;

        foreach ($languages as $language) {
            $translations = $this->get_all_translations_for_language($language);
            $total_translations += count($translations);
            error_log("Auto Translator DEBUG: Bridge registered " . count($translations) . " translations for language '$language'");
        }

        error_log("Auto Translator SUCCESS: Translation bridge created with $total_translations translations");
        return $total_translations;
    }

    /**
     * Bridge function to provide translations when Polylang requests them
     */
    public function bridge_translate_string($translation, $string, $language) {
        // Check if we have a translation for this string in this language
        $our_translation = $this->get_translation_from_options($string, $language);

        if ($our_translation) {
            error_log("Auto Translator DEBUG: Bridge provided translation for '$string' in '$language': '$our_translation'");
            return $our_translation;
        }

        // Return original translation if we don't have one
        return $translation;
    }

    /**
     * Insert translation directly into Polylang database tables
     */
    public function insert_polylang_translation($string_name, $translation, $language) {
        global $wpdb;

        try {
            $strings_table = $wpdb->prefix . 'polylang_strings';
            $mlt_table = $wpdb->prefix . 'polylang_mlt';

            // First, check if string already exists
            $string_id = $wpdb->get_var($wpdb->prepare(
                "SELECT string_id FROM $strings_table WHERE name = %s",
                $string_name
            ));

            // If string doesn't exist, insert it
            if (!$string_id) {
                $result = $wpdb->insert(
                    $strings_table,
                    array(
                        'name' => $string_name,
                        'string' => $string_name,
                        'context' => 'alachiqgroup-pro',
                        'multiline' => 0
                    ),
                    array('%s', '%s', '%s', '%d')
                );

                if ($result === false) {
                    error_log("Auto Translator ERROR: Failed to insert string '$string_name' into Polylang strings table");
                    return false;
                }

                $string_id = $wpdb->insert_id;
                error_log("Auto Translator DEBUG: Inserted new string '$string_name' with ID $string_id");
            }

            // Now insert/update the translation
            // This is a simplified approach - in reality, Polylang's structure is more complex
            error_log("Auto Translator DEBUG: Would insert translation for string ID $string_id, language '$language': '$translation'");

            return true;

        } catch (Exception $e) {
            error_log("Auto Translator ERROR: Exception in insert_polylang_translation: " . $e->getMessage());
            return false;
        }
    }

    /**
     * AJAX: Get translation progress
     */
    public function ajax_get_translation_progress() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
                wp_send_json_error(__('Security check failed', 'alachiqgroup-pro'));
                return;
            }

            $progress = get_transient('alachiq_translation_progress');

            if ($progress === false) {
                $progress = array(
                    'current' => 0,
                    'total' => 0,
                    'percentage' => 0,
                    'status' => 'idle'
                );
            }

            wp_send_json_success($progress);
        } catch (Exception $e) {
            error_log('Auto Translator Progress Error: ' . $e->getMessage());
            wp_send_json_error(__('Error getting progress', 'alachiqgroup-pro'));
        }
    }

    /**
     * AJAX: Export strings to CSV
     */
    public function ajax_export_csv() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_GET['nonce'], 'alachiq_translator_nonce')) {
                wp_die(__('Security check failed', 'alachiqgroup-pro'));
                return;
            }

            if (!current_user_can('manage_options')) {
                wp_die(__('Insufficient permissions', 'alachiqgroup-pro'));
                return;
            }

            $this->export_to_csv();
        } catch (Exception $e) {
            error_log('Auto Translator Export Error: ' . $e->getMessage());
            wp_die(__('Export failed', 'alachiqgroup-pro'));
        }
    }

    /**
     * Export strings to CSV
     */
    public function export_to_csv() {
        $strings = $this->get_all_strings();

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="alachiqgroup-translations-' . date('Y-m-d') . '.csv"');

        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // CSV Header
        fputcsv($output, array('String Name', 'Context', 'Original (Azerbaijani)', 'English', 'Russian'));

        foreach ($strings as $string_data) {
            // Get existing translations if any
            $english_translation = $this->get_existing_translation($string_data['name'], 'en');
            $russian_translation = $this->get_existing_translation($string_data['name'], 'ru');

            fputcsv($output, array(
                $string_data['name'],
                $string_data['context'],
                $string_data['string'],
                $english_translation,
                $russian_translation
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * AJAX: Import CSV translations
     */
    public function ajax_import_csv() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
                wp_send_json_error(__('Security check failed', 'alachiqgroup-pro'));
                return;
            }

            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Insufficient permissions', 'alachiqgroup-pro'));
                return;
            }

            if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
                wp_send_json_error(__('No file uploaded or upload error', 'alachiqgroup-pro'));
                return;
            }

            $file_path = $_FILES['csv_file']['tmp_name'];
            $imported_count = $this->import_from_csv($file_path);

            if ($imported_count > 0) {
                wp_send_json_success(sprintf(
                    __('Successfully imported %d translations', 'alachiqgroup-pro'),
                    $imported_count
                ));
            } else {
                wp_send_json_error(__('No translations were imported', 'alachiqgroup-pro'));
            }
        } catch (Exception $e) {
            error_log('Auto Translator Import Error: ' . $e->getMessage());
            wp_send_json_error(__('Import failed: ', 'alachiqgroup-pro') . $e->getMessage());
        }
    }

    /**
     * Import translations from CSV
     */
    private function import_from_csv($file_path) {
        $imported_count = 0;

        if (($handle = fopen($file_path, 'r')) !== FALSE) {
            // Skip header row
            fgetcsv($handle);

            while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                if (count($data) >= 5) {
                    $string_name = $data[0];
                    $english_translation = trim($data[3]);
                    $russian_translation = trim($data[4]);

                    // Import English translation
                    if (!empty($english_translation)) {
                        if ($this->save_polylang_translation($string_name, $english_translation, 'en')) {
                            $imported_count++;
                        }
                    }

                    // Import Russian translation
                    if (!empty($russian_translation)) {
                        if ($this->save_polylang_translation($string_name, $russian_translation, 'ru')) {
                            $imported_count++;
                        }
                    }
                }
            }

            fclose($handle);
        }

        return $imported_count;
    }

    /**
     * Get existing translation for a string
     */
    private function get_existing_translation($string_name, $language) {
        if (!function_exists('PLL') || !class_exists('PLL_Admin_Strings')) {
            return '';
        }

        $strings = PLL_Admin_Strings::get_strings();

        foreach ($strings as $string) {
            if ($string['name'] === $string_name) {
                // For now, return empty string as we'll implement proper translation retrieval later
                // This method is mainly used for checking existing translations before overwriting
                return '';
            }
        }

        return '';
    }

    /**
     * Auto-setup basic languages for Polylang
     */
    private function auto_setup_languages() {
        if (!function_exists('PLL') || !PLL() || !PLL()->model) {
            return false;
        }

        // Check if languages already exist
        if (PLL()->model->has_languages()) {
            echo '<div class="notice notice-info"><p>Languages are already configured!</p></div>';
            return true;
        }

        // Basic language configuration
        $languages = array(
            array(
                'name' => 'Azərbaycan dili',
                'slug' => 'az',
                'locale' => 'az',
                'rtl' => 0,
                'flag' => 'az',
                'term_group' => 1
            ),
            array(
                'name' => 'English',
                'slug' => 'en',
                'locale' => 'en_US',
                'rtl' => 0,
                'flag' => 'us',
                'term_group' => 2
            ),
            array(
                'name' => 'Русский',
                'slug' => 'ru',
                'locale' => 'ru_RU',
                'rtl' => 0,
                'flag' => 'ru',
                'term_group' => 3
            )
        );

        try {
            // Add languages
            foreach ($languages as $lang_data) {
                // Create language term
                $term = wp_insert_term(
                    $lang_data['name'],
                    'language',
                    array(
                        'slug' => $lang_data['slug'],
                        'description' => $lang_data['locale']
                    )
                );

                if (!is_wp_error($term)) {
                    // Add language meta
                    add_term_meta($term['term_id'], 'locale', $lang_data['locale']);
                    add_term_meta($term['term_id'], 'rtl', $lang_data['rtl']);
                    add_term_meta($term['term_id'], 'flag_code', $lang_data['flag']);
                    add_term_meta($term['term_id'], 'term_group', $lang_data['term_group']);
                }
            }

            // Set default language
            update_option('polylang', array(
                'default_lang' => 'az',
                'rewrite' => 1,
                'hide_default' => 0,
                'force_lang' => 1,
                'redirect_lang' => 0,
                'media_support' => 1,
                'uninstall' => 0,
                'sync' => array()
            ));

            echo '<div class="notice notice-success"><p><strong>Success!</strong> Basic languages have been configured. Please refresh the page.</p></div>';
            echo '<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>';

            return true;
        } catch (Exception $e) {
            echo '<div class="notice notice-error"><p><strong>Error:</strong> Could not auto-configure languages. Please set them up manually in <a href="' . admin_url('admin.php?page=mlang') . '">Languages → Settings</a>.</p></div>';
            return false;
        }
    }

    /**
     * AJAX: Sync translations with Polylang
     */
    public function ajax_sync_translations() {
        error_log("Auto Translator DEBUG: Sync AJAX called with data: " . print_r($_POST, true));

        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
            error_log("Auto Translator ERROR: Nonce verification failed for sync");
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            error_log("Auto Translator ERROR: Insufficient permissions for sync");
            wp_send_json_error('Insufficient permissions');
            return;
        }

        error_log("Auto Translator DEBUG: Starting sync with Polylang");
        $synced_count = $this->sync_translations_with_polylang();

        wp_send_json_success(array(
            'message' => "Synced $synced_count translations with Polylang",
            'synced_count' => $synced_count
        ));
    }

    /**
     * AJAX: Diagnose Polylang setup
     */
    public function ajax_diagnose_polylang() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $diagnosis = $this->diagnose_polylang_setup();

        wp_send_json_success(array(
            'message' => 'Polylang diagnosis completed',
            'diagnosis' => $diagnosis
        ));
    }

    /**
     * Diagnose Polylang setup and configuration
     */
    public function diagnose_polylang_setup() {
        global $wpdb;

        $diagnosis = array();

        // Check if Polylang plugin is active
        $diagnosis['polylang_active'] = is_plugin_active('polylang/polylang.php') || is_plugin_active('polylang-pro/polylang.php');

        // Check Polylang functions
        $diagnosis['functions'] = array(
            'PLL' => function_exists('PLL'),
            'pll_register_string' => function_exists('pll_register_string'),
            'pll_set_string_translation' => function_exists('pll_set_string_translation'),
            'pll__' => function_exists('pll__'),
            'pll_e' => function_exists('pll_e'),
            'pll_current_language' => function_exists('pll_current_language'),
            'pll_the_languages' => function_exists('pll_the_languages')
        );

        // Check Polylang classes
        $diagnosis['classes'] = array(
            'PLL_Admin_Strings' => class_exists('PLL_Admin_Strings'),
            'PLL_Settings' => class_exists('PLL_Settings'),
            'PLL_Model' => class_exists('PLL_Model')
        );

        // Check database tables
        $tables = array('polylang_strings', 'polylang_mlt', 'polylang_mo');
        foreach ($tables as $table) {
            $full_table_name = $wpdb->prefix . $table;
            $diagnosis['tables'][$table] = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'") == $full_table_name;
        }

        // Check Polylang options
        $diagnosis['options'] = array(
            'polylang' => get_option('polylang'),
            'polylang_wpml_strings' => get_option('polylang_wpml_strings'),
            'widget_polylang' => get_option('widget_polylang')
        );

        // Check if string translation is enabled
        $polylang_options = get_option('polylang', array());
        $diagnosis['string_translation_enabled'] = isset($polylang_options['force_lang']) && $polylang_options['force_lang'] != 0;

        // Check our stored translations
        $diagnosis['our_translations'] = array(
            'en_count' => count($this->get_all_translations_for_language('en')),
            'ru_count' => count($this->get_all_translations_for_language('ru'))
        );

        // Try to get registered strings from Polylang
        if (class_exists('PLL_Admin_Strings')) {
            try {
                $pll_admin_strings = new PLL_Admin_Strings();
                if (method_exists($pll_admin_strings, 'get_strings')) {
                    $registered_strings = $pll_admin_strings->get_strings();
                    $diagnosis['polylang_registered_strings'] = count($registered_strings);
                } else {
                    $diagnosis['polylang_registered_strings'] = 'get_strings method not available';
                }
            } catch (Exception $e) {
                $diagnosis['polylang_registered_strings'] = 'Error: ' . $e->getMessage();
            }
        } else {
            $diagnosis['polylang_registered_strings'] = 'PLL_Admin_Strings class not available';
        }

        return $diagnosis;
    }

    /**
     * AJAX: Import translations directly to Polylang database
     */
    public function ajax_import_to_polylang_db() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'alachiq_translator_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        error_log("Auto Translator DEBUG: Starting direct Polylang database import");

        $result = $this->import_translations_to_polylang_database();

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => $result['message'],
                'imported_count' => $result['imported_count'],
                'details' => $result['details']
            ));
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Import translations directly to Polylang database tables
     */
    public function import_translations_to_polylang_database() {
        global $wpdb;

        error_log("Auto Translator DEBUG: Analyzing Polylang database structure");

        // First, analyze Polylang's database structure
        $analysis = $this->analyze_polylang_database();

        if (!$analysis['ready']) {
            return array(
                'success' => false,
                'message' => 'Polylang database not ready: ' . $analysis['error']
            );
        }

        error_log("Auto Translator DEBUG: Polylang database analysis complete");

        // Get our stored translations
        $languages = ['en', 'ru'];
        $imported_count = 0;
        $details = array();

        foreach ($languages as $language) {
            $translations = $this->get_all_translations_for_language($language);
            $lang_imported = 0;

            foreach ($translations as $string_name => $translation) {
                $result = $this->insert_translation_to_polylang_db($string_name, $translation, $language, $analysis);

                if ($result['success']) {
                    $imported_count++;
                    $lang_imported++;
                } else {
                    error_log("Auto Translator ERROR: Failed to import '$string_name' for '$language': " . $result['error']);
                }
            }

            $details[$language] = $lang_imported;
            error_log("Auto Translator DEBUG: Imported $lang_imported translations for language '$language'");
        }

        // Clear any Polylang caches
        $this->clear_polylang_caches();

        return array(
            'success' => true,
            'message' => "Successfully imported $imported_count translations to Polylang database",
            'imported_count' => $imported_count,
            'details' => $details
        );
    }

    /**
     * Create Polylang database tables if they don't exist
     */
    public function create_polylang_tables_if_missing() {
        global $wpdb;

        error_log("Auto Translator DEBUG: Checking if Polylang tables need to be created");

        // Check if polylang_strings table exists
        $strings_table = $wpdb->prefix . 'polylang_strings';
        if (!$wpdb->get_var("SHOW TABLES LIKE '$strings_table'")) {
            error_log("Auto Translator DEBUG: Creating polylang_strings table");

            $sql = "CREATE TABLE $strings_table (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                string longtext NOT NULL,
                context varchar(255) NOT NULL DEFAULT '',
                multiline tinyint(1) NOT NULL DEFAULT 0,
                PRIMARY KEY (id),
                KEY name (name),
                KEY context (context)
            ) {$wpdb->get_charset_collate()};";

            $result = $wpdb->query($sql);
            if ($result !== false) {
                error_log("Auto Translator DEBUG: polylang_strings table created successfully");
            } else {
                error_log("Auto Translator ERROR: Failed to create polylang_strings table: " . $wpdb->last_error);
            }
        }

        // Check if polylang_mlt table exists
        $mlt_table = $wpdb->prefix . 'polylang_mlt';
        if (!$wpdb->get_var("SHOW TABLES LIKE '$mlt_table'")) {
            error_log("Auto Translator DEBUG: Creating polylang_mlt table");

            $sql = "CREATE TABLE $mlt_table (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                object_id bigint(20) unsigned NOT NULL,
                term_id bigint(20) unsigned NOT NULL,
                object_type varchar(20) NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY object_id (object_id, term_id, object_type),
                KEY term_id (term_id)
            ) {$wpdb->get_charset_collate()};";

            $result = $wpdb->query($sql);
            if ($result !== false) {
                error_log("Auto Translator DEBUG: polylang_mlt table created successfully");
            } else {
                error_log("Auto Translator ERROR: Failed to create polylang_mlt table: " . $wpdb->last_error);
            }
        }

        // Check if polylang_mo table exists (optional, for .mo file caching)
        $mo_table = $wpdb->prefix . 'polylang_mo';
        if (!$wpdb->get_var("SHOW TABLES LIKE '$mo_table'")) {
            error_log("Auto Translator DEBUG: Creating polylang_mo table");

            $sql = "CREATE TABLE $mo_table (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                language varchar(10) NOT NULL,
                mo longblob NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY language (language)
            ) {$wpdb->get_charset_collate()};";

            $result = $wpdb->query($sql);
            if ($result !== false) {
                error_log("Auto Translator DEBUG: polylang_mo table created successfully");
            } else {
                error_log("Auto Translator ERROR: Failed to create polylang_mo table: " . $wpdb->last_error);
            }
        }
    }

    /**
     * Analyze Polylang database structure and get required information
     */
    public function analyze_polylang_database() {
        global $wpdb;

        $analysis = array(
            'ready' => false,
            'error' => '',
            'strings_table' => $wpdb->prefix . 'polylang_strings',
            'mlt_table' => $wpdb->prefix . 'polylang_mlt',
            'languages' => array()
        );

        // Debug: List all tables to see what exists
        $all_tables = $wpdb->get_results("SHOW TABLES", ARRAY_N);
        $table_names = array();
        foreach ($all_tables as $table) {
            $table_names[] = $table[0];
        }
        error_log("Auto Translator DEBUG: All database tables: " . implode(', ', $table_names));

        // Check if tables exist with better debugging
        $strings_table_check = $wpdb->get_var("SHOW TABLES LIKE '{$analysis['strings_table']}'");
        $mlt_table_check = $wpdb->get_var("SHOW TABLES LIKE '{$analysis['mlt_table']}'");

        error_log("Auto Translator DEBUG: Looking for strings table: {$analysis['strings_table']}");
        error_log("Auto Translator DEBUG: Strings table check result: " . ($strings_table_check ? $strings_table_check : 'NULL'));
        error_log("Auto Translator DEBUG: Looking for MLT table: {$analysis['mlt_table']}");
        error_log("Auto Translator DEBUG: MLT table check result: " . ($mlt_table_check ? $mlt_table_check : 'NULL'));

        $strings_table_exists = $strings_table_check == $analysis['strings_table'];
        $mlt_table_exists = $mlt_table_check == $analysis['mlt_table'];

        if (!$strings_table_exists) {
            // Check if table exists with different case or prefix
            $similar_tables = array();
            foreach ($table_names as $table_name) {
                if (stripos($table_name, 'polylang') !== false) {
                    $similar_tables[] = $table_name;
                }
            }
            $analysis['error'] = 'Polylang strings table does not exist. Similar tables found: ' . implode(', ', $similar_tables);
            error_log("Auto Translator DEBUG: " . $analysis['error']);
            return $analysis;
        }

        if (!$mlt_table_exists) {
            $analysis['error'] = 'Polylang MLT table does not exist';
            error_log("Auto Translator DEBUG: " . $analysis['error']);
            return $analysis;
        }

        // Get language information from Polylang
        if (function_exists('PLL')) {
            $pll = PLL();
            if ($pll && isset($pll->model) && method_exists($pll->model, 'get_languages_list')) {
                $languages = $pll->model->get_languages_list();
                foreach ($languages as $lang) {
                    $analysis['languages'][$lang->slug] = array(
                        'term_id' => $lang->term_id,
                        'name' => $lang->name,
                        'slug' => $lang->slug,
                        'locale' => $lang->locale
                    );
                }
            }
        }

        // If we couldn't get languages from PLL, try database query
        if (empty($analysis['languages'])) {
            $terms_table = $wpdb->prefix . 'terms';
            $term_taxonomy_table = $wpdb->prefix . 'term_taxonomy';

            $languages = $wpdb->get_results("
                SELECT t.term_id, t.name, t.slug
                FROM {$terms_table} t
                JOIN {$term_taxonomy_table} tt ON t.term_id = tt.term_id
                WHERE tt.taxonomy = 'language'
            ");

            foreach ($languages as $lang) {
                $analysis['languages'][$lang->slug] = array(
                    'term_id' => $lang->term_id,
                    'name' => $lang->name,
                    'slug' => $lang->slug,
                    'locale' => $lang->slug // fallback
                );
            }
        }

        if (empty($analysis['languages'])) {
            $analysis['error'] = 'No languages found in Polylang';
            return $analysis;
        }

        $analysis['ready'] = true;
        error_log("Auto Translator DEBUG: Found languages: " . implode(', ', array_keys($analysis['languages'])));

        return $analysis;
    }

    /**
     * Insert a single translation into Polylang database
     */
    public function insert_translation_to_polylang_db($string_name, $translation, $language, $analysis) {
        global $wpdb;

        // Check if language exists in analysis
        if (!isset($analysis['languages'][$language])) {
            return array(
                'success' => false,
                'error' => "Language '$language' not found in Polylang"
            );
        }

        $lang_info = $analysis['languages'][$language];
        $strings_table = $analysis['strings_table'];
        $mlt_table = $analysis['mlt_table'];

        // First, check if string already exists
        $existing_string = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$strings_table}
            WHERE name = %s AND context = %s
        ", $string_name, 'alachiqgroup-pro'));

        $string_id = null;

        if ($existing_string) {
            $string_id = $existing_string->id;
            error_log("Auto Translator DEBUG: Found existing string ID $string_id for '$string_name'");
        } else {
            // Insert new string
            $insert_result = $wpdb->insert(
                $strings_table,
                array(
                    'name' => $string_name,
                    'string' => $string_name, // Default value, can be the same as name
                    'context' => 'alachiqgroup-pro',
                    'multiline' => 0
                ),
                array('%s', '%s', '%s', '%d')
            );

            if ($insert_result === false) {
                return array(
                    'success' => false,
                    'error' => "Failed to insert string '$string_name': " . $wpdb->last_error
                );
            }

            $string_id = $wpdb->insert_id;
            error_log("Auto Translator DEBUG: Inserted new string ID $string_id for '$string_name'");
        }

        // Now insert/update the translation in MLT table
        $existing_translation = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$mlt_table}
            WHERE object_id = %d AND term_id = %d
        ", $string_id, $lang_info['term_id']));

        if ($existing_translation) {
            // Update existing translation
            $update_result = $wpdb->update(
                $mlt_table,
                array('description' => $translation),
                array(
                    'object_id' => $string_id,
                    'term_id' => $lang_info['term_id']
                ),
                array('%s'),
                array('%d', '%d')
            );

            if ($update_result === false) {
                return array(
                    'success' => false,
                    'error' => "Failed to update translation for '$string_name' in '$language': " . $wpdb->last_error
                );
            }

            error_log("Auto Translator DEBUG: Updated translation for '$string_name' in '$language'");
        } else {
            // Insert new translation
            $insert_result = $wpdb->insert(
                $mlt_table,
                array(
                    'object_id' => $string_id,
                    'term_id' => $lang_info['term_id'],
                    'description' => $translation
                ),
                array('%d', '%d', '%s')
            );

            if ($insert_result === false) {
                return array(
                    'success' => false,
                    'error' => "Failed to insert translation for '$string_name' in '$language': " . $wpdb->last_error
                );
            }

            error_log("Auto Translator DEBUG: Inserted translation for '$string_name' in '$language'");
        }

        return array('success' => true);
    }

    /**
     * Clear Polylang caches after import
     */
    public function clear_polylang_caches() {
        error_log("Auto Translator DEBUG: Clearing Polylang caches");

        // Clear WordPress object cache
        wp_cache_flush();

        // Clear Polylang specific caches if functions exist
        if (function_exists('pll_cache_delete')) {
            pll_cache_delete('strings');
            pll_cache_delete('languages');
            error_log("Auto Translator DEBUG: Cleared Polylang string and language caches");
        }

        // Clear any transients that might be related
        delete_transient('pll_languages_list');
        delete_transient('pll_strings_translations');

        error_log("Auto Translator DEBUG: Cache clearing complete");
    }

    /**
     * Initialize and get the auto translator instance
     */
    public static function initialize() {
        return self::get_instance();
    }
}

} // End class_exists check

// Initialize the Auto Translator
if (class_exists('Alachiqgroup_Pro_Auto_Translator')) {
    Alachiqgroup_Pro_Auto_Translator::initialize();
}

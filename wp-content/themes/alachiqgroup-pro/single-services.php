<?php get_header(); ?>

<main id="main-content" class="single-service-page">
    <?php while (have_posts()) : the_post(); ?>
        
        <!-- Service Hero Section -->
        <section class="service-hero">
            <div class="container">
                <div class="service-hero__content">
                    <div class="service-hero__breadcrumb">
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('services'))); ?>">← <span><?php pll_e_safe('Xidmətlər'); ?></span></a>
                    </div>
                    
                    <div class="service-hero__main">
                        <div class="service-hero__image">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                            <?php else : ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/placeholder-service.svg" alt="<?php echo esc_attr(get_the_title()); ?>" />
                            <?php endif; ?>
                        </div>
                        
                        <!-- Service Info -->
                        <div class="service-info">
                            <?php
                            $service_categories = get_the_terms(get_the_ID(), 'service_category');
                            if ($service_categories && !is_wp_error($service_categories)) : ?>
                                <div class="service-category">
                                    <?php echo esc_html($service_categories[0]->name); ?>
                                </div>
                            <?php endif; ?>
                            
                            <h1 class="service-title"><?php echo esc_html(get_the_title()); ?></h1>
                            
                            <div class="service-description">
                                <?php the_content(); ?>
                            </div>
                            
                            <?php
                            $service_price = get_post_meta(get_the_ID(), '_service_price', true);
                            $service_currency = get_post_meta(get_the_ID(), '_service_currency', true) ?: 'AZN';
                            $service_duration = get_post_meta(get_the_ID(), '_service_duration', true);
                            ?>
                            
                            <?php if ($service_price || $service_duration) : ?>
                                <div class="service-meta">
                                    <?php if ($service_price) : ?>
                                        <div class="service-price">
                                            <span class="price-label"><?php pll_e_safe('Qiymət:'); ?></span>
                                            <span class="price-value"><?php echo esc_html($service_price) . ' ' . esc_html($service_currency); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($service_duration) : ?>
                                        <div class="service-duration">
                                            <span class="duration-label"><?php pll_e_safe('Müddət:'); ?></span>
                                            <span class="duration-value"><?php echo esc_html($service_duration); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $service_features = get_post_meta(get_the_ID(), '_service_features', true);
                            if (!empty($service_features) && is_array($service_features)) : ?>
                                <div class="service-features">
                                    <h3><?php pll_e_safe('Xüsusiyyətlər:'); ?></h3>
                                    <ul>
                                        <?php foreach ($service_features as $feature) : ?>
                                            <li><?php echo esc_html($feature); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <div class="service-actions">
                                <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="service-btn service-btn--primary">
                                    <i class="fas fa-phone"></i>
                                    <span><?php pll_e_safe('Əlaqə Saxlayın'); ?></span>
                                </a>
                                <a href="tel:<?php echo esc_attr(str_replace(array(' ', '(', ')', '-'), '', get_theme_mod('company_phone', '+994125143906'))); ?>" class="service-btn service-btn--secondary">
                                    <i class="fas fa-phone-alt"></i>
                                    <span><?php pll_e_safe('Zəng Edin'); ?></span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Services Section -->
        <section class="related-services">
            <div class="container">
                <h2 class="section-title"><?php pll_e_safe('Digər Xidmətlər'); ?></h2>
                
                <div class="related-services-grid">
                    <?php
                    // Get current service categories
                    $current_categories = get_the_terms(get_the_ID(), 'service_category');
                    $category_ids = array();
                    if ($current_categories && !is_wp_error($current_categories)) {
                        $category_ids = wp_list_pluck($current_categories, 'term_id');
                    }
                    
                    // Query for related services
                    $related_services = new WP_Query(array(
                        'post_type' => 'services',
                        'posts_per_page' => 3,
                        'post_status' => 'publish',
                        'post__not_in' => array(get_the_ID()),
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'service_category',
                                'field' => 'term_id',
                                'terms' => $category_ids,
                            ),
                        ),
                        'orderby' => 'rand'
                    ));
                    
                    if ($related_services->have_posts()) :
                        while ($related_services->have_posts()) : $related_services->the_post();
                            $related_icon = get_post_meta(get_the_ID(), '_service_icon', true) ?: 'fas fa-cog';
                            $related_price = get_post_meta(get_the_ID(), '_service_price', true);
                            $related_currency = get_post_meta(get_the_ID(), '_service_currency', true) ?: 'AZN';
                    ?>
                        <article class="related-service-card">
                            <a href="<?php echo esc_url(get_permalink()); ?>">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="related-service-image">
                                        <?php the_post_thumbnail('medium', array('alt' => get_the_title())); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="related-service-content">
                                    <div class="related-service-icon">
                                        <i class="<?php echo esc_attr($related_icon); ?>"></i>
                                    </div>
                                    
                                    <h3 class="related-service-title"><?php echo esc_html(get_the_title()); ?></h3>
                                    
                                    <?php if ($related_price) : ?>
                                        <div class="related-service-price">
                                            <?php echo esc_html($related_price) . ' ' . esc_html($related_currency); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <p class="related-service-excerpt">
                                        <?php echo wp_trim_words(get_the_excerpt(), 10, '...'); ?>
                                    </p>
                                </div>
                            </a>
                        </article>
                    <?php 
                        endwhile;
                        wp_reset_postdata();
                    endif; ?>
                </div>
            </div>
        </section>
        
    <?php endwhile; ?>
</main>

<style>
/* Single Service Page Styles */
.single-service-page {
    background: var(--color-background);
    min-height: 100vh;
}

.service-hero {
    padding: var(--space-4xl) 0;
    background: var(--color-surface);
}

.service-hero__breadcrumb {
    margin-bottom: var(--space-xl);
}

.service-hero__breadcrumb a {
    color: var(--color-text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-base);
}

.service-hero__breadcrumb a:hover {
    color: var(--alachiq-primary);
}

.service-hero__main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4xl);
    align-items: start;
}

.service-hero__image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.service-category {
    display: inline-block;
    background: rgba(217, 196, 156, 0.2);
    color: var(--alachiq-secondary);
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--space-lg);
}

.service-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--space-lg);
    line-height: 1.2;
}

.service-description {
    color: var(--color-text-secondary);
    font-size: var(--font-size-base);
    line-height: 1.7;
    margin-bottom: var(--space-xl);
}

.service-meta {
    display: flex;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: rgba(217, 196, 156, 0.1);
    border-radius: var(--radius-lg);
}

.service-price,
.service-duration {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.price-label,
.duration-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    font-weight: 500;
}

.price-value,
.duration-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--alachiq-primary);
}

.service-features {
    margin-bottom: var(--space-xl);
}

.service-features h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--space-md);
}

.service-features ul {
    list-style: none;
    padding: 0;
}

.service-features li {
    padding: var(--space-sm) 0;
    border-bottom: 1px solid rgba(217, 196, 156, 0.2);
    position: relative;
    padding-left: var(--space-lg);
}

.service-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--alachiq-primary);
    font-weight: bold;
}

.service-actions {
    display: flex;
    gap: var(--space-md);
}

.service-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-base);
}

.service-btn--primary {
    background: var(--alachiq-primary);
    color: var(--alachiq-secondary);
}

.service-btn--primary:hover {
    background: var(--alachiq-secondary);
    color: var(--alachiq-primary);
    transform: translateY(-2px);
}

.service-btn--secondary {
    background: transparent;
    color: var(--alachiq-secondary);
    border: 2px solid var(--alachiq-primary);
}

.service-btn--secondary:hover {
    background: var(--alachiq-primary);
    color: var(--alachiq-secondary);
}

/* Related Services */
.related-services {
    padding: var(--space-4xl) 0;
    background: var(--color-surface);
}

.related-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
    margin-top: var(--space-xl);
}

.related-service-card {
    background: var(--color-background);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
}

.related-service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.related-service-image {
    height: 200px;
    overflow: hidden;
}

.related-service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-base);
}

.related-service-card:hover .related-service-image img {
    transform: scale(1.05);
}

.related-service-content {
    padding: var(--space-lg);
}

.related-service-icon {
    width: 50px;
    height: 50px;
    background: var(--alachiq-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-md);
}

.related-service-icon i {
    color: var(--alachiq-secondary);
    font-size: var(--font-size-lg);
}

.related-service-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--space-sm);
}

.related-service-price {
    font-size: var(--font-size-md);
    font-weight: 700;
    color: var(--alachiq-primary);
    margin-bottom: var(--space-sm);
}

.related-service-excerpt {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .service-hero__main {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }
    
    .service-meta {
        flex-direction: column;
        gap: var(--space-md);
    }
    
    .service-actions {
        flex-direction: column;
    }

    .related-services-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>

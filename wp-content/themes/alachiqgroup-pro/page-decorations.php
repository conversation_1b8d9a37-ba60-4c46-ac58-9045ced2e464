<?php get_header(); ?>

<!-- CLEAN BUTTON STYLING - CACHE BUSTED v2.0 -->
<style>
/* REMOVE ALL DEBUG COLORS - CLEAN PROFESSIONAL STYLING */
body {
  border: none !important;
}

.product-card, article {
  background: white !important;
  border: 1px solid rgba(115, 99, 86, 0.1) !important;
  display: flex !important;
  flex-direction: column !important;
  height: auto !important;
  min-height: 400px !important;
}

.product-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  padding: 1rem !important;
  height: auto !important;
  overflow: visible !important;
}

.product-image {
  flex-shrink: 0 !important;
  height: 200px !important;
  overflow: hidden !important;
}

.product-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}
/* FORCE PRODUCT ACTIONS BUTTONS - VERTICAL STACK */
.product-actions {
  display: flex !important;
  flex-direction: column !important;
  gap: 0.4rem !important;
  padding: 1rem !important;
  margin-top: auto !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  visibility: visible !important;
  opacity: 1 !important;
  border: none !important;
  background: transparent !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 10 !important;
}

/* BEAUTIFUL PRIMARY BUTTON */
.decorations-page .product-actions .btn-primary,
.decorations-page .product-card .btn-primary,
.product-actions a.btn-primary,
.product-card a.btn-primary,
a.btn-primary {
  width: 100% !important;
  background: linear-gradient(135deg, #d9c49c 0%, #d9b79a 100%) !important;
  color: #403124 !important;
  padding: 0.6rem 1rem !important;
  border: 1px solid #d9c49c !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 0.85rem !important;
  text-decoration: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.4rem !important;
  transition: all 0.3s ease !important;
  min-height: 38px !important;
  box-sizing: border-box !important;
  white-space: nowrap !important;
  box-shadow: 0 2px 8px rgba(217, 196, 156, 0.3) !important;
}

.product-actions .btn-primary i {
  font-size: 0.8rem !important;
}

/* BEAUTIFUL SECONDARY BUTTON */
.decorations-page .product-actions .btn-secondary,
.decorations-page .product-card .btn-secondary,
.product-actions a.btn-secondary,
.product-card a.btn-secondary,
a.btn-secondary {
  width: 100% !important;
  background: #f2ece4 !important;
  color: #403124 !important;
  padding: 0.6rem 1rem !important;
  border: 1px solid #736356 !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 0.85rem !important;
  text-decoration: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.4rem !important;
  transition: all 0.3s ease !important;
  min-height: 38px !important;
  box-sizing: border-box !important;
  white-space: nowrap !important;
  box-shadow: 0 2px 6px rgba(115, 99, 86, 0.2) !important;
}

.product-actions .btn-secondary i {
  font-size: 0.8rem !important;
}

/* BEAUTIFUL HOVER EFFECTS */
.product-actions .btn-primary:hover,
a.btn-primary:hover {
  background: linear-gradient(135deg, #403124 0%, #736356 100%) !important;
  color: #f2ece4 !important;
  border-color: #403124 !important;
  text-decoration: none !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(64, 49, 36, 0.4) !important;
}

.product-actions .btn-secondary:hover,
a.btn-secondary:hover {
  background: linear-gradient(135deg, #736356 0%, #403124 100%) !important;
  color: #f2ece4 !important;
  border-color: #403124 !important;
  text-decoration: none !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(115, 99, 86, 0.4) !important;
}

/* NO RESULTS MESSAGE STYLING */
.no-results-message {
  grid-column: 1 / -1 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 400px !important;
  padding: 3rem !important;
  background: white !important;
  border-radius: 16px !important;
  border: 2px dashed rgba(217, 196, 156, 0.3) !important;
  margin: 2rem 0 !important;
}

.no-results-content {
  text-align: center !important;
  max-width: 500px !important;
  padding: 2rem !important;
}

.no-results-content i {
  font-size: 4rem !important;
  color: #d9c49c !important;
  margin-bottom: 1.5rem !important;
  opacity: 0.7 !important;
}

.no-results-content h3 {
  font-size: 1.8rem !important;
  font-weight: 700 !important;
  color: #403124 !important;
  margin-bottom: 1rem !important;
  line-height: 1.3 !important;
}

.no-results-content p {
  font-size: 1.1rem !important;
  color: #736356 !important;
  margin-bottom: 2rem !important;
  line-height: 1.6 !important;
}

.no-results-content .btn-primary {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.8rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.no-results-content .btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(217, 196, 156, 0.4) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .no-results-message {
    min-height: 300px !important;
    padding: 1.5rem !important;
    margin: 1.5rem 0 !important;
  }

  .no-results-content {
    padding: 1.5rem !important;
  }

  .no-results-content i {
    font-size: 3rem !important;
  }

  .no-results-content h3 {
    font-size: 1.5rem !important;
  }

  .no-results-content p {
    font-size: 1rem !important;
  }
}
</style>

<!-- E-commerce Hero Section -->
<section class="ecommerce-hero">
        <div class="container">
        <div class="ecommerce-hero__content">
            <div class="ecommerce-hero__badge">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 8px;">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
                <span><?php pll_e_safe('Professional Decoration Services'); ?></span>
                </div>
            <h1 class="ecommerce-hero__title"><?php pll_e_safe('Dekorasiya Məhsulları'); ?></h1>
            <p class="ecommerce-hero__subtitle">
                <?php pll_e_safe('Evinizi və ofisinizi gözəlləşdirmək üçün professional dekorasiya xidmətlərimizi kəşf edin.'); ?>
                <?php pll_e_safe('Hər bir məhsul keyfiyyət və yaradıcılıqla hazırlanıb.'); ?>
                </p>
            <div class="ecommerce-hero__actions">
                <a href="#products-grid" class="hero-cta-primary"><?php pll_e_safe('Məhsulları Kəşf Et'); ?></a>
                <a href="tel:<?php echo esc_attr(get_theme_mod('company_phone', '(+994 12) 514 39 06')); ?>" class="hero-cta-secondary">
                    <i class="fas fa-phone"></i>
                    <span><?php pll_e_safe('Məsləhət Al'); ?></span>
                </a>
                </div>
            </div>
        </div>
    </section>

<!-- Filters and Search Section -->
<section class="ecommerce-filters">
        <div class="container">
        <div class="filters-container">
            <div class="search-box">
                <input type="text" id="product-search" placeholder="<?php pll_e_safe('Məhsul axtarın...'); ?>" />
            </div>
            <div class="category-filters">
                <?php
                $categories = get_terms(array(
                    'taxonomy' => 'product_category',
                    'hide_empty' => false,
                ));
                
                if (!empty($categories) && !is_wp_error($categories)) {
                    foreach ($categories as $category) {
                        $translated_category = alachiqgroup_pro_get_translated_term($category, 'product_category');
                        echo '<button type="button" class="category-filter" data-category="' . esc_attr($category->slug) . '">' . esc_html($translated_category->name) . '</button>';
                    }
                }
                ?>
                    </div>
                    </div>
                </div>
</section>

<!-- Products Section -->
<main id="main-content" class="ecommerce-products">
    <div class="container">
        <div class="products-header">
            <h2 class="products-title"><?php pll_e_safe('Dekorasiya Məhsullarımız'); ?></h2>
            <p class="products-subtitle">
                <?php pll_e_safe('Hər bir məhsul keyfiyyətli materiallardan və professional dizaynla hazırlanıb. Evinizi daha rahat və gözəl etmək üçün xidmətinizdəyik.'); ?>
            </p>
                </div>

        <div class="products-grid" id="products-grid">
            <?php
            // Query for decoration products
            $products_query = new WP_Query(array(
                'post_type' => 'decoration_products',
                'posts_per_page' => -1,
                'post_status' => 'publish',
                'orderby' => 'date',
                'order' => 'DESC'
            ));

            // DEBUG: Check if we have posts
            echo '<!-- DEBUG: Products query has posts: ' . ($products_query->have_posts() ? 'YES' : 'NO') . ' -->';
            echo '<!-- DEBUG: Post count: ' . $products_query->found_posts . ' -->';

            if ($products_query->have_posts()) {
                while ($products_query->have_posts()) {
                    $products_query->the_post();
                    
                    // Get product meta
                    $product_price = get_post_meta(get_the_ID(), '_product_price', true);
                    $product_currency = get_post_meta(get_the_ID(), '_product_currency', true) ?: 'AZN';
                    $product_duration = get_post_meta(get_the_ID(), '_product_duration', true);
                    $product_features = get_post_meta(get_the_ID(), '_product_features', true);
                    $product_gallery = get_post_meta(get_the_ID(), '_product_gallery', true);
                    
                    // Get product categories
                    $product_categories = get_the_terms(get_the_ID(), 'product_category');
                    $category_names = array();
                    if ($product_categories && !is_wp_error($product_categories)) {
                        foreach ($product_categories as $category) {
                            $category_names[] = $category->name;
                        }
                    }
                    $category_class = $product_categories && !is_wp_error($product_categories) ? $product_categories[0]->slug : '';
                    ?>
                    
                    <article class="product-card" data-category="<?php echo esc_attr($category_class); ?>" data-categories="<?php echo esc_attr(implode(',', array_map(function($cat) { return $cat->slug; }, $product_categories ?: array()))); ?>">
                        <div class="product-image">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('alt' => get_the_title())); ?>
                            <?php else : ?>
                                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/placeholder-product.svg" alt="<?php echo esc_attr(get_the_title()); ?>" />
                            <?php endif; ?>
                            
                            <?php if ($product_price) : ?>
                                <div class="product-badge">
                                    <?php echo esc_html($product_price) . ' ' . esc_html($product_currency); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-content">
                            <?php if (!empty($category_names)) : ?>
                                <div class="product-category"><?php echo esc_html($category_names[0]); ?></div>
                            <?php endif; ?>
                            
                            <h3 class="product-title"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title(get_the_ID())); ?></h3>
                            
                            <div class="product-description">
                                <?php
                                $translated_excerpt = alachiqgroup_pro_get_translated_post_content(get_the_ID(), 'excerpt');
                                if (!empty($translated_excerpt)) {
                                    echo wp_trim_words($translated_excerpt, 20);
                                } else {
                                    echo wp_trim_words(get_the_excerpt() ?: get_the_content(), 20);
                                }
                                ?>
                </div>

                            <?php if ($product_features) : ?>
                                <ul class="product-features">
                                    <?php
                                    $features = explode("\n", $product_features);
                                    $feature_count = 0;
                                    foreach ($features as $feature) {
                                        $feature = trim($feature);
                                        if (!empty($feature) && $feature_count < 3) {
                                            echo '<li>' . esc_html($feature) . '</li>';
                                            $feature_count++;
                                        }
                                    }
                                    ?>
                        </ul>
                            <?php endif; ?>
                            
                            <div class="product-meta">
                                <?php if ($product_price) : ?>
                                    <div class="product-price">
                                        <?php echo esc_html($product_price) . ' ' . esc_html($product_currency); ?>
                    </div>
                                <?php endif; ?>

                                <?php if ($product_duration) : ?>
                                    <div class="product-duration">
                                        <?php echo esc_html($product_duration); ?>
                        </div>
                                <?php endif; ?>
                </div>

                            <div class="product-actions">
                                <a href="<?php the_permalink(); ?>" class="btn-primary">
                                    <i class="fas fa-eye"></i>
                                    <span><?php pll_e_safe('Ətraflı Bax'); ?></span>
                                </a>
                                <a href="tel:<?php echo esc_attr(get_theme_mod('company_phone', '(+994 12) 514 39 06')); ?>" class="btn-secondary">
                                    <i class="fas fa-phone"></i>
                                    <span><?php pll_e_safe('Zəng Et'); ?></span>
                                </a>
                            </div>
                        </div>
                    </article>
                    
                    <?php
                }
                wp_reset_postdata();
            } else {
                // DEBUG: No real products found, showing sample products
                echo '<!-- DEBUG: ' . pll___safe('No decoration_products found, showing sample products') . ' -->';

                // Show sample products for demonstration
                $sample_products = array(
                    array(
                        'title' => pll___safe('Modern Divan Dizaynı'),
                        'category' => pll___safe('Mebel'),
                        'price' => '1200',
                        'duration' => pll___safe('2-3 həftə'),
                        'description' => pll___safe('Modern və rahat divan dizaynı. Keyfiyyətli materiallardan hazırlanıb.'),
                        'features' => array(pll___safe('Keyfiyyətli material'), pll___safe('Modern dizayn'), pll___safe('Rahat oturacaq'))
                    ),
                    array(
                        'title' => pll___safe('Klassik Masalar'),
                        'category' => pll___safe('Mebel'),
                        'price' => '800',
                        'duration' => pll___safe('1-2 həftə'),
                        'description' => pll___safe('Klassik stil masalar. Ev və ofis üçün ideal.'),
                        'features' => array(pll___safe('Klassik stil'), pll___safe('Dayanıqlı material'), pll___safe('Çox funksiyalı'))
                    ),
                    array(
                        'title' => pll___safe('Dekorativ Divarlar'),
                        'category' => pll___safe('Dekorasiya'),
                        'price' => '500',
                        'duration' => pll___safe('3-5 gün'),
                        'description' => pll___safe('Dekorativ divar kağızları və panel sistemləri.'),
                        'features' => array(pll___safe('Müasir dizayn'), pll___safe('Asan quraşdırma'), pll___safe('Uzunömürlü'))
                    ),
                    array(
                        'title' => pll___safe('İşıqlandırma Sistemləri'),
                        'category' => pll___safe('İşıqlandırma'),
                        'price' => '1500',
                        'duration' => pll___safe('1 həftə'),
                        'description' => pll___safe('Professional işıqlandırma sistemləri və LED həllər.'),
                        'features' => array(pll___safe('LED texnologiya'), pll___safe('Enerji qənaətcil'), pll___safe('Uzaktan idarə'))
                    ),
                    array(
                        'title' => pll___safe('Pəncərə Dekorasiyaları'),
                        'category' => pll___safe('Dekorasiya'),
                        'price' => '300',
                        'duration' => pll___safe('2-3 gün'),
                        'description' => pll___safe('Pəncərələr üçün dekorativ pərdələr və jalüzlər.'),
                        'features' => array(pll___safe('Müxtəlif rənglər'), pll___safe('Asan təmizlik'), pll___safe('UV qorunması'))
                    ),
                    array(
                        'title' => pll___safe('Zərif Mətbəx Dizaynı'),
                        'category' => pll___safe('Mətbəx'),
                        'price' => '2500',
                        'duration' => pll___safe('3-4 həftə'),
                        'description' => pll___safe('Tam mətbəx dizaynı və quraşdırma xidməti.'),
                        'features' => array(pll___safe('Tam dizayn'), pll___safe('Keyfiyyətli material'), pll___safe('Professional quraşdırma'))
                    )
                );
                
                foreach ($sample_products as $index => $product) {
                    ?>
                    <article class="product-card" data-category="<?php echo esc_attr(strtolower($product['category'])); ?>">
                        <div class="product-image">
                            <img src="https://via.placeholder.com/400x300/D9C49C/403124?text=<?php echo urlencode($product['title']); ?>" alt="<?php echo esc_attr($product['title']); ?>" />
                            <div class="product-badge">
                                <?php echo esc_html($product['price']); ?> AZN
                            </div>
                        </div>
                        
                        <div class="product-content">
                            <div class="product-category"><?php echo esc_html($product['category']); ?></div>
                            
                            <h3 class="product-title"><?php echo esc_html($product['title']); ?></h3>
                            
                            <div class="product-description">
                                <?php echo esc_html($product['description']); ?>
                            </div>
                            
                            <ul class="product-features">
                                <?php foreach ($product['features'] as $feature) : ?>
                                    <li><?php echo esc_html($feature); ?></li>
                                <?php endforeach; ?>
                            </ul>
                            
                            <div class="product-meta">
                                <div class="product-price">
                                    <?php echo esc_html($product['price']); ?> AZN
                                </div>
                                <div class="product-duration">
                                    <?php echo esc_html($product['duration']); ?>
                                </div>
                            </div>
                            
                            <div class="product-actions">
                                <a href="#" class="btn-primary">
                                    <i class="fas fa-eye"></i>
                                    <?php pll_e_safe('Ətraflı Bax'); ?>
                                </a>
                                <a href="tel:<?php echo esc_attr(get_theme_mod('company_phone', '(+994 12) 514 39 06')); ?>" class="btn-secondary">
                                    <i class="fas fa-phone"></i>
                                    <?php pll_e_safe('Zəng Et'); ?>
                                </a>
                            </div>
                        </div>
                    </article>
                    <?php
                }
            }
            ?>
            </div>
        </div>
</main>

<!-- Back to Top Button -->
<button id="back-to-top" class="back-to-top-btn" aria-label="<?php pll_e_safe('Yuxarı qayıt'); ?>">
    <i class="fas fa-chevron-up"></i>
</button>

<style>

.no-products {
    background: white;
    border-radius: 1rem;
    padding: 3rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.no-products h3 {
    color: var(--alachiq-secondary);
    margin-bottom: 1rem;
}

.no-products p {
    color: var(--alachiq-neutral-dark);
}

/* Enhanced Empty State Styles */
.no-results-message {
    grid-column: 1 / -1;
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    padding: var(--space-3xl);
    text-align: center;
    margin: var(--space-xl) 0;
}

.no-results-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-results-content i {
    font-size: 4rem;
    color: var(--alachiq-primary);
    margin-bottom: var(--space-lg);
    opacity: 0.7;
}

.no-results-content h3 {
    color: var(--alachiq-secondary);
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-md);
}

.no-results-content p {
    color: var(--alachiq-neutral-dark);
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin-bottom: var(--space-xl);
}

.no-results-content .btn-primary {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    font-weight: 600;
    transition: all var(--transition-base);
}

.no-results-content .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

/* Animation for empty state */
.no-results-message {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


</style>



<?php get_footer(); ?>

<script>
// Main functionality script - runs after footer so jQuery is available
(function() {
    // Check if jQuery is available
    if (typeof jQuery === 'undefined') {
        console.error('jQuery is not available');
        return;
    }
    
    jQuery(document).ready(function($) {
        console.log('Main script loaded');
        console.log('Product cards found:', $('.product-card').length);
        console.log('Category filters found:', $('.category-filter').length);
        console.log('Search input found:', $('#product-search').length);
        
        // Search functionality
        $('#product-search').on('input', function() {
            console.log('Search input changed');
            var searchTerm = $(this).val().toLowerCase();
            var visibleCount = 0;
            
            $('.product-card').each(function() {
                var title = $(this).find('.product-title').text().toLowerCase();
                var description = $(this).find('.product-description').text().toLowerCase();
                var category = $(this).find('.product-category').text().toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm) || category.includes(searchTerm)) {
                    $(this).show();
                    visibleCount++;
                } else {
                    $(this).hide();
                }
            });
            
            console.log('Search completed, visible products:', visibleCount);
            
            // Show no results message if no products found
            if (visibleCount === 0 && searchTerm.length > 0) {
                if ($('.no-results-message').length === 0) {
                    $('.products-grid').append('<div class="no-results-message"><div class="no-results-content"><i class="fas fa-search"></i><h3><?php pll_e_safe('Axtarış nəticəsi tapılmadı'); ?></h3><p><?php pll_e_safe('Başqa açar sözlərlə axtarın.'); ?></p><button class="btn-primary clear-search-btn"><?php pll_e_safe('Axtarışı Təmizlə'); ?></button></div></div>');
                }
            } else {
                $('.no-results-message').remove();
            }
        });
        
        // Category filtering
        $('.category-filter').on('click', function(e) {
            console.log('Category filter clicked:', $(this).data('category'));
            $('.category-filter').removeClass('active');
            $(this).addClass('active');
            
            var category = $(this).data('category');
            
            if (category === 'all') {
                $('.product-card').show();
                $('.no-results-message').remove();
                console.log('Showing all products');
            } else {
                $('.product-card').hide();
                var matchingProducts = $('.product-card[data-category="' + category + '"]');
                matchingProducts.show();
                console.log('Found', matchingProducts.length, 'products for category:', category);
                
                // Show no results message if no products found
                if (matchingProducts.length === 0) {
                    if ($('.no-results-message').length === 0) {
                        $('.products-grid').append('<div class="no-results-message"><div class="no-results-content"><i class="fas fa-search"></i><h3><?php pll_e_safe('Bu kateqoriyada məhsul tapılmadı'); ?></h3><p><?php pll_e_safe('Başqa kateqoriya seçin və ya bütün məhsulları görün.'); ?></p><button class="btn-primary show-all-btn"><?php pll_e_safe('Hamısını Göstər'); ?></button></div></div>');
                    }
                } else {
                    $('.no-results-message').remove();
                }
            }
        });
        
        // Handle "Show All" button click
        $(document).on('click', '.show-all-btn', function() {
            console.log('Show all button clicked');
            $('.category-filter[data-category="all"]').click();
        });
        
        // Handle "Clear Search" button click
        $(document).on('click', '.clear-search-btn', function() {
            console.log('Clear search button clicked');
            $('#product-search').val('');
            $('.product-card').show();
            $('.no-results-message').remove();
        });
        
        // Product card click - navigate to product page
        $('.product-card').on('click', function(e) {
            // Don't navigate if clicking on action buttons
            if (!$(e.target).closest('.product-actions').length) {
                var productUrl = $(this).find('.btn-primary').attr('href');
                if (productUrl) {
                    window.location.href = productUrl;
                }
            }
        });
        
        // Back to top functionality
        var backToTopBtn = $('#back-to-top');
        
        $(window).on('scroll', function() {
            if ($(window).scrollTop() > 300) {
                backToTopBtn.addClass('visible');
            } else {
                backToTopBtn.removeClass('visible');
            }
        });
        
        backToTopBtn.on('click', function() {
            $('html, body').animate({
                scrollTop: 0
            }, 800);
        });
        
        // Smooth scroll for hero CTA
        $('.hero-cta-primary').on('click', function(e) {
            e.preventDefault();
            var target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
        
        // Enhanced loading states for filters
        $('.category-filter').on('click', function() {
            var $this = $(this);
            $this.addClass('loading');
            
            setTimeout(function() {
                $this.removeClass('loading');
            }, 500);
        });
        
        // Enhanced search with loading state
        var searchTimeout;
        $('#product-search').on('input', function() {
            var $this = $(this);
            clearTimeout(searchTimeout);
            
            searchTimeout = setTimeout(function() {
                // Add loading state to products
                $('.product-card').addClass('loading');
                
                setTimeout(function() {
                    $('.product-card').removeClass('loading');
                }, 300);
            }, 300);
        });
        


    });
})();
</script>
<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php, front-page.php, etc. exist.
 *
 * @package Alachiq Group Pro
 * @since 1.0.0
 */

get_header(); ?>

<main class="alachiq-main-content">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="alachiq-content-area">
                    <?php if (have_posts()) : ?>
                        
                        <header class="page-header">
                            <h1 class="page-title">
                                <?php
                                if (is_home() && !is_front_page()) {
                                    single_post_title();
                                } elseif (is_archive()) {
                                    the_archive_title();
                                } elseif (is_search()) {
                                    printf(pll___safe('Axtarış nəticələri: %s'), get_search_query());
                                } else {
                                    pll_e_safe('<PERSON>');
                                }
                                ?>
                            </h1>
                            <?php if (is_archive()) : ?>
                                <div class="archive-description">
                                    <?php the_archive_description(); ?>
                                </div>
                            <?php endif; ?>
                        </header>

                        <div class="alachiq-posts-grid">
                            <?php while (have_posts()) : the_post(); ?>
                                <article id="post-<?php the_ID(); ?>" <?php post_class('alachiq-post-card'); ?>>
                                    
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="alachiq-post-card__image">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('medium', array('class' => 'img-fluid')); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    <div class="alachiq-post-card__content">
                                        <header class="alachiq-post-card__header">
                                            <h2 class="alachiq-post-card__title">
                                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                            </h2>
                                            
                                            <div class="alachiq-post-card__meta">
                                                <span class="alachiq-post-card__date">
                                                    <i class="fas fa-calendar-alt"></i>
                                                    <?php echo get_the_date(); ?>
                                                </span>
                                                
                                                <?php if (get_the_category()) : ?>
                                                    <span class="alachiq-post-card__category">
                                                        <i class="fas fa-folder"></i>
                                                        <?php the_category(', '); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </header>

                                        <div class="alachiq-post-card__excerpt">
                                            <?php 
                                            if (has_excerpt()) {
                                                the_excerpt();
                                            } else {
                                                echo wp_trim_words(get_the_content(), 25, '...');
                                            }
                                            ?>
                                        </div>

                                        <footer class="alachiq-post-card__footer">
                                            <a href="<?php the_permalink(); ?>" class="btn btn-outline-primary">
                                                <?php pll_e_safe('Daha çox oxu'); ?>
                                                <i class="fas fa-arrow-right"></i>
                                            </a>
                                        </footer>
                                    </div>
                                </article>
                            <?php endwhile; ?>
                        </div>

                        <?php
                        // Pagination
                        the_posts_pagination(array(
                            'mid_size' => 2,
                            'prev_text' => '<i class="fas fa-chevron-left"></i> ' . pll___safe('Əvvəlki'),
                            'next_text' => pll___safe('Növbəti') . ' <i class="fas fa-chevron-right"></i>',
                            'class' => 'alachiq-pagination'
                        ));
                        ?>

                    <?php else : ?>
                        
                        <div class="alachiq-no-content">
                            <div class="alachiq-no-content__icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h2><?php pll_e_safe('Heç bir məqalə tapılmadı'); ?></h2>
                            <p><?php pll_e_safe('Təəssüf ki, axtardığınız məzmun tapılmadı. Başqa açar sözlərlə axtarış etməyi cəhd edin.'); ?></p>
                            
                            <div class="alachiq-search-form">
                                <?php get_search_form(); ?>
                            </div>
                        </div>

                    <?php endif; ?>
                </div>
            </div>

            <div class="col-lg-4">
                <aside class="alachiq-sidebar">
                    <?php
                    if (is_active_sidebar('sidebar-1')) {
                        dynamic_sidebar('sidebar-1');
                    } else {
                        // Default sidebar content if no widgets are added
                        ?>
                        <div class="widget">
                            <h3 class="widget-title"><?php pll_e_safe('Haqqımızda'); ?></h3>
                            <p><?php pll_e_safe('Alachiq Group - Azərbaycanda tikinti və dizayn sahəsində fəaliyyət göstərən aparıcı şirkət.'); ?></p>
                        </div>
                        
                        <div class="widget">
                            <h3 class="widget-title"><?php pll_e_safe('Əlaqə'); ?></h3>
                            <p>
                                <strong><?php pll_e_safe('Telefon:'); ?></strong><br>
                                <a href="tel:+994503701522">+994 50 370 15 22</a>
                            </p>
                            <p>
                                <strong><?php pll_e_safe('Email:'); ?></strong><br>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </p>
                        </div>
                        <?php
                    }
                    ?>
                </aside>
            </div>
        </div>
    </div>
</main>

<style>
/* Index page specific styles */
.alachiq-main-content {
    padding: 4rem 0;
    background: var(--alachiq-background, #f8f9fa);
}

.page-header {
    margin-bottom: 3rem;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--alachiq-text-primary, #333);
    margin-bottom: 1rem;
}

.archive-description {
    color: var(--alachiq-text-secondary, #666);
    font-size: 1.1rem;
}

.alachiq-posts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.alachiq-post-card {
    background: var(--alachiq-surface, #fff);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.alachiq-post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.alachiq-post-card__image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.alachiq-post-card__content {
    padding: 1.5rem;
}

.alachiq-post-card__title a {
    color: var(--alachiq-text-primary, #333);
    text-decoration: none;
    font-weight: 600;
}

.alachiq-post-card__meta {
    display: flex;
    gap: 1rem;
    margin: 0.5rem 0 1rem;
    font-size: 0.9rem;
    color: var(--alachiq-text-secondary, #666);
}

.alachiq-no-content {
    text-align: center;
    padding: 4rem 2rem;
}

.alachiq-no-content__icon {
    font-size: 4rem;
    color: var(--alachiq-primary, #d9c49c);
    margin-bottom: 1rem;
}

.alachiq-sidebar {
    padding-left: 2rem;
}

.widget {
    background: var(--alachiq-surface, #fff);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.widget-title {
    color: var(--alachiq-text-primary, #333);
    margin-bottom: 1rem;
    font-weight: 600;
}

@media (max-width: 768px) {
    .alachiq-sidebar {
        padding-left: 0;
        margin-top: 2rem;
    }
}
</style>

<?php get_footer(); ?>

<?php
/**
 * Front Page Template
 * Unique homepage design for Alachiq Group Pro
 * Taking inspiration from alovinsaat while maintaining theme harmony
 */

get_header(); ?>

<!-- Modern Construction Hero Section -->
<section class="construction-hero">
    <!-- Hero Background -->
    <div class="hero-background">
        <div class="hero-image">
            <img src="<?php echo get_template_directory_uri(); ?>/img/sla4.jpg" alt="Alachiq Group Construction">
        </div>
        <div class="hero-overlay"></div>
        <div class="construction-pattern"></div>
    </div>
    <!-- Hero Content -->
    <div class="hero-content">
        <div class="container">
            <div class="row align-items-center">
                <!-- Left Side: Static Content -->
                <div class="col-lg-6">
                    <div class="hero-text-content">
                        <!-- Hero Badge -->
                        <div class="hero-badge" data-aos="fade-right">
                            <i class="fas fa-hard-hat"></i>
                            <span><?php pll_e_safe('2010-dan bəri pe<PERSON>ə<PERSON> xidmət'); ?></span>
                        </div>

                        <!-- Hero Title -->
                        <h1 class="hero-title" data-aos="fade-right" data-aos-delay="200">
                            <?php _e('Azərbaycanın Ən Böyük <span class="title-highlight">Tikinti Şirkəti</span>', 'alachiqgroup-pro'); ?>
                        </h1>

                        <!-- Hero Subtitle -->
                        <p class="hero-subtitle" data-aos="fade-right" data-aos-delay="400">
                            <?php pll_e_safe('Villa tikintisindən tutmuş böyük tikinti layihələrinə qədər - bütün tikinti ehtiyaclarınız üçün tək ünvan'); ?>
                        </p>

                        <!-- Hero Actions -->
                        <div class="hero-actions" data-aos="fade-right" data-aos-delay="600">
                            <a href="<?php echo home_url('/layiheler/'); ?>" class="hero-btn hero-btn--primary">
                                <span><?php pll_e_safe('Layihələrimizi Görün'); ?></span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <a href="<?php echo home_url('/elaqe/'); ?>" class="hero-btn hero-btn--secondary">
                                <span><?php pll_e_safe('Pulsuz Məsləhət'); ?></span>
                                <i class="fas fa-phone"></i>
                            </a>
                        </div>

                        <!-- Hero Stats -->
                        <div class="hero-stats-inline" data-aos="fade-right" data-aos-delay="800">
                            <div class="stat-inline-item">
                                <div class="stat-number">300+</div>
                                <div class="stat-label"><?php pll_e_safe('Layihə'); ?></div>
                            </div>
                            <div class="stat-inline-item">
                                <div class="stat-number">15+</div>
                                <div class="stat-label"><?php pll_e_safe('İl Təcrübə'); ?></div>
                            </div>
                            <div class="stat-inline-item">
                                <div class="stat-number">100%</div>
                                <div class="stat-label"><?php pll_e_safe('Keyfiyyət'); ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Carousel -->
                <div class="col-lg-6">
                    <div class="hero-carousel-container" data-aos="fade-left" data-aos-delay="400">
                        <div class="hero-carousel">
                            <div class="carousel-slide active">
                                <div class="slide-content">
                                    <div class="slide-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/villa.jpg" alt="Villa Tikintisi">
                                    </div>
                                    <div class="slide-overlay">
                                        <div class="slide-info">
                                            <h3><?php pll_e_safe('Villa Tikintisi'); ?></h3>
                                            <p><?php pll_e_safe('Müasir villa layihələri'); ?></p>
                                            <div class="slide-icon">
                                                <i class="fas fa-home"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="carousel-slide">
                                <div class="slide-content">
                                    <div class="slide-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/1.jpg" alt="Tikinti Xidmətləri">
                                    </div>
                                    <div class="slide-overlay">
                                        <div class="slide-info">
                                            <h3><?php pll_e_safe('Tikinti & Təmir'); ?></h3>
                                            <p><?php pll_e_safe('Peşəkar tikinti həlləri'); ?></p>
                                            <div class="slide-icon">
                                                <i class="fas fa-hammer"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="carousel-slide">
                                <div class="slide-content">
                                    <div class="slide-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/dizayn-z.webp" alt="Dizayn Xidmətləri">
                                    </div>
                                    <div class="slide-overlay">
                                        <div class="slide-info">
                                            <h3><?php pll_e_safe('Dizayn & Memarlıq'); ?></h3>
                                            <p><?php pll_e_safe('3D vizualizasiya'); ?></p>
                                            <div class="slide-icon">
                                                <i class="fas fa-drafting-compass"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="carousel-slide">
                                <div class="slide-content">
                                    <div class="slide-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/mebel-z.webp" alt="Mebel İşləri">
                                    </div>
                                    <div class="slide-overlay">
                                        <div class="slide-info">
                                            <h3><?php pll_e_safe('Mebel İşləri'); ?></h3>
                                            <p><?php pll_e_safe('Xüsusi sifarişlə mebel'); ?></p>
                                            <div class="slide-icon">
                                                <i class="fas fa-couch"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Carousel Controls -->
                        <div class="carousel-controls">
                            <button class="carousel-btn prev-btn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="carousel-btn next-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>

                        <!-- Carousel Indicators -->
                        <div class="carousel-indicators">
                            <span class="indicator active" data-slide="0"></span>
                            <span class="indicator" data-slide="1"></span>
                            <span class="indicator" data-slide="2"></span>
                            <span class="indicator" data-slide="3"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>




<!-- Modern Services Section -->
<section class="home-services alachiq-section" id="xidmetler">
    <div class="container">
        <div class="home-services__header">
            <div class="home-services__badge">
                <i class="fas fa-tools"></i>
                <span><?php pll_e_safe('Xidmətlərimiz'); ?></span>
            </div>
            <h2 class="home-services__title" >
                <?php pll_e_safe('Peşəkar Tikinti'); ?> <span class="text-accent"><?php pll_e_safe('Xidmətləri'); ?></span>
            </h2>
            <p class="home-services__subtitle" >
                <?php pll_e_safe('A-dan Z-yə qədər bütün tikinti və dizayn xidmətlərini bir yerdə təqdim edirik'); ?>
            </p>
        </div>

        <div class="home-services__grid">
            <?php
            // Query for services from WordPress dashboard
            $home_services_query = new WP_Query(array(
                'post_type' => 'services',
                'posts_per_page' => 6, // Limit to 6 services for homepage
                'post_status' => 'publish',
                'orderby' => 'menu_order date',
                'order' => 'ASC'
            ));

            if ($home_services_query->have_posts()) :
                $delay = 100; // Starting animation delay
                while ($home_services_query->have_posts()) : $home_services_query->the_post();
                    $service_icon = get_post_meta(get_the_ID(), '_service_icon', true);
                    $service_features = get_post_meta(get_the_ID(), '_service_features', true);

                    // Default icon if none set
                    if (empty($service_icon)) {
                        $service_icon = 'fas fa-cog';
                    }

                    // Ensure features is an array
                    if (!is_array($service_features)) {
                        $service_features = !empty($service_features) ? explode("\n", $service_features) : array();
                    }

                    // Limit features to 3 for homepage display
                    $service_features = array_slice($service_features, 0, 3);
            ?>
            <div class="home-services__item" data-aos="fade-up" data-aos-delay="<?php echo $delay; ?>">
                <div class="service-card tilt-card">
                    <div class="service-card__glow"></div>
                    <div class="service-card__icon">
                        <i class="<?php echo esc_attr($service_icon); ?> animated-icon"></i>
                    </div>
                    <div class="service-card__content">
                        <h3 class="service-card__title"><?php echo esc_html(get_the_title()); ?></h3>
                        <p class="service-card__description">
                            <?php echo wp_trim_words(get_the_excerpt() ?: get_the_content(), 12, '...'); ?>
                        </p>

                        <?php if (!empty($service_features)) : ?>
                        <ul class="service-card__features">
                            <?php foreach ($service_features as $feature) : ?>
                                <li><i class="fas fa-check"></i> <span><?php echo esc_html(trim($feature)); ?></span></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>

                        <a href="<?php echo esc_url(get_permalink()); ?>" class="service-card__link">
                            <span><?php pll_e_safe('Ətraflı'); ?></span> <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <?php
                $delay += 100; // Increment delay for next item
                endwhile;
                wp_reset_postdata();
            else :
                // Fallback static services if no dynamic services exist
            ?>
            <div class="home-services__item" data-aos="fade-up" data-aos-delay="100">
                <div class="service-card tilt-card">
                    <div class="service-card__glow"></div>
                    <div class="service-card__icon">
                        <i class="fas fa-drafting-compass animated-icon"></i>
                    </div>
                    <div class="service-card__content">
                        <h3 class="service-card__title"><?php pll_e_safe('Dizayn və Memarlıq'); ?></h3>
                        <p class="service-card__description">
                            <?php pll_e_safe('Müasir dizayn həlləri və peşəkar memarlıq xidmətləri'); ?>
                        </p>
                        <ul class="service-card__features">
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('3D Vizualizasiya'); ?></span></li>
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('İnteryer Dizaynı'); ?></span></li>
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('Memarlıq Layihələri'); ?></span></li>
                        </ul>
                        <a href="<?php echo home_url('/services/'); ?>" class="service-card__link">
                            <span><?php pll_e_safe('Ətraflı'); ?></span> <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="home-services__item" data-aos="fade-up" data-aos-delay="200">
                <div class="service-card tilt-card">
                    <div class="service-card__glow"></div>
                    <div class="service-card__icon">
                        <i class="fas fa-hammer animated-icon"></i>
                    </div>
                    <div class="service-card__content">
                        <h3 class="service-card__title"><?php pll_e_safe('Tikinti və Təmir'); ?></h3>
                        <p class="service-card__description">
                            <?php pll_e_safe('Yüksək keyfiyyətli tikinti və təmir işləri'); ?>
                        </p>
                        <ul class="service-card__features">
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('Villa Tikintisi'); ?></span></li>
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('Mənzil Təmiri'); ?></span></li>
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('Ofis Təmiri'); ?></span></li>
                        </ul>
                        <a href="<?php echo home_url('/services/'); ?>" class="service-card__link">
                            <span><?php pll_e_safe('Ətraflı'); ?></span> <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="home-services__item" data-aos="fade-up" data-aos-delay="300">
                <div class="service-card tilt-card">
                    <div class="service-card__glow"></div>
                    <div class="service-card__icon">
                        <i class="fas fa-couch animated-icon"></i>
                    </div>
                    <div class="service-card__content">
                        <h3 class="service-card__title"><?php pll_e_safe('Mebel İşləri'); ?></h3>
                        <p class="service-card__description">
                            <?php pll_e_safe('Xüsusi sifarişlə hazırlanan keyfiyyətli mebellər'); ?>
                        </p>
                        <ul class="service-card__features">
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('Mətbəx Mebelləri'); ?></span></li>
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('Dolablar'); ?></span></li>
                            <li><i class="fas fa-check"></i> <span><?php pll_e_safe('Ofis Mebelləri'); ?></span></li>
                        </ul>
                        <a href="<?php echo home_url('/services/'); ?>" class="service-card__link">
                            <span><?php pll_e_safe('Ətraflı'); ?></span> <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="home-about alachiq-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="home-about__content">
                    <div class="home-about__badge">
                        <i class="fas fa-building"></i>
                        <span><?php pll_e_safe('Haqqımızda'); ?></span>
                    </div>
                    <h2 class="home-about__title">
                        <?php _e('15 İllik Təcrübə ilə <span class="text-accent">Keyfiyyətli Tikinti</span>', 'alachiqgroup-pro'); ?>
                    </h2>
                    <p class="home-about__description">
                        <?php pll_e_safe('<strong>Alachiq Group MMC</strong> 2010-cu ildən etibarən Azərbaycanda tikinti sahəsində fəaliyyət göstərir. Müştərilərimizin ehtiyaclarını ən yüksək səviyyədə qarşılamaq üçün müasir texnologiyalar və keyfiyyətli materiallardan istifadə edirik.'); ?>
                    </p>

                    <div class="home-about__features">
                        <div class="home-about__feature">
                            <div class="home-about__feature-icon">
                                <i class="fas fa-medal"></i>
                            </div>
                            <div class="home-about__feature-content">
                                <h4><?php pll_e_safe('Keyfiyyət Zəmanəti'); ?></h4>
                                <p><?php pll_e_safe('Bütün işlərimizə uzunmüddətli zəmanət veririk'); ?></p>
                            </div>
                        </div>

                        <div class="home-about__feature">
                            <div class="home-about__feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="home-about__feature-content">
                                <h4><?php pll_e_safe('Peşəkar Komanda'); ?></h4>
                                <p><?php pll_e_safe('Təcrübəli mütəxəssislər və peşəkar işçilər'); ?></p>
                            </div>
                        </div>

                        <div class="home-about__feature">
                            <div class="home-about__feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="home-about__feature-content">
                                <h4><?php pll_e_safe('Vaxtında Təhvil'); ?></h4>
                                <p><?php pll_e_safe('Müqavilədə göstərilən müddətdə işi tamamlayırıq'); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="home-about__actions">
                        <a href="<?php echo home_url('/about/'); ?>" class="btn-primary">
                            <i class="fas fa-info-circle"></i>
                            <span><?php pll_e_safe('Ətraflı Məlumat'); ?></span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="home-about__image">
                    <div class="home-about__image-wrapper">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/2.jpg" alt="Alachiq Group Construction">

                    </div>
                    <div class="home-about__experience-badge">
                        <span class="home-about__experience-number">15+</span>
                        <span class="home-about__experience-text"><?php pll_e_safe('İl Təcrübə'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Projects Section -->
<section class="home-projects alachiq-section">
    <div class="container">
        <div class="home-projects__header">
            <div class="home-projects__badge">
                <i class="fas fa-building"></i>
                <span><?php pll_e_safe('Layihələrimiz'); ?></span>
            </div>
            <h2 class="home-projects__title">
                <?php _e('Son <span class="text-accent">Layihələrimiz</span>', 'alachiqgroup-pro'); ?>
            </h2>
            <p class="home-projects__subtitle">
                <?php pll_e_safe('Tamamladığımız layihələrdən bəziləri ilə tanış olun'); ?>
            </p>
        </div>

        <div class="home-projects__grid">
            <?php
            // Get recent projects
            $recent_projects = new WP_Query(array(
                'post_type' => 'projects',
                'posts_per_page' => 6,
                'post_status' => 'publish'
            ));

            if ($recent_projects->have_posts()) :
                $project_count = 0;
                while ($recent_projects->have_posts()) : $recent_projects->the_post();
                    $project_count++;
                    $project_area = get_post_meta(get_the_ID(), '_project_area', true);
                    $project_type = get_post_meta(get_the_ID(), '_project_type', true);
                    $delay = $project_count * 0.1;
            ?>
            <div class="home-projects__item" data-aos="fade-up" data-aos-delay="<?php echo $delay * 100; ?>">
                <div class="project-card">
                    <div class="project-card__image">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                        <?php else : ?>
                            <img src="<?php echo get_template_directory_uri(); ?>/img/original.jpg" alt="<?php the_title(); ?>">
                        <?php endif; ?>
                        <div class="project-card__overlay">
                            <div class="project-card__content">
                                <h3 class="project-card__title"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title(get_the_ID())); ?></h3>
                                <?php if ($project_area) : ?>
                                    <p class="project-card__area">
                                        <i class="fas fa-ruler-combined"></i>
                                        <?php echo esc_html($project_area); ?> <?php pll_e_safe('m²'); ?>
                                    </p>
                                <?php endif; ?>
                                <?php if ($project_type) : ?>
                                    <p class="project-card__type">
                                        <i class="fas fa-tag"></i>
                                        <?php echo esc_html($project_type); ?>
                                    </p>
                                <?php endif; ?>
                                <a href="<?php the_permalink(); ?>" class="project-card__link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
                endwhile;
                wp_reset_postdata();
            else :
                // Fallback content if no projects exist
                for ($i = 1; $i <= 6; $i++) :
                    $delay = $i * 0.1;
            ?>
            <div class="home-projects__item" data-aos="fade-up" data-aos-delay="<?php echo $delay * 100; ?>">
                <div class="project-card">
                    <div class="project-card__image">
                        <img src="<?php echo get_template_directory_uri(); ?>/img/original.jpg" alt="Nümunə Layihə <?php echo $i; ?>">
                        <div class="project-card__overlay">
                            <div class="project-card__content">
                                <h3 class="project-card__title"><?php pll_e_safe('Nümunə Layihə'); ?> <?php echo $i; ?></h3>
                                <p class="project-card__area">
                                    <i class="fas fa-ruler-combined"></i>
                                    <?php echo rand(150, 500); ?> <?php pll_e_safe('m²'); ?>
                                </p>
                                <p class="project-card__type">
                                    <i class="fas fa-tag"></i>
                                    <?php pll_e_safe('Villa'); ?>
                                </p>
                                <a href="<?php echo home_url('/layiheler/'); ?>" class="project-card__link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php
                endfor;
            endif;
            ?>
        </div>

        <div class="home-projects__actions">
            <a href="<?php echo home_url('/projects/'); ?>" class="btn-outline">
                <i class="fas fa-eye"></i>
                <span><?php pll_e_safe('Bütün Layihələrə Baxın'); ?></span>
            </a>
        </div>
    </div>
</section>

<!-- Partners Section -->
<section class="home-partners alachiq-section">
    <div class="container">
        <div class="home-partners__header">
            <div class="home-partners__badge">
                <i class="fas fa-handshake"></i>
                <span><?php pll_e_safe('Tərəfdaşlarımız'); ?></span>
            </div>
            <h2 class="home-partners__title">
                <?php _e('Etibarlı <span class="text-accent">Tərəfdaşlar</span>', 'alachiqgroup-pro'); ?>
            </h2>
            <p class="home-partners__subtitle">
                <?php pll_e_safe('Keyfiyyətli materiallar üçün etibarlı tərəfdaşlarımızla işləyirik'); ?>
            </p>
        </div>

        <div class="home-partners__grid">
            <div class="partner-card" data-aos="fade-up" data-aos-delay="100">
                <img src="<?php echo get_template_directory_uri(); ?>/img/TITLE-az-1-2.png" alt="Partner 1">
            </div>
            <div class="partner-card" data-aos="fade-up" data-aos-delay="200">
                <img src="<?php echo get_template_directory_uri(); ?>/img/metaklogo.png" alt="Partner 2">
            </div>
            <div class="partner-card" data-aos="fade-up" data-aos-delay="300">
                <img src="<?php echo get_template_directory_uri(); ?>/img/fablogo.png" alt="Partner 3">
            </div>
            <div class="partner-card" data-aos="fade-up" data-aos-delay="400">
                <img src="<?php echo get_template_directory_uri(); ?>/img/download.png" alt="Partner 4">
            </div>
            <div class="partner-card" data-aos="fade-up" data-aos-delay="500">
                <img src="<?php echo get_template_directory_uri(); ?>/img/zakiroglu.png" alt="Partner 5">
            </div>
            <div class="partner-card" data-aos="fade-up" data-aos-delay="600">
                <img src="<?php echo get_template_directory_uri(); ?>/img/logopasa1.png" alt="Partner 6">
            </div>
        </div>
    </div>
</section>



<?php get_footer(); ?>

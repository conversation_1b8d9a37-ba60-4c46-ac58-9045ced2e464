<?php get_header(); ?>

<main class="projects-page">
    <!-- Modern Hero Section -->
    <section class="projects-hero">
        <div class="projects-hero__background">
            <div class="projects-hero__pattern"></div>
            <div class="projects-hero__gradient"></div>
        </div>
        <div class="container">
            <div class="projects-hero__content">
                <div class="projects-hero__badge">
                    <i class="fas fa-building"></i>
                    <span><?php pll_e_safe('Layihələrimiz'); ?></span>
                </div>
                <h1 class="projects-hero__title">
                    <span class="projects-hero__title-line">
                        <span class="projects-hero__title-word"><?php pll_e_safe('Tamamladığımız'); ?></span>
                    </span>
                    <span class="projects-hero__title-line">
                        <span class="projects-hero__title-word projects-hero__title-word--accent"><?php pll_e_safe('Lay<PERSON>ələr'); ?> </span>
                    </span>
                </h1>
                <p class="projects-hero__subtitle">
                    <?php echo esc_html(get_theme_mod('company_experience_years', '10+')); ?> <?php pll_e_safe('ildən artıq təcrübəmizlə tamamladığımız'); ?> <?php echo esc_html(get_theme_mod('company_total_projects', '500+')); ?> <?php pll_e_safe('layihə və müştərilərimizin bizə göstərdiyi etimad'); ?>
                </p>
                <div class="projects-hero__stats">
                    <div class="projects-hero__stat">
                        <span class="projects-hero__stat-number"><?php echo esc_html(get_theme_mod('company_total_projects', '500+')); ?></span>
                        <span class="projects-hero__stat-label"><?php pll_e_safe('Layihə'); ?></span>
                    </div>
                    <div class="projects-hero__stat">
                        <span class="projects-hero__stat-number">100%</span>
                        <span class="projects-hero__stat-label"><?php pll_e_safe('Məmnuniyyət'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="projects-filter">
        <div class="container">
            <div class="projects-filter__content">
                <div class="projects-filter__header">
                        <h2 class="projects-filter__title"><?php pll_e_safe('Layihə Kateqoriyaları'); ?></h2>
                    <p class="projects-filter__subtitle"><?php pll_e_safe('İstədiyiniz kateqoriyaya görə layihələri filtrləyin'); ?></p>
                </div>
                
                <div class="projects-filter__buttons">
                    <?php
                    $selected_category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : '';
                    $all_active_class = empty($selected_category) ? 'projects-filter__btn--active' : '';
                    ?>
                    
                    <!-- All Projects Button -->
                    <button class="projects-filter__btn <?php echo $all_active_class; ?>" data-filter="all">
                        <i class="fas fa-th-large"></i>
                        <span><?php pll_e_safe('Hamısı'); ?></span>
                    </button>
                    
                    <?php
                    $categories = alachiqgroup_pro_get_project_categories(false);
                    if ($categories && !is_wp_error($categories)) :
                        foreach ($categories as $category) :
                            $active_class = ($selected_category === $category->slug) ? 'projects-filter__btn--active' : '';
                            $icon = 'fas fa-building'; // Default icon
                            
                            // Set specific icons for different categories
                            switch(strtolower($category->slug)) {
                                case pll___safe('villa'):
                                case pll___safe('villalar'):
                                    $icon = 'fas fa-home';
                                    break;
                                case 'kottec':
                                case 'kotteclər':
                                    $icon = 'fas fa-house-user';
                                    break;
                                case 'kommersiya':
                                case 'commercial':
                                    $icon = 'fas fa-building';
                                    break;
                                case pll___safe('interyer'):
                                case pll___safe('interior'):
                                    $icon = 'fas fa-couch';
                                    break;
                                case pll___safe('hovuz'):
                                case pll___safe('pool'):
                                    $icon = 'fas fa-swimming-pool';
                                    break;
                                default:
                                    $icon = 'fas fa-hammer';
                            }
                    ?>
                    <button class="projects-filter__btn <?php echo $active_class; ?>" data-filter="<?php echo esc_attr($category->slug); ?>">
                        <i class="<?php echo $icon; ?>"></i>
                        <span><?php
                            $translated_category = alachiqgroup_pro_get_translated_term($category, 'project_category');
                            echo esc_html($translated_category->name);
                        ?></span>
                    </button>
                    <?php
                        endforeach;
                    endif;
                    ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Grid Section -->
    <section class="projects-grid">
        <div class="container">
            <div class="projects-grid__container" id="projects-grid">
                <?php
                $projects_query = new WP_Query(array(
                    'post_type' => 'projects',
                    'posts_per_page' => 12,
                    'post_status' => 'publish',
                    'orderby' => 'date',
                    'order' => 'DESC'
                ));

                if ($projects_query->have_posts()) :
                    while ($projects_query->have_posts()) : $projects_query->the_post();
                        $project_categories = get_the_terms(get_the_ID(), 'project_category');
                        $category_classes = '';
                        if ($project_categories) {
                            foreach ($project_categories as $category) {
                                $category_classes .= ' ' . $category->slug;
                            }
                        }

                        $project_area = get_post_meta(get_the_ID(), '_project_area', true);
                        $project_floors = get_post_meta(get_the_ID(), '_project_floors', true);
                        $project_type = get_post_meta(get_the_ID(), '_project_type', true);
                        $project_year = get_post_meta(get_the_ID(), '_project_year', true);
                ?>
                <div class="project-card project-item<?php echo esc_attr($category_classes); ?>">
                    <div class="project-card__image">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('project-thumb', array('class' => 'project-card__img')); ?>
                        <?php else : ?>
                            <div class="project-card__placeholder">
                                <i class="fas fa-building"></i>
                                <span><?php pll_e_safe('Şəkil Yoxdur'); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="project-card__overlay">
                            <div class="project-card__overlay-content">
                                <a href="<?php the_permalink(); ?>" class="project-card__btn project-card__btn--view">
                                    <i class="fas fa-eye"></i>
                                    <span><?php pll_e_safe('Ətraflı'); ?></span>
                                </a>
                                <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>" class="project-card__btn project-card__btn--zoom" data-lightbox="project-<?php echo get_the_ID(); ?>">
                                    <i class="fas fa-search-plus"></i>
                                    <span><?php pll_e_safe('Böyüt'); ?></span>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($project_categories) : ?>
                        <div class="project-card__category">
                            <?php echo esc_html($project_categories[0]->name); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="project-card__content">
                        <h3 class="project-card__title">
                            <a href="<?php the_permalink(); ?>"><?php echo esc_html(alachiqgroup_pro_get_translated_post_title(get_the_ID())); ?></a>
                        </h3>
                        
                        <?php
                        $translated_excerpt = alachiqgroup_pro_get_translated_post_content(get_the_ID(), 'excerpt');
                        if (!empty($translated_excerpt)) : ?>
                        <p class="project-card__excerpt"><?php echo wp_trim_words($translated_excerpt, 15); ?></p>
                        <?php endif; ?>
                        
                        <div class="project-card__details">
                            <?php if ($project_area) : ?>
                            <div class="project-card__detail">
                                <i class="fas fa-ruler-combined"></i>
                                <span><?php echo esc_html($project_area); ?> <?php pll_e_safe('m²'); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($project_floors) : ?>
                            <div class="project-card__detail">
                                <i class="fas fa-building"></i>
                                <span><?php echo esc_html($project_floors); ?> <?php pll_e_safe('mərtəbə'); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($project_year) : ?>
                            <div class="project-card__detail">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo esc_html($project_year); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="project-card__footer">
                            <a href="<?php the_permalink(); ?>" class="project-card__link">
                                <span><?php pll_e_safe('Ətraflı Bax'); ?></span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <?php
                    endwhile;
                    wp_reset_postdata();
                else :
                ?>
                <div class="projects-empty">
                    <div class="projects-empty__content">
                        <div class="projects-empty__icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h3 class="projects-empty__title"><?php pll_e_safe('Hələ ki layihə yoxdur'); ?></h3>
                        <p class="projects-empty__description"><?php pll_e_safe('Tezliklə yeni layihələr əlavə ediləcək.'); ?></p>
                        <?php if (current_user_can('edit_posts')) : ?>
                        <a href="<?php echo admin_url('post-new.php?post_type=projects'); ?>" class="projects-empty__btn">
                            <i class="fas fa-plus"></i>
                            <span><?php pll_e_safe('İlk layihəni əlavə et'); ?></span>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Achievements Section -->
    <section class="projects-achievements">
        <div class="projects-achievements__background"></div>
        <div class="container">
            <div class="projects-achievements__header">
                <div class="projects-achievements__label"><?php pll_e_safe('Nailiyyətlərimiz'); ?></div>
                <h2 class="projects-achievements__title">
                    <?php pll_e_safe('Rəqəmlərlə'); ?>
                    <span class="projects-achievements__title-accent"><?php pll_e_safe('Uğur Hekayəmiz'); ?></span>
                </h2>
            </div>
            
            <div class="projects-achievements__grid">
                <div class="achievement-card">
                    <div class="achievement-card__icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="achievement-card__content">
                        <span class="achievement-card__number" data-count="<?php echo esc_attr(str_replace('+', '', get_theme_mod('company_total_projects', '500'))); ?>">0</span>
                        <span class="achievement-card__label"><?php pll_e_safe('Tamamlanmış Layihə'); ?></span>
                    </div>
                </div>
                
                <div class="achievement-card">
                    <div class="achievement-card__icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="achievement-card__content">
                        <span class="achievement-card__number" data-count="<?php echo esc_attr(str_replace('+', '', get_theme_mod('company_experience_years', '10'))); ?>">0</span>
                        <span class="achievement-card__label"><?php pll_e_safe('İl Təcrübə'); ?></span>
                    </div>
                </div>
                
                <div class="achievement-card">
                    <div class="achievement-card__icon">
                        <i class="fas fa-smile"></i>
                    </div>
                    <div class="achievement-card__content">
                        <span class="achievement-card__number" data-count="100">0</span>
                        <span class="achievement-card__label"><?php pll_e_safe('Məmnun Müştəri (%)'); ?></span>
                    </div>
                </div>
                
                <div class="achievement-card">
                    <div class="achievement-card__icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="achievement-card__content">
                        <span class="achievement-card__number" data-count="<?php echo esc_attr(str_replace('+', '', get_theme_mod('company_team_members', '25'))); ?>">0</span>
                        <span class="achievement-card__label"><?php pll_e_safe('Komanda Üzvü'); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="projects-cta">
        <div class="projects-cta__background">
            <div class="projects-cta__pattern"></div>
        </div>
        <div class="container">
            <div class="projects-cta__content">
                <div class="projects-cta__icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h2 class="projects-cta__title">
                    <?php pll_e_safe('Növbəti Layihəniz'); ?>
                    <span class="projects-cta__title-accent"><?php pll_e_safe('Bizim Olsun'); ?></span>
                </h2>
                <p class="projects-cta__description">
                    <?php pll_e_safe('Layihənizi həyata keçirmək üçün bizimlə əlaqə saxlayın və pulsuz məsləhət alın'); ?>  
                </p>
                <div class="projects-cta__actions">
                    <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="projects-cta__btn projects-cta__btn--primary">
                        <span><?php pll_e_safe('Pulsuz Məsləhət'); ?></span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="tel:<?php echo esc_attr(str_replace(array(' ', '(', ')', '-'), '', get_theme_mod('company_phone', '+994125143906'))); ?>" class="projects-cta__btn projects-cta__btn--secondary">
                        <i class="fas fa-phone"></i>
                        <span><?php pll_e_safe('İndi Zəng Et'); ?></span>
                    </a>
                </div>
            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
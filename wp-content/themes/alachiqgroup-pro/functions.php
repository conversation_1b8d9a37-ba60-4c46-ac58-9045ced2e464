<?php
/**
 * Alachiq Group Pro Theme Functions
 * Clean, modular architecture following SOC, DRY, SOLID principles
 * 
 * CSS Architecture:
 * - style.css: Global variables, reset, base styles
 * - components.css: Reusable components (buttons, cards, sections)
 * - hero.css: Hero section styles
 * - services.css: Services section styles  
 * - projects.css: Projects section styles
 * - cta.css: Call-to-action section styles
 * - header-modern.css: Header styles
 * - footer-modern.css: Footer styles
 * 
 * Loading Strategy:
 * - Global styles loaded on all pages
 * - Component-specific styles loaded only when needed
 * - Performance optimized with conditional loading
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include debug script

// Theme setup
function alachiqgroup_pro_setup() {
    // Note: Textdomain loading is handled by alachiqgroup_pro_load_textdomain() function
    // to support dynamic language switching

    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('menus');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => pll___safe('Əsas Menyu'),
        'footer' => pll___safe('Footer Menyu'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
    
    // Add image sizes
    add_image_size('project-thumb', 400, 300, true);
    add_image_size('project-gallery', 600, 450, true);
    add_image_size('project-hero', 800, 600, true);
    add_image_size('slider-image', 1200, 500, true);
    
    // Disable WordPress core lightbox to prevent conflicts with our custom lightbox
    add_filter('wp_lazy_loading_enabled', '__return_false');
    add_filter('wp_get_attachment_image_attributes', 'alachiqgroup_pro_remove_lightbox_attributes', 10, 3);
}
add_action('after_setup_theme', 'alachiqgroup_pro_setup');







// Remove WordPress core lightbox attributes to prevent conflicts
function alachiqgroup_pro_remove_lightbox_attributes($attr, $attachment, $size) {
    // Remove any lightbox-related attributes that WordPress might add
    if (isset($attr['data-wp-on-async--click'])) {
        unset($attr['data-wp-on-async--click']);
    }
    
    // Ensure class attribute exists but remove any lightbox-related classes
    if (!isset($attr['class'])) {
        $attr['class'] = '';
    }
    
    return $attr;
}

// Disable WordPress core lightbox globally
function alachiqgroup_pro_disable_core_lightbox() {
    // Remove WordPress core lightbox scripts and styles
    wp_dequeue_script('wp-lightbox');
    wp_dequeue_style('wp-lightbox');
    
    // Disable lightbox for all images
    add_filter('wp_lazy_loading_enabled', '__return_false');
    add_filter('wp_get_attachment_image_attributes', 'alachiqgroup_pro_remove_lightbox_attributes', 10, 3);
}
add_action('wp_enqueue_scripts', 'alachiqgroup_pro_disable_core_lightbox', 100);

// Modern Performance Optimizations
function alachiqgroup_pro_performance_optimizations() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emojis
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    
    // Optimize jQuery loading
    if (!is_admin()) {
        wp_deregister_script('jquery');
        wp_register_script('jquery', includes_url('/js/jquery/jquery.min.js'), array(), null, true);
    }
}
add_action('init', 'alachiqgroup_pro_performance_optimizations');

// Enqueue scripts and styles
function alachiqgroup_pro_scripts() {
    // LOAD EXTERNAL DEPENDENCIES FIRST
    // Bootstrap 5 CSS
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css', array(), '5.3.2');
    
    // Font Awesome
    wp_enqueue_style('fontawesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');
    
    // Google Fonts - Montserrat (exact same as alovinsaat.az)
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap', array(), null);
    
    // Animate CSS
    wp_enqueue_style('animate', 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css', array(), '4.1.1');

    // AOS (Animate On Scroll) CSS
    wp_enqueue_style('aos', 'https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css', array(), '2.3.4');
    
    // LOAD COMPONENTS CSS (DEPENDS ON BOOTSTRAP AND FONTAWESOME)
    $components_css = get_template_directory() . '/assets/css/components.css';
    if (file_exists($components_css)) {
        wp_enqueue_style('alachiq-components', get_template_directory_uri() . '/assets/css/components.css', array('bootstrap', 'fontawesome'), filemtime($components_css));
    }
    
    // Load CSS files
    $all_css_files = array(
        'header' => '/assets/css/header-modern.css',
        'footer' => '/assets/css/footer-modern.css',
        'decorations' => '/assets/css/decorations-page.css',
        'single-project' => '/assets/css/single-project.css',
        'projects-page-fixed' => '/assets/css/projects-page-fixed.css',
        'services-page-modern' => '/assets/css/services-page-modern.css',
        'about-page' => '/assets/css/about-page.css',
        'contact-page' => '/assets/css/contact-page.css',
        'ecommerce-decorations' => '/assets/css/ecommerce-decorations.css'
    );
    
    foreach ($all_css_files as $handle => $file) {
        $file_path = get_template_directory() . $file;
        if (file_exists($file_path)) {
            wp_enqueue_style('alachiq-' . $handle, get_template_directory_uri() . $file, array('alachiq-components'), filemtime($file_path));
        }
    }
    
    // Theme main stylesheet (MUST LOAD LAST - OVERRIDES ALL OTHERS)
    wp_enqueue_style('alachiq-style', get_stylesheet_uri(), array('alachiq-components'), filemtime(get_template_directory() . '/style.css'));
    
    // jQuery (WordPress includes it)
    wp_enqueue_script('jquery');
    
    // Bootstrap 5 JS
    wp_enqueue_script('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js', array('jquery'), '5.3.2', true);
    
    // Typed.js for typing effect
    wp_enqueue_script('typed', 'https://cdn.jsdelivr.net/npm/typed.js@2.0.12/lib/typed.min.js', array(), '2.0.12', true);

    // AOS (Animate On Scroll) JS
    wp_enqueue_script('aos', 'https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js', array(), '2.3.4', true);
    
    // Load JavaScript files
    $all_js_files = array(
        'scripts' => '/assets/js/scripts.js',
        'modern-enhancements' => '/assets/js/modern-enhancements.js'
    );

    // Page-specific JavaScript files
    if (is_front_page() || is_home()) {
        $all_js_files['homepage'] = '/assets/js/homepage.js';
    }

    if (is_page('decorations') || is_post_type_archive('decoration_products') || is_singular('decoration_products')) {
        $all_js_files['decorations'] = '/assets/js/decorations-page.js';
    }

    if (is_singular('projects')) {
        $all_js_files['single-project'] = '/assets/js/single-project.js';
    }

    if (is_post_type_archive('projects') || is_page('projects')) {
        $all_js_files['projects-page'] = '/assets/js/projects-page.js';
    }

    if (is_page('services')) {
        $all_js_files['services-page'] = '/assets/js/services-page.js';
    }

    if (is_page('about')) {
        $all_js_files['about-page'] = '/assets/js/about-page.js';
    }

    foreach ($all_js_files as $handle => $file) {
        $file_path = get_template_directory() . $file;
        if (file_exists($file_path)) {
            wp_enqueue_script('alachiq-' . $handle, get_template_directory_uri() . $file, array('jquery'), filemtime($file_path), true);
        }
    }
    

    
    // Always load emergency fallback script
    $emergency_js = get_template_directory() . '/assets/js/emergency-fallback.js';
    if (file_exists($emergency_js)) {
        wp_enqueue_script('alachiq-emergency-fallback', get_template_directory_uri() . '/assets/js/emergency-fallback.js', array(), filemtime($emergency_js), true);
    }
    
    // Simple image error handling script
    $simple_image_handler_js = get_template_directory() . '/assets/js/simple-image-handler.js';
    if (file_exists($simple_image_handler_js)) {
        wp_enqueue_script('alachiq-simple-image-handler', get_template_directory_uri() . '/assets/js/simple-image-handler.js', array(), filemtime($simple_image_handler_js), true);
    }
    
    // Image protection script - prevents images from being made invisible
    $image_protection_js = get_template_directory() . '/assets/js/image-protection.js';
    if (file_exists($image_protection_js)) {
        wp_enqueue_script('alachiq-image-protection', get_template_directory_uri() . '/assets/js/image-protection.js', array(), filemtime($image_protection_js), true);
    }
    

    
    // Remove duplicate AOS - already loaded in home page section
    
    // Header enhancement script (load on all pages)
    $header_js = get_template_directory() . '/assets/js/header-modern.js';
    if (file_exists($header_js)) {
        wp_enqueue_script('alachiq-header-js', get_template_directory_uri() . '/assets/js/header-modern.js', array('jquery'), filemtime($header_js), true);
    }
    
    // Dark mode removed for cleaner theme
    
    // Home page data functions
    function alachiqgroup_pro_get_home_services() {
        return array(
            array(
                'title' => 'Memarlıq və Dizayn',
                'description' => 'Müasir memarlıq həlləri və yaradıcı dizayn layihələri. Hər layihə üçün fərdi yanaşma.',
                'icon' => 'fas fa-drafting-compass',
                'url' => home_url('/services/'),
                'featured' => true,
                'color' => 'primary'
            ),
            array(
                'title' => 'Tikinti Xidmətləri',
                'description' => 'Villa, kottec və kommersiya obyektlərinin tikintisi. Yüksək keyfiyyət və müasir texnologiyalar.',
                'icon' => 'fas fa-hard-hat',
                'url' => home_url('/services/'),
                'featured' => false,
                'color' => 'secondary'
            ),
            array(
                'title' => 'Təmir və Yenilənmə',
                'description' => 'Keyfiyyətli təmir və yenilənmə işləri. Köhnə binaların müasir standartlara uyğun yenilənməsi.',
                'icon' => 'fas fa-tools',
                'url' => home_url('/services/'),
                'featured' => false,
                'color' => 'accent'
            ),
            array(
                'title' => 'İnteryer Dizaynı',
                'description' => 'Müasir və funksional interyer həlləri. Yaşayış və iş məkanlarının estetik tərtibatı.',
                'icon' => 'fas fa-couch',
                'url' => home_url('/services/'),
                'featured' => false,
                'color' => 'neutral-light'
            ),
            array(
                'title' => 'Hovuz Tikintisi',
                'description' => 'Müasir hovuz dizaynı və tikintisi. Fərdi və kommersiya hovuzları üçün tam həllər.',
                'icon' => 'fas fa-swimming-pool',
                'url' => home_url('/services/'),
                'featured' => false,
                'color' => 'neutral-dark'
            ),
            array(
                'title' => 'Abadlaşdırma',
                'description' => 'Bağ və həyət dizaynı, abadlaşdırma işləri. Yaşıl məkanların yaradılması və tərtibatı.',
                'icon' => 'fas fa-seedling',
                'url' => home_url('/services/'),
                'featured' => false,
                'color' => 'primary'
            )
        );
    }

    function alachiqgroup_pro_get_home_projects() {
        $projects = array();
        
        // Get recent projects from custom post type
        $recent_projects = get_posts(array(
            'post_type' => 'projects',
            'posts_per_page' => 6,
            'post_status' => 'publish'
        ));

        foreach ($recent_projects as $project) {
            $project_image = get_the_post_thumbnail_url($project->ID, 'medium');
            $project_category = get_the_terms($project->ID, 'project_category');
            $category_name = $project_category && !is_wp_error($project_category) ? $project_category[0]->name : 'Layihə';
            $category_slug = $project_category && !is_wp_error($project_category) ? $project_category[0]->slug : 'project';
            
            $projects[] = array(
                'title' => $project->post_title,
                'excerpt' => wp_trim_words($project->post_excerpt ?: $project->post_content, 20),
                'image' => $project_image ?: get_template_directory_uri() . '/assets/images/placeholder.jpg',
                'url' => get_permalink($project->ID),
                'category' => $category_slug,
                'category_name' => $category_name,
                'location' => get_post_meta($project->ID, '_project_location', true) ?: 'Bakı'
            );
        }

        // If no projects found, return sample data
        if (empty($projects)) {
            $projects = array(
                array(
                    'title' => 'Müasir Villa Layihəsi',
                    'excerpt' => 'Bakının ən yaxşı məhəlləsində tikilən müasir villa layihəsi.',
                    'image' => get_template_directory_uri() . '/assets/images/placeholder.jpg',
                    'url' => home_url('/projects/'),
                    'category' => 'villa',
                    'category_name' => 'Villa',
                    'location' => 'Bakı'
                ),
                array(
                    'title' => 'Kottec Kompleksi',
                    'excerpt' => 'Yüksək keyfiyyətli kottec kompleksi layihəsi.',
                    'image' => get_template_directory_uri() . '/assets/images/placeholder.jpg',
                    'url' => home_url('/projects/'),
                    'category' => 'cottage',
                    'category_name' => 'Kottec',
                    'location' => 'Bakı'
                ),
                array(
                    'title' => 'Kommertsiya Obyekti',
                    'excerpt' => 'Müasir kommertsiya obyekti tikintisi və dizaynı.',
                    'image' => get_template_directory_uri() . '/assets/images/placeholder.jpg',
                    'url' => home_url('/projects/'),
                    'category' => 'commercial',
                    'category_name' => 'Kommertsiya',
                    'location' => 'Bakı'
                )
            );
        }

        return $projects;
    }

    function alachiqgroup_pro_get_home_structured_data() {
        return array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'logo' => get_template_directory_uri() . '/assets/images/logo.jpg',
            'description' => get_bloginfo('description'),
            'address' => array(
                '@type' => 'PostalAddress',
                'addressLocality' => 'Bakı',
                'addressCountry' => 'AZ'
            ),
            'contactPoint' => array(
                '@type' => 'ContactPoint',
                'telephone' => get_theme_mod('company_phone', '+994503701522'),
                'contactType' => 'customer service'
            ),
            'sameAs' => array(
                'https://www.facebook.com/alachiqgroup',
                'https://www.instagram.com/alachiqgroup'
            )
        );
    }
    
    // Lightbox2 for decoration products and projects
    if (is_singular('decoration_products') || is_singular('projects') || is_post_type_archive('projects') || (is_page() && get_page_template_slug() == 'archive-projects.php')) {
        wp_enqueue_script('lightbox-js', 'https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox.min.js', array('jquery'), '2.11.4', true);
        wp_enqueue_style('lightbox-css', 'https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css', array(), '2.11.4');

        // Configure Lightbox2 options
        wp_add_inline_script('lightbox-js', '
            lightbox.option({
                "resizeDuration": 200,
                "wrapAround": true,
                "albumLabel": "Şəkil %1 / %2",
                "fadeDuration": 300,
                "imageFadeDuration": 300
            });
        ');
    }

    // Localize script for AJAX
    wp_localize_script('alachiq-scripts', 'alachiq_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('alachiq_nonce')
    ));
    
    // Add post ID to body for single project pages
    if (is_singular('projects')) {
        wp_localize_script('alachiq-single-project-js', 'alachiq_project', array(
            'post_id' => get_the_ID(),
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('alachiq_project_nonce')
        ));
    }
    
}

/**
 * Preload critical resources
 */
function alachiqgroup_pro_preload_resources() {
    if (is_front_page()) {
        echo '<link rel="preload" href="' . get_template_directory_uri() . '/img/sla4.jpg" as="image">' . "\n";
        echo '<link rel="preload" href="' . get_template_directory_uri() . '/img/slider_2.jpg" as="image">' . "\n";
        echo '<link rel="preload" href="' . get_template_directory_uri() . '/img/slider_3.jpg" as="image">' . "\n";
    }
}
add_action('wp_head', 'alachiqgroup_pro_preload_resources', 1);

/**
 * Add modern meta tags
 */
function alachiqgroup_pro_modern_meta_tags() {
    echo '<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">' . "\n";
    echo '<meta name="theme-color" content="#D9C49C">' . "\n";
    echo '<meta name="apple-mobile-web-app-capable" content="yes">' . "\n";
    echo '<meta name="apple-mobile-web-app-status-bar-style" content="default">' . "\n";
    echo '<meta name="apple-mobile-web-app-title" content="Alachiq Group">' . "\n";
}
add_action('wp_head', 'alachiqgroup_pro_modern_meta_tags', 1);

/**
 * Add structured data for local business
 */
function alachiqgroup_pro_structured_data() {
    if (is_front_page()) {
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => 'Alachiq Group',
            'description' => 'Peşəkar tikinti və dizayn xidmətləri',
            'url' => home_url(),
            'telephone' => '+994501234567',
            'address' => array(
                '@type' => 'PostalAddress',
                'addressCountry' => 'AZ',
                'addressLocality' => 'Bakı'
            ),
            'geo' => array(
                '@type' => 'GeoCoordinates',
                'latitude' => '40.3777',
                'longitude' => '49.8920'
            ),
            'openingHours' => 'Mo-Fr 09:00-18:00',
            'priceRange' => '$$',
            'sameAs' => array(
                'https://facebook.com/alachiqgroup',
                'https://instagram.com/alachiqgroup'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
    }
}
add_action('wp_head', 'alachiqgroup_pro_structured_data', 1);
add_action('wp_enqueue_scripts', 'alachiqgroup_pro_scripts');

/**
 * Debug function to help identify page loading issues
 * Remove this in production
 */
function alachiq_debug_page_info() {
    if (current_user_can('administrator') && isset($_GET['debug'])) {
        echo '<!-- DEBUG INFO: ';
        echo 'URL: ' . $_SERVER['REQUEST_URI'] . ' | ';
        echo 'is_page: ' . (is_page() ? 'true' : 'false') . ' | ';
        echo 'is_front_page: ' . (is_front_page() ? 'true' : 'false') . ' | ';
        echo 'is_home: ' . (is_home() ? 'true' : 'false') . ' | ';
        echo 'post_type: ' . get_post_type() . ' | ';
        if (is_page()) {
            echo 'page_slug: ' . get_post_field('post_name', get_post()) . ' | ';
            echo 'page_title: ' . get_the_title() . ' | ';
        }
        echo '-->';
    }
}
add_action('wp_head', 'alachiq_debug_page_info');

// Post views functionality
function get_post_views($post_id) {
    $count_key = 'post_views_count';
    $count = get_post_meta($post_id, $count_key, true);
    if ($count == '') {
        delete_post_meta($post_id, $count_key);
        add_post_meta($post_id, $count_key, '0');
        return "0";
    }
    return $count;
}

function set_post_views($post_id) {
    $count_key = 'post_views_count';
    $count = get_post_meta($post_id, $count_key, true);
    if ($count == '') {
        $count = 0;
        delete_post_meta($post_id, $count_key);
        add_post_meta($post_id, $count_key, '0');
    } else {
        $count++;
        update_post_meta($post_id, $count_key, $count);
    }
}

// AJAX handler for incrementing post views
function alachiqgroup_pro_increment_post_views() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'alachiq_project_nonce')) {
        wp_die('Security check failed');
    }
    
    $post_id = intval($_POST['post_id']);
    
    if ($post_id > 0) {
        set_post_views($post_id);
        wp_send_json_success(array('views' => get_post_views($post_id)));
    } else {
        wp_send_json_error('Invalid post ID');
    }
}
add_action('wp_ajax_increment_post_views', 'alachiqgroup_pro_increment_post_views');
add_action('wp_ajax_nopriv_increment_post_views', 'alachiqgroup_pro_increment_post_views');

// Emergency inline CSS fallback
function alachiqgroup_pro_emergency_styles() {
    if (is_front_page() || is_home()) {
        echo '<style id="alachiq-emergency-styles">' . "\n";
        echo '/* Emergency fallback styles - loads immediately */' . "\n";
        echo 'body { font-family: "Montserrat", sans-serif !important; margin: 0; padding: 0; }' . "\n";
        echo '.hero-clean { background: #403124; color: white; min-height: 100vh; display: flex; align-items: center; }' . "\n";
        echo '.alachiq-container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }' . "\n";
        echo '.hero-title { font-size: 3rem; font-weight: 700; margin-bottom: 1rem; }' . "\n";
        echo '.hero-text { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }' . "\n";
        echo '.btn-primary { background: #D9C49C; color: #403124; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: 600; }' . "\n";
        echo '.services-clean { padding: 80px 0; }' . "\n";
        echo '.services-grid { display: grid !important; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important; gap: 30px !important; width: 100% !important; }' . "\n";
        echo '.service-item { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); text-align: center; }' . "\n";
        echo '/* Image placeholder fallback */' . "\n";
        echo 'img[src=""], img:not([src]), img[src*="placeholder.jpg"]:not([src]) { background: #f8f9fa; border: 2px dashed #dee2e6; display: inline-block; width: 100%; height: 200px; position: relative; }' . "\n";
        echo 'img[src=""]::before, img:not([src])::before { content: "Şəkil Yüklənmir"; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #6c757d; font-size: 14px; }' . "\n";
    }
    
    // Emergency decorations page styles
    if (strpos($_SERVER['REQUEST_URI'], '/decorations') !== false || strpos($_SERVER['REQUEST_URI'], '/dekorasiya') !== false) {
        echo '<style id="alachiq-decorations-emergency">' . "\n";
        echo '/* Emergency decorations styles */' . "\n";
        echo 'body { font-family: "Montserrat", sans-serif !important; }' . "\n";
        echo '.decorations-page { background: #fcfaf8; min-height: 100vh; }' . "\n";
        echo '.decorations-hero { background: #403124; color: white; padding: 6rem 0 4rem; text-align: center; }' . "\n";
        echo '.decorations-hero__title { font-size: 3rem; font-weight: 700; margin-bottom: 1rem; color: #D9C49C; }' . "\n";
        echo '.decorations-grid { padding: 6rem 0; }' . "\n";
        echo '.decorations-grid__container { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; }' . "\n";
        echo '.decoration-item { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease; }' . "\n";
        echo '.decoration-item:hover { transform: translateY(-5px); }' . "\n";
        echo '.decoration-item img { width: 100%; height: 250px; object-fit: cover; }' . "\n";
        echo '.decoration-item__content { padding: 1.5rem; }' . "\n";
        echo '.decoration-item__title { font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #403124; }' . "\n";
        echo '.decoration-item__price { font-size: 1.1rem; font-weight: 700; color: #D9C49C; }' . "\n";
        echo '</style>' . "\n";
    }
}
add_action('wp_head', 'alachiqgroup_pro_emergency_styles', 1);

// Custom post type for Projects
function alachiqgroup_pro_custom_post_types() {
    // Projects post type
    register_post_type('projects', array(
        'labels' => array(
            'name' => pll___safe('Layihələr'),
            'singular_name' => pll___safe('Layihə'),
            'add_new' => pll___safe('Yeni Layihə Əlavə Et'),
            'add_new_item' => pll___safe('Yeni Layihə Əlavə Et'),
            'edit_item' => pll___safe('Layihəni Redaktə Et'),
            'new_item' => pll___safe('Yeni Layihə'),
            'view_item' => pll___safe('Layihəyə Bax'),
            'search_items' => pll___safe('Layihələri Axtar'),
            'not_found' => pll___safe('Layihə tapılmadı'),
            'not_found_in_trash' => pll___safe('Zibil qutusunda layihə tapılmadı'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-building',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'projects'),
    ));

    // Services post type
    register_post_type('services', array(
        'labels' => array(
            'name' => pll___safe('Xidmətlər'),
            'singular_name' => pll___safe('Xidmət'),
            'add_new' => pll___safe('Yeni Xidmət Əlavə Et'),
            'add_new_item' => pll___safe('Yeni Xidmət Əlavə Et'),
            'edit_item' => pll___safe('Xidməti Redaktə Et'),
            'new_item' => pll___safe('Yeni Xidmət'),
            'view_item' => pll___safe('Xidmətə Bax'),
            'search_items' => pll___safe('Xidmətləri Axtar'),
            'not_found' => pll___safe('Xidmət tapılmadı'),
            'not_found_in_trash' => pll___safe('Zibil qutusunda xidmət tapılmadı'),
        ),
        'public' => true,
        'has_archive' => false,
        'menu_icon' => 'dashicons-hammer',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'service'),
    ));

    // Carousel Slides post type
    register_post_type('carousel_slides', array(
        'labels' => array(
            'name' => pll___safe('Karusel Slaydları'),
            'singular_name' => pll___safe('Karusel Slaydı'),
            'add_new' => pll___safe('Yeni Slayd Əlavə Et'),
            'add_new_item' => pll___safe('Yeni Slayd Əlavə Et'),
            'edit_item' => pll___safe('Slaydı Redaktə Et'),
            'new_item' => pll___safe('Yeni Slayd'),
            'view_item' => pll___safe('Slaydı Gör'),
            'search_items' => pll___safe('Slaydları Axtar'),
            'not_found' => pll___safe('Slayd tapılmadı'),
            'not_found_in_trash' => pll___safe('Zibil qutusunda slayd tapılmadı'),
        ),
        'public' => false,
        'show_ui' => true,
        'menu_icon' => 'dashicons-images-alt2',
        'supports' => array('title', 'thumbnail', 'page-attributes'),
        'menu_position' => 25,
        'capability_type' => 'post',
        'show_in_menu' => true,
    ));

    // Project categories
    register_taxonomy('project_category', 'projects', array(
        'labels' => array(
            'name' => pll___safe('Layihə Kateqoriyaları'),
            'singular_name' => pll___safe('Layihə Kateqoriyası'),
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'project-category'),
    ));

    // Decoration Products post type
    register_post_type('decoration_products', array(
        'labels' => array(
            'name' => pll___safe('Dekorasiya Məhsulları'),
            'singular_name' => pll___safe('Dekorasiya Məhsulu'),
            'add_new' => pll___safe('Yeni Məhsul'),
            'add_new_item' => pll___safe('Yeni Məhsul Əlavə Et'),
            'edit_item' => pll___safe('Məhsulu Redaktə Et'),
            'new_item' => pll___safe('Yeni Məhsul'),
            'view_item' => pll___safe('Məhsulu Gör'),
            'search_items' => pll___safe('Məhsul Axtar'),
            'not_found' => pll___safe('Məhsul tapılmadı'),
            'not_found_in_trash' => pll___safe('Zibil qutusunda məhsul tapılmadı'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-store',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'decoration-products'),
        'show_in_rest' => true,
    ));

    // Product categories taxonomy
    register_taxonomy('product_category', 'decoration_products', array(
        'labels' => array(
            'name' => pll___safe('Məhsul Kateqoriyaları'),
            'singular_name' => pll___safe('Məhsul Kateqoriyası'),
            'search_items' => pll___safe('Kateqoriya Axtar'),
            'all_items' => pll___safe('Bütün Kateqoriyalar'),
            'parent_item' => pll___safe('Ana Kateqoriya'),
            'parent_item_colon' => pll___safe('Ana Kateqoriya:'),
            'edit_item' => pll___safe('Kateqoriyanı Redaktə Et'),
            'update_item' => pll___safe('Kateqoriyanı Yenilə'),
            'add_new_item' => pll___safe('Yeni Kateqoriya Əlavə Et'),
            'new_item_name' => pll___safe('Yeni Kateqoriya Adı'),
            'menu_name' => pll___safe('Kateqoriyalar'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'product-category'),
        'show_in_rest' => true,
    ));

    // Service categories taxonomy
    register_taxonomy('service_category', 'services', array(
        'labels' => array(
            'name' => pll___safe('Xidmət Kateqoriyaları'),
            'singular_name' => pll___safe('Xidmət Kateqoriyası'),
            'search_items' => pll___safe('Kateqoriya Axtar'),
            'all_items' => pll___safe('Bütün Kateqoriyalar'),
            'parent_item' => pll___safe('Ana Kateqoriya'),
            'parent_item_colon' => pll___safe('Ana Kateqoriya:'),
            'edit_item' => pll___safe('Kateqoriyanı Redaktə Et'),
            'update_item' => pll___safe('Kateqoriyanı Yenilə'),
            'add_new_item' => pll___safe('Yeni Kateqoriya Əlavə Et'),
            'new_item_name' => pll___safe('Yeni Kateqoriya Adı'),
            'menu_name' => pll___safe('Kateqoriyalar'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'service-category'),
        'show_in_rest' => true,
    ));
}
add_action('init', 'alachiqgroup_pro_custom_post_types');

// Flush rewrite rules on theme activation
function alachiqgroup_pro_flush_rewrites() {
    alachiqgroup_pro_custom_post_types();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'alachiqgroup_pro_flush_rewrites');

// Widget areas
function alachiqgroup_pro_widgets_init() {
    register_sidebar(array(
        'name' => pll___safe('Footer Widget 1'),
        'id' => 'footer-1',
        'description' => pll___safe('Footer-də görünmək üçün widget-lər əlavə edin.'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));

    register_sidebar(array(
        'name' => pll___safe('Footer Widget 2'),
        'id' => 'footer-2',
        'description' => pll___safe('Footer-də görünmək üçün widget-lər əlavə edin.'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));

    register_sidebar(array(
        'name' => pll___safe('Footer Widget 3'),
        'id' => 'footer-3',
        'description' => pll___safe('Footer-də görünmək üçün widget-lər əlavə edin.'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
}
add_action('widgets_init', 'alachiqgroup_pro_widgets_init');

// Theme customizer
function alachiqgroup_pro_customize_register($wp_customize) {
    // Company Info Section
    $wp_customize->add_section('company_info', array(
        'title' => pll___safe('Şirkət Məlumatları'),
        'priority' => 30,
    ));

    // Phone number
    $wp_customize->add_setting('company_phone', array(
        'default' => '+994 50 370 15 22',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_phone', array(
        'label' => pll___safe('Telefon Nömrəsi'),
        'section' => 'company_info',
        'type' => 'text',
    ));

    // WhatsApp number
    $wp_customize->add_setting('company_whatsapp', array(
        'default' => '994503701522',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_whatsapp', array(
        'label' => pll___safe('WhatsApp Nömrəsi'),
        'section' => 'company_info',
        'type' => 'text',
    ));

    // Email
    $wp_customize->add_setting('company_email', array(
        'default' => '<EMAIL>',
        'sanitize_callback' => 'sanitize_email',
    ));

    $wp_customize->add_control('company_email', array(
        'label' => pll___safe('E-poçt Ünvanı'),
        'section' => 'company_info',
        'type' => 'email',
    ));

    // Work hours
    $wp_customize->add_setting('work_hours', array(
        'default' => 'İş vaxtı: həftə içi 09:00-18:00',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('work_hours', array(
        'label' => pll___safe('İş Saatları'),
        'section' => 'company_info',
        'type' => 'text',
    ));
    
    // Company Statistics Section
    $wp_customize->add_section('company_stats', array(
        'title' => pll___safe('Şirkət Statistikaları'),
        'priority' => 35,
    ));

    // Years of experience
    $wp_customize->add_setting('company_experience_years', array(
        'default' => '10+',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_experience_years', array(
        'label' => pll___safe('Təcrübə İlləri'),
        'section' => 'company_stats',
        'type' => 'text',
        'description' => pll___safe('Məsələn: 10+ və ya 15'),
    ));

    // Total projects
    $wp_customize->add_setting('company_total_projects', array(
        'default' => '500+',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_total_projects', array(
        'label' => pll___safe('Ümumi Layihə Sayı'),
        'section' => 'company_stats',
        'type' => 'text',
        'description' => pll___safe('Məsələn: 500+ və ya 750'),
    ));

    // Happy clients
    $wp_customize->add_setting('company_happy_clients', array(
        'default' => '200+',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_happy_clients', array(
        'label' => pll___safe('Məmnun Müştəri Sayı'),
        'section' => 'company_stats',
        'type' => 'text',
        'description' => pll___safe('Məsələn: 200+ və ya 350'),
    ));

    // Team members
    $wp_customize->add_setting('company_team_members', array(
        'default' => '25+',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_team_members', array(
        'label' => pll___safe('Komanda Üzvləri'),
        'section' => 'company_stats',
        'type' => 'text',
        'description' => pll___safe('Məsələn: 25+ və ya 30'),
    ));
}
add_action('customize_register', 'alachiqgroup_pro_customize_register');

// Add custom meta boxes for projects and carousel slides
function alachiqgroup_pro_add_project_meta_boxes() {
    add_meta_box(
        'project_details',
        pll___safe('Layihə Təfərrüatları'),
        'alachiqgroup_pro_project_details_callback',
        'projects',
        'normal',
        'high'
    );

    add_meta_box(
        'project_gallery',
        pll___safe('Layihə Qalereyası'),
        'alachiqgroup_pro_project_gallery_callback',
        'projects',
        'normal',
        'high'
    );

    // Carousel slide meta boxes
    add_meta_box(
        'carousel_slide_details',
        pll___safe('Slayd Təfərrüatları'),
        'alachiqgroup_pro_carousel_slide_details_callback',
        'carousel_slides',
        'normal',
        'high'
    );

    // Service meta boxes
    add_meta_box(
        'service_details',
        pll___safe('Xidmət Təfərrüatları'),
        'alachiqgroup_pro_service_details_callback',
        'services',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'alachiqgroup_pro_add_project_meta_boxes');

// Project details meta box callback
function alachiqgroup_pro_project_details_callback($post) {
    wp_nonce_field('alachiqgroup_pro_save_project_details', 'alachiqgroup_pro_project_details_nonce');

    $area = get_post_meta($post->ID, '_project_area', true);
    $floors = get_post_meta($post->ID, '_project_floors', true);
    $project_type = get_post_meta($post->ID, '_project_type', true);

    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th><label for="project_area">' . pll___safe('Layihə Sahəsi (m²)') . '</label></th>';
    echo '<td><input type="number" id="project_area" name="project_area" value="' . esc_attr($area) . '" min="0" step="0.1" /></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="project_floors">' . pll___safe('Mərtəbə Sayı') . '</label></th>';
    echo '<td><input type="number" id="project_floors" name="project_floors" value="' . esc_attr($floors) . '" min="0" max="10" /></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="project_type">' . pll___safe('Layihə Növü') . '</label></th>';
    echo '<td>';
    echo '<select id="project_type" name="project_type">';
    echo '<option value="">' . pll___safe('Növ Seçin') . '</option>';
    echo '<option value="residential"' . selected($project_type, 'residential', false) . '>' . pll___safe('Yaşayış') . '</option>';
    echo '<option value="commercial"' . selected($project_type, 'commercial', false) . '>' . pll___safe('Ticarət') . '</option>';
    echo '<option value="interior"' . selected($project_type, 'interior', false) . '>' . pll___safe('İnteryer Dizayn') . '</option>';
    echo '<option value="landscape"' . selected($project_type, 'landscape', false) . '>' . pll___safe('Landşaft') . '</option>';
    echo '</select>';
    echo '</td>';
    echo '</tr>';
    echo '</table>';
}

// Carousel slide details meta box callback
function alachiqgroup_pro_carousel_slide_details_callback($post) {
    wp_nonce_field('alachiqgroup_pro_save_carousel_slide_details', 'alachiqgroup_pro_carousel_slide_details_nonce');

    $subtitle = get_post_meta($post->ID, '_slide_subtitle', true);
    $button1_text = get_post_meta($post->ID, '_slide_button1_text', true);
    $button1_url = get_post_meta($post->ID, '_slide_button1_url', true);
    $button2_text = get_post_meta($post->ID, '_slide_button2_text', true);
    $button2_url = get_post_meta($post->ID, '_slide_button2_url', true);

    echo '<table class="form-table">';

    echo '<tr>';
    echo '<th scope="row"><label for="slide_subtitle">' . pll___safe('Slayd Təsviri') . '</label></th>';
    echo '<td><textarea id="slide_subtitle" name="slide_subtitle" rows="3" cols="50" style="width: 100%;">' . esc_textarea($subtitle) . '</textarea>';
    echo '<p class="description">' . pll___safe('Slaydın əsas başlığının altında göstəriləcək təsvir mətni') . '</p></td>';
    echo '</tr>';

    echo '<tr>';
    echo '<th scope="row"><label for="slide_button1_text">' . pll___safe('Birinci Düymə Mətni') . '</label></th>';
    echo '<td><input type="text" id="slide_button1_text" name="slide_button1_text" value="' . esc_attr($button1_text) . '" style="width: 100%;" />';
    echo '<p class="description">' . pll___safe('Birinci düymənin üzərində göstəriləcək mətn') . '</p></td>';
    echo '</tr>';

    echo '<tr>';
    echo '<th scope="row"><label for="slide_button1_url">' . pll___safe('Birinci Düymə Linki') . '</label></th>';
    echo '<td><input type="url" id="slide_button1_url" name="slide_button1_url" value="' . esc_attr($button1_url) . '" style="width: 100%;" />';
    echo '<p class="description">' . pll___safe('Birinci düymənin yönləndirəcəyi səhifənin linki') . '</p></td>';
    echo '</tr>';

    echo '<tr>';
    echo '<th scope="row"><label for="slide_button2_text">' . pll___safe('İkinci Düymə Mətni') . '</label></th>';
    echo '<td><input type="text" id="slide_button2_text" name="slide_button2_text" value="' . esc_attr($button2_text) . '" style="width: 100%;" />';
    echo '<p class="description">' . pll___safe('İkinci düymənin üzərində göstəriləcək mətn') . '</p></td>';
    echo '</tr>';

    echo '<tr>';
    echo '<th scope="row"><label for="slide_button2_url">' . pll___safe('İkinci Düymə Linki') . '</label></th>';
    echo '<td><input type="url" id="slide_button2_url" name="slide_button2_url" value="' . esc_attr($button2_url) . '" style="width: 100%;" />';
    echo '<p class="description">' . pll___safe('İkinci düymənin yönləndirəcəyi səhifənin linki') . '</p></td>';
    echo '</tr>';

    echo '</table>';

    echo '<div style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-left: 4px solid #D9C49C;">';
    echo '<h4>' . pll___safe('Təlimatlar:') . '</h4>';
    echo '<ul>';
    echo '<li>' . pll___safe('Slayd başlığı: Yuxarıdakı "Başlıq" sahəsini istifadə edin') . '</li>';
    echo '<li>' . pll___safe('Slayd şəkli: Sağ tərəfdəki "Əsas şəkil" bölməsindən şəkil əlavə edin') . '</li>';
    echo '<li>' . pll___safe('Tövsiyə olunan şəkil ölçüsü: 1200x700 piksel') . '</li>';
    echo '<li>' . pll___safe('Slaydların sırası: Sağ tərəfdəki "Səhifə Atributları" bölməsindəki "Sıra" sahəsini istifadə edin') . '</li>';
    echo '</ul>';

    // Debug information
    $last_saved = get_post_meta($post->ID, '_slide_last_saved', true);
    if ($last_saved) {
        echo '<p><strong>Son yenilənmə:</strong> ' . $last_saved . '</p>';
    }

    echo '</div>';
}

// Project gallery meta box callback
function alachiqgroup_pro_project_gallery_callback($post) {
    wp_nonce_field('save_project_gallery', 'project_gallery_nonce');

    // Get gallery data
    $gallery_images = get_post_meta($post->ID, '_project_gallery', true);

    if (empty($gallery_images)) {
        $gallery_images = get_post_meta($post->ID, 'project_gallery_backup', true);
    }
    if (empty($gallery_images)) {
        $gallery_images = get_post_meta($post->ID, '_gallery_test', true);
    }

    // Ensure it's always an array
    if (!is_array($gallery_images)) {
        if (!empty($gallery_images)) {
            // If it's a string, try to convert it
            if (is_string($gallery_images)) {
                $gallery_images = explode(',', $gallery_images);
                $gallery_images = array_filter(array_map('trim', $gallery_images));
            } else {
                // If it's not a string and not empty, it might be a serialized array
                $gallery_images = maybe_unserialize($gallery_images);
                if (!is_array($gallery_images)) {
                    $gallery_images = array();
                }
            }
        } else {
            $gallery_images = array();
        }
    }



    echo '<div id="project-gallery-container">';
    echo '<div id="project-gallery-images">';

    if (!empty($gallery_images)) {
        foreach ($gallery_images as $image_id) {
            $image_data = wp_get_attachment_image_src($image_id, 'thumbnail');
            if ($image_data) {
                $image_url = $image_data[0];
                echo '<div class="gallery-image-item" data-id="' . $image_id . '">';
                echo '<img src="' . esc_url($image_url) . '" alt="" />';
                echo '<button type="button" class="remove-gallery-image">×</button>';
                echo '</div>';
            }
        }
    }

    echo '</div>';
    echo '<button type="button" id="add-gallery-images" class="button">' . pll___safe('Şəkil Əlavə Et') . '</button>';
    echo '<div id="gallery-save-status" style="margin: 10px 0; font-weight: bold;"></div>';
    echo '<br>';

    // Ensure we have a string value for the field
    if (is_array($gallery_images)) {
        $field_value = implode(',', array_filter($gallery_images));
    } else {
        $field_value = '';
    }
    echo '<input type="hidden" id="project_gallery" name="project_gallery" value="' . esc_attr($field_value) . '" />';
    echo '<p><em>Gallery auto-saves when you add/remove images.</em></p>';
    echo '</div>';

    // Add JavaScript for gallery management
    echo '<script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        // Initialize gallery field if empty
        if (!$("#project_gallery").val()) {
            // Try to rebuild gallery field from existing DOM images
            var domImageIds = [];
            $("#project-gallery-images .gallery-image-item").each(function() {
                var imageId = $(this).data("id");
                if (imageId) {
                    domImageIds.push(imageId.toString());
                }
            });

            if (domImageIds.length > 0) {
                $("#project_gallery").val(domImageIds.join(","));
                saveGalleryAjax();
            } else {
                $("#project_gallery").val("");
            }
        }

        // Rebuild gallery preview from saved IDs on page load
        function rebuildGalleryPreview() {
            var galleryValue = $("#project_gallery").val();

            if (!galleryValue) {
                return;
            }

            var imageIds = galleryValue.split(",").filter(Boolean);
            $("#project-gallery-images").empty();

            imageIds.forEach(function(imageId) {
                // Add a placeholder that will be replaced when we get the image URL
                $("#project-gallery-images").append(
                    "<div class=\"gallery-image-item\" data-id=\"" + imageId + "\">" +
                    "<div style=\"width: 100px; height: 100px; background: #f0f0f0; display: flex; align-items: center; justify-content: center;\">Loading...</div>" +
                    "<button type=\"button\" class=\"remove-gallery-image\">×</button>" +
                    "</div>"
                );

                // Get the actual image URL via AJAX
                $.post(ajaxurl, {
                    action: "get_attachment_url",
                    attachment_id: imageId
                }, function(response) {
                    if (response && response !== "0") {
                        $(".gallery-image-item[data-id=\"" + imageId + "\"] div").replaceWith(
                            "<img src=\"" + response + "\" alt=\"\" style=\"width: 100px; height: 100px; object-fit: cover;\" />"
                        );
                    }
                });
            });
        }

        // Rebuild on page load
        rebuildGalleryPreview();

        // Auto-save gallery via AJAX
        function saveGalleryAjax() {
            var galleryValue = $("#project_gallery").val();
            var postId = $("#post_ID").val();

            if (!postId) {
                $("#gallery-save-status").html("<span style=\"color: red;\">✗ No post ID</span>");
                return;
            }

            $("#gallery-save-status").html("<span style=\"color: orange;\">Saving...</span>");

            $.post(ajaxurl, {
                action: "save_project_gallery",
                post_id: postId,
                gallery_ids: galleryValue
            }, function(response) {
                if (response === "success") {
                    $("#gallery-save-status").html("<span style=\"color: green;\">✓ Gallery saved (" + (galleryValue ? galleryValue.split(",").length : 0) + " images)</span>");
                } else {
                    $("#gallery-save-status").html("<span style=\"color: red;\">✗ Save failed</span>");
                }
            }).fail(function() {
                $("#gallery-save-status").html("<span style=\"color: red;\">✗ AJAX error</span>");
            });
        }

        $("#add-gallery-images").click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            mediaUploader = wp.media({
                title: "Select Images for Gallery",
                button: { text: "Add to Gallery" },
                multiple: true
            });

            mediaUploader.on("select", function() {
                var attachments = mediaUploader.state().get("selection").toJSON();
                var currentValue = $("#project_gallery").val();
                var currentIds = currentValue ? currentValue.split(",").filter(Boolean) : [];

                attachments.forEach(function(attachment) {
                    if (currentIds.indexOf(attachment.id.toString()) === -1) {
                        currentIds.push(attachment.id.toString());

                        var thumbnailUrl = attachment.sizes && attachment.sizes.thumbnail ?
                            attachment.sizes.thumbnail.url : attachment.url;

                        $("#project-gallery-images").append(
                            "<div class=\"gallery-image-item\" data-id=\"" + attachment.id + "\">" +
                            "<img src=\"" + thumbnailUrl + "\" alt=\"\" />" +
                            "<button type=\"button\" class=\"remove-gallery-image\">×</button>" +
                            "</div>"
                        );
                    }
                });

                $("#project_gallery").val(currentIds.join(","));

                // Use debouncing for adding images
                clearTimeout(saveTimeout);
                $("#gallery-save-status").html("<span style=\"color: orange;\">Preparing to save...</span>");

                saveTimeout = setTimeout(function() {
                    saveGalleryAjax();
                }, 1000);
            });

            mediaUploader.open();
        });

        // Add debouncing to prevent rapid-fire AJAX calls
        var saveTimeout;

        $(document).on("click", ".remove-gallery-image", function() {
            var $item = $(this).parent();
            var imageId = $item.data("id").toString();
            var currentValue = $("#project_gallery").val();
            var currentIds = currentValue ? currentValue.split(",").filter(Boolean) : [];
            var index = currentIds.indexOf(imageId);

            if (index > -1) {
                currentIds.splice(index, 1);
            }

            $item.remove();
            $("#project_gallery").val(currentIds.join(","));

            // Clear any pending save and delay the new save to prevent conflicts
            clearTimeout(saveTimeout);
            $("#gallery-save-status").html("<span style=\"color: orange;\">Preparing to save...</span>");

            saveTimeout = setTimeout(function() {
                saveGalleryAjax();
            }, 1000);
        });

        // Save gallery whenever it changes
        $("#project_gallery").on("change keyup", function() {
            saveGalleryAjax();
        });
    });
    </script>';

    echo '<style>
    #project-gallery-images {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
    }
    .gallery-image-item {
        position: relative;
        display: inline-block;
    }
    .gallery-image-item img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .remove-gallery-image {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3232;
        color: white;
        border: none;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        cursor: pointer;
        font-size: 12px;
        line-height: 1;
    }
    </style>';
}

// Get recent projects
function get_recent_projects($limit = 4) {
    $projects = get_posts(array(
        'post_type' => 'projects',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    return $projects;
}

// Save project meta data
function alachiqgroup_pro_save_project_meta($post_id) {

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check if this is a revision
    if (wp_is_post_revision($post_id)) {
        return;
    }

    // Check post type
    if (get_post_type($post_id) !== 'projects') {
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save project details
    if (isset($_POST['alachiqgroup_pro_project_details_nonce']) &&
        wp_verify_nonce($_POST['alachiqgroup_pro_project_details_nonce'], 'alachiqgroup_pro_save_project_details')) {

        if (isset($_POST['project_area'])) {
            update_post_meta($post_id, '_project_area', sanitize_text_field($_POST['project_area']));
        }

        if (isset($_POST['project_floors'])) {
            update_post_meta($post_id, '_project_floors', intval($_POST['project_floors']));
        }

        if (isset($_POST['project_type'])) {
            update_post_meta($post_id, '_project_type', sanitize_text_field($_POST['project_type']));
        }
    }

    // Save carousel slide details
    if (get_post_type($post_id) == 'carousel_slides') {
        if (isset($_POST['alachiqgroup_pro_carousel_slide_details_nonce']) &&
            wp_verify_nonce($_POST['alachiqgroup_pro_carousel_slide_details_nonce'], 'alachiqgroup_pro_save_carousel_slide_details')) {

            // Save slide subtitle
            if (isset($_POST['slide_subtitle'])) {
                update_post_meta($post_id, '_slide_subtitle', sanitize_textarea_field($_POST['slide_subtitle']));
            } else {
                delete_post_meta($post_id, '_slide_subtitle');
            }

            // Save button 1 text
            if (isset($_POST['slide_button1_text'])) {
                update_post_meta($post_id, '_slide_button1_text', sanitize_text_field($_POST['slide_button1_text']));
            } else {
                delete_post_meta($post_id, '_slide_button1_text');
            }

            // Save button 1 URL
            if (isset($_POST['slide_button1_url'])) {
                update_post_meta($post_id, '_slide_button1_url', esc_url_raw($_POST['slide_button1_url']));
            } else {
                delete_post_meta($post_id, '_slide_button1_url');
            }

            // Save button 2 text
            if (isset($_POST['slide_button2_text'])) {
                update_post_meta($post_id, '_slide_button2_text', sanitize_text_field($_POST['slide_button2_text']));
            } else {
                delete_post_meta($post_id, '_slide_button2_text');
            }

            // Save button 2 URL
            if (isset($_POST['slide_button2_url'])) {
                update_post_meta($post_id, '_slide_button2_url', esc_url_raw($_POST['slide_button2_url']));
            } else {
                delete_post_meta($post_id, '_slide_button2_url');
            }
        }
    }

    // Save project gallery
    if (isset($_POST['project_gallery'])) {
        $gallery_string = sanitize_text_field($_POST['project_gallery']);

        if (!empty($gallery_string)) {
            $gallery_ids = array_filter(array_map('intval', explode(',', $gallery_string)));
            update_post_meta($post_id, '_project_gallery', $gallery_ids);
        } else {
            delete_post_meta($post_id, '_project_gallery');
        }
    }
}
// DISABLED: These hooks were clearing the gallery on Update
// add_action('save_post', 'alachiqgroup_pro_save_project_meta', 1, 1);
// add_action('wp_insert_post', 'alachiqgroup_pro_save_project_meta', 1, 1);
// add_action('edit_post', 'alachiqgroup_pro_save_project_meta', 1, 1);

// Separate save function for carousel slides
function alachiqgroup_pro_save_carousel_slide_meta($post_id) {
    // Check if this is a carousel slide
    if (get_post_type($post_id) !== 'carousel_slides') {
        return;
    }

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Verify nonce
    if (!isset($_POST['alachiqgroup_pro_carousel_slide_details_nonce']) ||
        !wp_verify_nonce($_POST['alachiqgroup_pro_carousel_slide_details_nonce'], 'alachiqgroup_pro_save_carousel_slide_details')) {
        return;
    }

    // Save slide subtitle
    $subtitle = isset($_POST['slide_subtitle']) ? sanitize_textarea_field($_POST['slide_subtitle']) : '';
    update_post_meta($post_id, '_slide_subtitle', $subtitle);

    // Save button 1 text
    $button1_text = isset($_POST['slide_button1_text']) ? sanitize_text_field($_POST['slide_button1_text']) : '';
    update_post_meta($post_id, '_slide_button1_text', $button1_text);

    // Save button 1 URL
    $button1_url = isset($_POST['slide_button1_url']) ? esc_url_raw($_POST['slide_button1_url']) : '';
    update_post_meta($post_id, '_slide_button1_url', $button1_url);

    // Save button 2 text
    $button2_text = isset($_POST['slide_button2_text']) ? sanitize_text_field($_POST['slide_button2_text']) : '';
    update_post_meta($post_id, '_slide_button2_text', $button2_text);

    // Save button 2 URL
    $button2_url = isset($_POST['slide_button2_url']) ? esc_url_raw($_POST['slide_button2_url']) : '';
    update_post_meta($post_id, '_slide_button2_url', $button2_url);

    // Debug: Save a timestamp to verify the function is running
    update_post_meta($post_id, '_slide_last_saved', current_time('mysql'));
}
add_action('save_post', 'alachiqgroup_pro_save_carousel_slide_meta');

// Add meta boxes for decoration products
function alachiqgroup_pro_add_product_meta_boxes() {
    add_meta_box(
        'product_details',
        pll___safe('Məhsul Detalları'),
        'alachiqgroup_pro_product_details_callback',
        'decoration_products',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'alachiqgroup_pro_add_product_meta_boxes');

// Product details meta box callback
function alachiqgroup_pro_product_details_callback($post) {
    wp_nonce_field('alachiqgroup_pro_save_product_details', 'alachiqgroup_pro_product_details_nonce');
    
    $product_price = get_post_meta($post->ID, '_product_price', true);
    $product_currency = get_post_meta($post->ID, '_product_currency', true) ?: 'AZN';
    $product_duration = get_post_meta($post->ID, '_product_duration', true);
    $product_features = get_post_meta($post->ID, '_product_features', true);
    $product_gallery = get_post_meta($post->ID, '_product_gallery', true);
    
    // Ensure product gallery is properly handled
    if (!is_array($product_gallery)) {
        if (!empty($product_gallery)) {
            if (is_string($product_gallery)) {
                $product_gallery = explode(',', $product_gallery);
                $product_gallery = array_filter(array_map('trim', $product_gallery));
            } else {
                $product_gallery = maybe_unserialize($product_gallery);
                if (!is_array($product_gallery)) {
                    $product_gallery = array();
                }
            }
        } else {
            $product_gallery = array();
        }
    }
    
    echo '<table class="form-table">';
    
    // Price
    echo '<tr>';
    echo '<th><label for="product_price">' . pll___safe('Qiymət') . '</label></th>';
    echo '<td>';
    echo '<input type="number" id="product_price" name="product_price" value="' . esc_attr($product_price) . '" step="0.01" min="0" style="width: 150px;" />';
    echo '<select name="product_currency" style="margin-left: 10px;">';
    echo '<option value="AZN"' . selected($product_currency, 'AZN', false) . '>AZN</option>';
    echo '<option value="USD"' . selected($product_currency, 'USD', false) . '>USD</option>';
    echo '<option value="EUR"' . selected($product_currency, 'EUR', false) . '>EUR</option>';
    echo '</select>';
    echo '</td>';
    echo '</tr>';
    
    // Duration
    echo '<tr>';
    echo '<th><label for="product_duration">' . pll___safe('Müddət') . '</label></th>';
    echo '<td>';
    echo '<input type="text" id="product_duration" name="product_duration" value="' . esc_attr($product_duration) . '" placeholder="Məsələn: 2-3 həftə" style="width: 300px;" />';
    echo '</td>';
    echo '</tr>';
    
    // Features
    echo '<tr>';
    echo '<th><label for="product_features">' . pll___safe('Xüsusiyyətlər') . '</label></th>';
    echo '<td>';
    echo '<textarea id="product_features" name="product_features" rows="5" style="width: 100%;" placeholder="Hər xüsusiyyəti yeni sətirdə yazın">' . esc_textarea($product_features) . '</textarea>';
    echo '<p class="description">' . pll___safe('Hər xüsusiyyəti yeni sətirdə yazın') . '</p>';
    echo '</td>';
    echo '</tr>';
    
    // Gallery
    echo '<tr>';
    echo '<th><label>' . pll___safe('Məhsul Qalereyası') . '</label></th>';
    echo '<td>';
    // Convert gallery array to string for the hidden input
    $gallery_value = is_array($product_gallery) ? implode(',', array_filter($product_gallery)) : '';
    echo '<input type="hidden" id="product_gallery" name="product_gallery" value="' . esc_attr($gallery_value) . '" />';
    echo '<button type="button" id="add-gallery-images" class="button">' . pll___safe('Qalereya Əlavə Et') . '</button>';
    echo '<div id="product-gallery-images"></div>';
    echo '</td>';
    echo '</tr>';
    
    echo '</table>';
    
    // JavaScript for gallery
    echo '<script>
    jQuery(document).ready(function($) {
        // Load existing gallery images on page load
        var existingGalleryIds = $("#product_gallery").val();
        if (existingGalleryIds) {
            var ids = existingGalleryIds.split(",").filter(Boolean);
            var galleryHtml = "";
            
            ids.forEach(function(id) {
                // Add placeholder that will be replaced with actual image
                galleryHtml += \'<div class="gallery-image-item" data-id="\' + id + \'">\';
                galleryHtml += \'<div style="width: 100px; height: 100px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; font-size: 12px;">Loading...</div>\';
                galleryHtml += \'<button type="button" class="remove-gallery-image">&times;</button>\';
                galleryHtml += \'</div>\';
            });
            
            $("#product-gallery-images").html(galleryHtml);
            
            // Load actual images via AJAX
            ids.forEach(function(id) {
                $.post(ajaxurl, {
                    action: "get_attachment_url",
                    attachment_id: id
                }, function(response) {
                    if (response && response !== "0") {
                        $(".gallery-image-item[data-id=\'" + id + "\'] div").replaceWith(
                            \'<img src="\' + response + \'" alt="" style="width: 100px; height: 100px; object-fit: cover;" />\'
                        );
                    }
                });
            });
        }
        $("#add-gallery-images").click(function(e) {
            e.preventDefault();
            var frame = wp.media({
                title: "' . pll___safe('Məhsul Qalereyası Seç') . '",
                button: {
                    text: "' . pll___safe('Seç') . '"
                },
                multiple: true
            });
            
            frame.on("select", function() {
                var attachments = frame.state().get("selection").map(function(attachment) {
                    attachment = attachment.toJSON();
                    return attachment;
                });
                
                var galleryIds = [];
                var galleryHtml = "";
                
                attachments.forEach(function(attachment) {
                    galleryIds.push(attachment.id);
                    galleryHtml += \'<div class="gallery-image-item" data-id="\' + attachment.id + \'">\';
                    galleryHtml += \'<img src="\' + attachment.sizes.thumbnail.url + \'" alt="\' + attachment.title + \'" />\';
                    galleryHtml += \'<button type="button" class="remove-gallery-image">&times;</button>\';
                    galleryHtml += \'</div>\';
                });
                
                $("#product_gallery").val(galleryIds.join(","));
                $("#product-gallery-images").html(galleryHtml);
            });
            
            frame.open();
        });
        
        $(document).on("click", ".remove-gallery-image", function() {
            var item = $(this).parent();
            var id = item.data("id");
            var currentIds = $("#product_gallery").val().split(",");
            var newIds = currentIds.filter(function(currentId) {
                return currentId != id;
            });
            $("#product_gallery").val(newIds.join(","));
            item.remove();
        });
    });
    </script>';
    
    echo '<style>
    #product-gallery-images {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
    }
    .gallery-image-item {
        position: relative;
        display: inline-block;
    }
    .gallery-image-item img {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .remove-gallery-image {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3232;
        color: white;
        border: none;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        cursor: pointer;
        font-size: 12px;
        line-height: 1;
    }
    </style>';
}

// Save product meta data
function alachiqgroup_pro_save_product_meta($post_id) {
    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check if this is a revision
    if (wp_is_post_revision($post_id)) {
        return;
    }

    // Check post type
    if (get_post_type($post_id) !== 'decoration_products') {
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Verify nonce
    if (!isset($_POST['alachiqgroup_pro_product_details_nonce']) ||
        !wp_verify_nonce($_POST['alachiqgroup_pro_product_details_nonce'], 'alachiqgroup_pro_save_product_details')) {
        return;
    }

    // Save product price
    if (isset($_POST['product_price'])) {
        update_post_meta($post_id, '_product_price', sanitize_text_field($_POST['product_price']));
    }

    // Save product currency
    if (isset($_POST['product_currency'])) {
        update_post_meta($post_id, '_product_currency', sanitize_text_field($_POST['product_currency']));
    }

    // Save product duration
    if (isset($_POST['product_duration'])) {
        update_post_meta($post_id, '_product_duration', sanitize_text_field($_POST['product_duration']));
    }

    // Save product features
    if (isset($_POST['product_features'])) {
        update_post_meta($post_id, '_product_features', sanitize_textarea_field($_POST['product_features']));
    }

    // Save product gallery
    if (isset($_POST['product_gallery'])) {
        $gallery_string = sanitize_text_field($_POST['product_gallery']);
        if (!empty($gallery_string)) {
            $gallery_ids = array_filter(array_map('intval', explode(',', $gallery_string)));
            update_post_meta($post_id, '_product_gallery', $gallery_ids);
        } else {
            delete_post_meta($post_id, '_product_gallery');
        }
    }
}
add_action('save_post', 'alachiqgroup_pro_save_product_meta');

// Service details meta box callback
function alachiqgroup_pro_service_details_callback($post) {
    wp_nonce_field('alachiqgroup_pro_save_service_details', 'alachiqgroup_pro_service_details_nonce');

    $service_price = get_post_meta($post->ID, '_service_price', true);
    $service_currency = get_post_meta($post->ID, '_service_currency', true) ?: 'AZN';
    $service_duration = get_post_meta($post->ID, '_service_duration', true);
    $service_features = get_post_meta($post->ID, '_service_features', true);
    $service_icon = get_post_meta($post->ID, '_service_icon', true);

    // Ensure service features is an array
    if (!is_array($service_features)) {
        $service_features = !empty($service_features) ? explode("\n", $service_features) : array();
    }

    echo '<table class="form-table">';

    // Icon
    echo '<tr>';
    echo '<th><label for="service_icon">' . pll___safe('İkon') . '</label></th>';
    echo '<td>';
    echo '<input type="text" id="service_icon" name="service_icon" value="' . esc_attr($service_icon) . '" style="width: 100%;" placeholder="fas fa-hammer" />';
    echo '<p class="description">' . pll___safe('FontAwesome ikon sinfini daxil edin (məsələn: fas fa-hammer)') . '</p>';
    echo '</td>';
    echo '</tr>';

    // Price
    echo '<tr>';
    echo '<th><label for="service_price">' . pll___safe('Qiymət') . '</label></th>';
    echo '<td>';
    echo '<input type="number" id="service_price" name="service_price" value="' . esc_attr($service_price) . '" step="0.01" min="0" style="width: 150px;" />';
    echo '<select name="service_currency" style="margin-left: 10px;">';
    echo '<option value="AZN"' . selected($service_currency, 'AZN', false) . '>AZN</option>';
    echo '<option value="USD"' . selected($service_currency, 'USD', false) . '>USD</option>';
    echo '<option value="EUR"' . selected($service_currency, 'EUR', false) . '>EUR</option>';
    echo '</select>';
    echo '<p class="description">' . pll___safe('Xidmətin qiyməti (isteğe bağlı)') . '</p>';
    echo '</td>';
    echo '</tr>';

    // Duration
    echo '<tr>';
    echo '<th><label for="service_duration">' . pll___safe('Müddət') . '</label></th>';
    echo '<td>';
    echo '<input type="text" id="service_duration" name="service_duration" value="' . esc_attr($service_duration) . '" style="width: 100%;" placeholder="2-3 həftə" />';
    echo '<p class="description">' . pll___safe('Xidmətin tamamlanma müddəti') . '</p>';
    echo '</td>';
    echo '</tr>';

    // Features
    echo '<tr>';
    echo '<th><label for="service_features">' . pll___safe('Xüsusiyyətlər') . '</label></th>';
    echo '<td>';
    echo '<textarea id="service_features" name="service_features" rows="5" style="width: 100%;" placeholder="' . pll___safe('Hər sətirdə bir xüsusiyyət yazın') . '">' . esc_textarea(implode("\n", $service_features)) . '</textarea>';
    echo '<p class="description">' . pll___safe('Xidmətin əsas xüsusiyyətlərini hər sətirdə bir dənə olmaqla yazın') . '</p>';
    echo '</td>';
    echo '</tr>';

    echo '</table>';
}


// Save service meta data
function alachiqgroup_pro_save_service_meta($post_id) {
    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check if this is a revision
    if (wp_is_post_revision($post_id)) {
        return;
    }

    // Check post type
    if (get_post_type($post_id) !== 'services') {
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Verify nonce
    if (!isset($_POST['alachiqgroup_pro_service_details_nonce']) ||
        !wp_verify_nonce($_POST['alachiqgroup_pro_service_details_nonce'], 'alachiqgroup_pro_save_service_details')) {
        return;
    }

    // Save service icon
    if (isset($_POST['service_icon'])) {
        update_post_meta($post_id, '_service_icon', sanitize_text_field($_POST['service_icon']));
    }

    // Save service price
    if (isset($_POST['service_price'])) {
        update_post_meta($post_id, '_service_price', floatval($_POST['service_price']));
    }

    // Save service currency
    if (isset($_POST['service_currency'])) {
        update_post_meta($post_id, '_service_currency', sanitize_text_field($_POST['service_currency']));
    }

    // Save service duration
    if (isset($_POST['service_duration'])) {
        update_post_meta($post_id, '_service_duration', sanitize_text_field($_POST['service_duration']));
    }

    // Save service features
    if (isset($_POST['service_features'])) {
        $features = sanitize_textarea_field($_POST['service_features']);
        $features_array = array_filter(array_map('trim', explode("\n", $features)));
        update_post_meta($post_id, '_service_features', $features_array);
    }
}
add_action('save_post', 'alachiqgroup_pro_save_service_meta');

// Create default service categories
function alachiqgroup_pro_create_default_service_categories() {
    $categories = array(
        'architecture-design' => 'Memarlıq və Dizayn',
        'construction' => 'Tikinti Xidmətləri',
        'repair-renovation' => 'Təmir və Yenilənmə',
        'furniture' => 'Mebel İşləri',
        'pool-construction' => 'Hovuz Tikintisi',
        'landscaping' => 'Abadlaşdırma'
    );

    foreach ($categories as $slug => $name) {
        if (!term_exists($slug, 'service_category')) {
            wp_insert_term($name, 'service_category', array(
                'slug' => $slug,
                'description' => $name . ' xidmətləri'
            ));
        }
    }
}
add_action('after_switch_theme', 'alachiqgroup_pro_create_default_service_categories');

// Create default product categories
function alachiqgroup_pro_create_default_product_categories() {
    $categories = array(
        'interior-decoration' => 'İnteryer Dekorasiyası',
        'lighting' => 'İşıqlandırma',
        'wall-ceiling' => 'Divar və Tavan',
        'garden-landscape' => 'Bağ və Həyət',
        'office-commercial' => 'Ofis və Ticarət',
        'events-ceremonies' => 'Mərasim və Tədbirlər'
    );
    
    foreach ($categories as $slug => $name) {
        if (!term_exists($slug, 'product_category')) {
            wp_insert_term($name, 'product_category', array(
                'slug' => $slug,
                'description' => $name . ' xidmətləri'
            ));
        }
    }
}
add_action('after_switch_theme', 'alachiqgroup_pro_create_default_product_categories');

// Add translation fields to term edit forms
function alachiqgroup_pro_add_term_translation_fields($term) {
    $current_lang = alachiqgroup_pro_get_current_language();
    $languages = array('en' => 'English', 'ru' => 'Russian');

    echo '<tr class="form-field">';
    echo '<th scope="row"><label>' . pll___safe('Tərcümələr') . '</label></th>';
    echo '<td>';

    foreach ($languages as $lang_code => $lang_name) {
        $translated_name = get_term_meta($term->term_id, "name_{$lang_code}", true);
        echo '<div style="margin-bottom: 10px;">';
        echo '<label for="term_name_' . $lang_code . '">' . $lang_name . ':</label><br>';
        echo '<input type="text" id="term_name_' . $lang_code . '" name="term_name_' . $lang_code . '" value="' . esc_attr($translated_name) . '" style="width: 100%; max-width: 400px;" />';
        echo '</div>';
    }

    echo '<p class="description">' . pll___safe('Bu kateqoriyanın digər dillərdə adını daxil edin.') . '</p>';
    echo '</td>';
    echo '</tr>';
}

// Save term translation fields
function alachiqgroup_pro_save_term_translation_fields($term_id) {
    $languages = array('en', 'ru');

    foreach ($languages as $lang_code) {
        if (isset($_POST["term_name_{$lang_code}"])) {
            $translated_name = sanitize_text_field($_POST["term_name_{$lang_code}"]);
            if (!empty($translated_name)) {
                update_term_meta($term_id, "name_{$lang_code}", $translated_name);
            } else {
                delete_term_meta($term_id, "name_{$lang_code}");
            }
        }
    }
}

// Hook translation fields to all relevant taxonomies
add_action('project_category_edit_form_fields', 'alachiqgroup_pro_add_term_translation_fields');
add_action('product_category_edit_form_fields', 'alachiqgroup_pro_add_term_translation_fields');
add_action('edited_project_category', 'alachiqgroup_pro_save_term_translation_fields');
add_action('edited_product_category', 'alachiqgroup_pro_save_term_translation_fields');

// Add translation fields to post edit screens
function alachiqgroup_pro_add_post_translation_meta_box() {
    $post_types = array('projects', 'decoration_products');

    foreach ($post_types as $post_type) {
        add_meta_box(
            'alachiqgroup_pro_translations',
            pll___safe('Tərcümələr'),
            'alachiqgroup_pro_post_translation_meta_box_callback',
            $post_type,
            'normal',
            'high'
        );
    }
}
add_action('add_meta_boxes', 'alachiqgroup_pro_add_post_translation_meta_box');

// Meta box callback for post translations
function alachiqgroup_pro_post_translation_meta_box_callback($post) {
    wp_nonce_field('alachiqgroup_pro_post_translations', 'alachiqgroup_pro_post_translations_nonce');

    $languages = array('en' => 'English', 'ru' => 'Russian');

    echo '<table class="form-table">';

    foreach ($languages as $lang_code => $lang_name) {
        $translated_title = get_post_meta($post->ID, "title_{$lang_code}", true);
        $translated_content = get_post_meta($post->ID, "content_{$lang_code}", true);
        $translated_excerpt = get_post_meta($post->ID, "excerpt_{$lang_code}", true);

        echo '<tr>';
        echo '<th scope="row"><strong>' . $lang_name . '</strong></th>';
        echo '<td>';

        echo '<div style="margin-bottom: 15px;">';
        echo '<label for="post_title_' . $lang_code . '">' . pll___safe('Başlıq') . ':</label><br>';
        echo '<input type="text" id="post_title_' . $lang_code . '" name="post_title_' . $lang_code . '" value="' . esc_attr($translated_title) . '" style="width: 100%;" />';
        echo '</div>';

        echo '<div style="margin-bottom: 15px;">';
        echo '<label for="post_excerpt_' . $lang_code . '">' . pll___safe('Qısa məzmun') . ':</label><br>';
        echo '<textarea id="post_excerpt_' . $lang_code . '" name="post_excerpt_' . $lang_code . '" rows="3" style="width: 100%;">' . esc_textarea($translated_excerpt) . '</textarea>';
        echo '</div>';

        echo '<div style="margin-bottom: 15px;">';
        echo '<label for="post_content_' . $lang_code . '">' . pll___safe('Məzmun') . ':</label><br>';
        echo '<textarea id="post_content_' . $lang_code . '" name="post_content_' . $lang_code . '" rows="8" style="width: 100%;">' . esc_textarea($translated_content) . '</textarea>';
        echo '</div>';

        echo '</td>';
        echo '</tr>';
    }

    echo '</table>';
    echo '<p class="description">' . pll___safe('Bu məzmunun digər dillərdə tərcümələrini daxil edin.') . '</p>';
}

// Save post translation fields
function alachiqgroup_pro_save_post_translation_fields($post_id) {
    // Check nonce
    if (!isset($_POST['alachiqgroup_pro_post_translations_nonce']) ||
        !wp_verify_nonce($_POST['alachiqgroup_pro_post_translations_nonce'], 'alachiqgroup_pro_post_translations')) {
        return;
    }

    // Check permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $languages = array('en', 'ru');
    $fields = array('title', 'content', 'excerpt');

    foreach ($languages as $lang_code) {
        foreach ($fields as $field) {
            $meta_key = "{$field}_{$lang_code}";
            $post_key = "post_{$field}_{$lang_code}";

            if (isset($_POST[$post_key])) {
                $value = $field === 'content' ? wp_kses_post($_POST[$post_key]) : sanitize_text_field($_POST[$post_key]);
                if (!empty($value)) {
                    update_post_meta($post_id, $meta_key, $value);
                } else {
                    delete_post_meta($post_id, $meta_key);
                }
            }
        }
    }
}
add_action('save_post', 'alachiqgroup_pro_save_post_translation_fields');



// Translation admin page
function alachiqgroup_pro_translation_admin_page() {
    ?>
    <div class="wrap">
        <h1><?php pll_e_safe('Dinamik Məzmun Tərcümələri'); ?></h1>
        <p><?php pll_e_safe('Bu səhifədə kateqoriyalar və məqalələrin tərcümələrini idarə edə bilərsiniz.'); ?></p>

        <!-- Auto Translator Access Button -->
        <div class="notice notice-success" style="border-left-color: #46b450; padding: 15px;">
            <h3 style="margin-top: 0;"><?php pll_e_safe('🤖 Avtomatik Tərcümə Sistemi'); ?></h3>
            <p><?php pll_e_safe('Bütün sayt mətnlərini avtomatik olaraq tərcümə etmək üçün AI tərcümə sistemindən istifadə edin.'); ?></p>
            <p>
                <a href="<?php echo admin_url('admin.php?page=alachiq-auto-translator'); ?>" class="button button-primary button-large">
                    <?php pll_e_safe('🚀 Avtomatik Tərcüməyə Keç'); ?>
                </a>
                <span style="margin-left: 15px; color: #666;">
                    <?php pll_e_safe('DeepL və Google Translate API dəstəyi ilə'); ?>
                </span>
            </p>
        </div>

        <div class="notice notice-info">
            <p><strong><?php pll_e_safe('Necə istifadə etmək olar:'); ?></strong></p>
            <ul>
                <li><?php pll_e_safe('• Kateqoriyaları tərcümə etmək üçün: Layihələr > Kateqoriyalar və ya Dekorasiyalar > Kateqoriyalar bölməsinə gedin'); ?></li>
                <li><?php pll_e_safe('• Məqalələri tərcümə etmək üçün: Layihə və ya Dekorasiya məqaləsini redaktə edin'); ?></li>
                <li><?php pll_e_safe('• Hər kateqoriya və məqalədə "Tərcümələr" bölməsi var'); ?></li>
                <li><?php pll_e_safe('• Avtomatik tərcümə üçün yuxarıdakı düyməni basın'); ?></li>
            </ul>
        </div>

        <h2><?php pll_e_safe('Kateqoriya Tərcümələri'); ?></h2>

        <h3><?php pll_e_safe('Layihə Kateqoriyaları'); ?></h3>
        <?php alachiqgroup_pro_display_category_translations('project_category'); ?>

        <h3><?php pll_e_safe('Dekorasiya Kateqoriyaları'); ?></h3>
        <?php alachiqgroup_pro_display_category_translations('product_category'); ?>

        <h2><?php pll_e_safe('Məqalə Tərcümələri'); ?></h2>
        <p><?php pll_e_safe('Məqalə tərcümələrini idarə etmək üçün hər məqaləni ayrıca redaktə edin.'); ?></p>
    </div>
    <?php
}

// Display category translations table
function alachiqgroup_pro_display_category_translations($taxonomy) {
    $categories = get_terms(array(
        'taxonomy' => $taxonomy,
        'hide_empty' => false,
    ));

    if (empty($categories) || is_wp_error($categories)) {
        echo '<p>' . pll___safe('Kateqoriya tapılmadı.') . '</p>';
        return;
    }

    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>' . pll___safe('Azərbaycan dili') . '</th>';
    echo '<th>' . pll___safe('English') . '</th>';
    echo '<th>' . pll___safe('Русский') . '</th>';
    echo '<th>' . pll___safe('Əməliyyatlar') . '</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($categories as $category) {
        $en_translation = get_term_meta($category->term_id, 'name_en', true);
        $ru_translation = get_term_meta($category->term_id, 'name_ru', true);

        echo '<tr>';
        echo '<td><strong>' . esc_html($category->name) . '</strong></td>';
        echo '<td>' . ($en_translation ? esc_html($en_translation) : '<em>' . pll___safe('Tərcümə yoxdur') . '</em>') . '</td>';
        echo '<td>' . ($ru_translation ? esc_html($ru_translation) : '<em>' . pll___safe('Tərcümə yoxdur') . '</em>') . '</td>';
        echo '<td><a href="' . admin_url("term.php?taxonomy={$taxonomy}&tag_ID={$category->term_id}") . '" class="button button-small">' . pll___safe('Redaktə et') . '</a></td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
}

// Create sample products (only if no products exist)
function alachiqgroup_pro_create_sample_products() {
    $existing_products = get_posts(array(
        'post_type' => 'decoration_products',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ));
    
    if (empty($existing_products)) {
        $sample_products = array(
            array(
                'title' => 'Modern İnteryer Dekorasiyası',
                'content' => 'Müasir üslubda interyer dekorasiyası xidməti. Minimalist dizayn, funksional həllər və keyfiyyətli materiallar.',
                'category' => 'interior-decoration',
                'price' => '2500',
                'duration' => '2-3 həftə',
                'features' => "3D Vizualizasiya\nRəng Sxemləri\nMebel Seçimi\nDekorativ Elementlər"
            ),
            array(
                'title' => 'LED İşıqlandırma Sistemi',
                'content' => 'Enerji səmərəli LED işıqlandırma sistemləri. Dekorativ işıq effektləri və smart idarəetmə.',
                'category' => 'lighting',
                'price' => '1200',
                'duration' => '1-2 həftə',
                'features' => "LED İşıqlandırma\nSmart İdarəetmə\nEnerji Səmərəliliyi\nUzun Ömürlülük"
            ),
            array(
                'title' => 'Dekorativ Divar Kağızları',
                'content' => 'Yüksək keyfiyyətli divar kağızları və tekstura örtüklər. Müxtəlif üslublarda geniş seçim.',
                'category' => 'wall-ceiling',
                'price' => '800',
                'duration' => '3-5 gün',
                'features' => "Yüksək Keyfiyyət\nGeniş Seçim\nTez Quraşdırma\nUzun Ömürlülük"
            ),
            array(
                'title' => 'Bağ və Həyət Dizaynı',
                'content' => 'Landşaft dizaynı və bağ dekorasiyası. Açıq məkanların gözəlləşdirilməsi.',
                'category' => 'garden-landscape',
                'price' => '3500',
                'duration' => '4-6 həftə',
                'features' => "Landşaft Dizaynı\nBitki Seçimi\nDekorativ Elementlər\nSuvarma Sistemi"
            ),
            array(
                'title' => 'Ofis Dekorasiyası',
                'content' => 'Korporativ məkanların professional dekorasiyası. İş mühitinin yaxşılaşdırılması.',
                'category' => 'office-commercial',
                'price' => '4000',
                'duration' => '3-4 həftə',
                'features' => "Korporativ Stil\nFunksional Dizayn\nKeyfiyyətli Materiallar\nTez Tamamlama"
            ),
            array(
                'title' => 'Toy Dekorasiyası',
                'content' => 'Xüsusi tədbirlər üçün dekorasiya xidmətləri. Toy, nişan və digər mərasimlər.',
                'category' => 'events-ceremonies',
                'price' => '2000',
                'duration' => '1 həftə',
                'features' => "Fərdi Yanaşma\nYaradıcı Həllər\nTam Xidmət\nXatirə Qalacaq"
            )
        );
        
        foreach ($sample_products as $product) {
            $post_id = wp_insert_post(array(
                'post_title' => $product['title'],
                'post_content' => $product['content'],
                'post_status' => 'publish',
                'post_type' => 'decoration_products'
            ));
            
            if ($post_id) {
                // Set category
                wp_set_object_terms($post_id, $product['category'], 'product_category');
                
                // Set meta data
                update_post_meta($post_id, '_product_price', $product['price']);
                update_post_meta($post_id, '_product_currency', 'AZN');
                update_post_meta($post_id, '_product_duration', $product['duration']);
                update_post_meta($post_id, '_product_features', $product['features']);
            }
        }
    }
}
add_action('after_switch_theme', 'alachiqgroup_pro_create_sample_products');

// Create sample services (only if no services exist)
function alachiqgroup_pro_create_sample_services() {
    $existing_services = get_posts(array(
        'post_type' => 'services',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ));

    if (!empty($existing_services)) {
        return; // Services already exist, don't create samples
    }

    $sample_services = array(
        array(
            'title' => 'Memarlıq və Dizayn',
            'content' => 'Professional memarlıq və dizayn xidmətləri ilə xəyallarınızı reallığa çeviririk. Müasir və funksional həllər təqdim edirik.',
            'category' => 'architecture-design',
            'price' => '2500',
            'duration' => '3-4 həftə',
            'icon' => 'fas fa-drafting-compass',
            'features' => array(
                'Professional memarlıq layihələndirmə',
                '3D vizualizasiya və render',
                'İnteryer və eksteryer dizayn',
                'Texniki sənədləşmə',
                'Müasir dizayn həlləri'
            )
        ),
        array(
            'title' => 'Tikinti Xidmətləri',
            'content' => 'Villa, kottec və kommersiya obyektlərinin tikintisi. Yüksək keyfiyyət və müasir texnologiyalar ilə işləyirik.',
            'category' => 'construction',
            'price' => '15000',
            'duration' => '3-6 ay',
            'icon' => 'fas fa-hard-hat',
            'features' => array(
                'Villa və kottec tikintisi',
                'Kommersiya obyektləri',
                'Yüksək keyfiyyətli materiallar',
                'Peşəkar komanda',
                'Vaxtında təhvil zəmanəti'
            )
        ),
        array(
            'title' => 'Təmir və Yenilənmə',
            'content' => 'Mövcud binaların təmiri və yenilənməsi. Köhnə strukturları müasir standartlara uyğun şəkildə yeniləyirik.',
            'category' => 'repair-renovation',
            'price' => '5000',
            'duration' => '2-4 həftə',
            'icon' => 'fas fa-tools',
            'features' => array(
                'Tam təmir xidmətləri',
                'Struktur gücləndirilməsi',
                'Müasir materiallarla yenilənmə',
                'Enerji səmərəliliyi',
                'Dizayn yenilənməsi'
            )
        ),
        array(
            'title' => 'Mebel İşləri',
            'content' => 'Xüsusi mebel dizaynı və istehsalı. Keyfiyyətli materiallardan hazırlanan unikal mebel həlləri.',
            'category' => 'furniture',
            'price' => '1500',
            'duration' => '2-3 həftə',
            'icon' => 'fas fa-couch',
            'features' => array(
                'Xüsusi mebel dizaynı',
                'Keyfiyyətli materiallar',
                'Handmade istehsal',
                'Unikal dizayn həlləri',
                'Uzunmüddətli zəmanət'
            )
        ),
        array(
            'title' => 'Hovuz Tikintisi',
            'content' => 'Hovuz tikintisi və landşaft dizaynı. Müasir texnologiyalar və professional yanaşma ilə.',
            'category' => 'pool-construction',
            'price' => '8000',
            'duration' => '4-6 həftə',
            'icon' => 'fas fa-swimming-pool',
            'features' => array(
                'Hovuz layihələndirmə',
                'Professional tikinti',
                'Su təmizləmə sistemləri',
                'Landşaft dizaynı',
                'Texniki xidmət'
            )
        ),
        array(
            'title' => 'Abadlaşdırma',
            'content' => 'Bağ və həyət abadlaşdırması, landşaft dizaynı. Təbii gözəlliyi və funksionallığı birləşdiririk.',
            'category' => 'landscaping',
            'price' => '3000',
            'duration' => '2-3 həftə',
            'icon' => 'fas fa-seedling',
            'features' => array(
                'Landşaft layihələndirmə',
                'Bağ və həyət dizaynı',
                'Bitki seçimi və əkimi',
                'Suvarma sistemləri',
                'Dekorativ elementlər'
            )
        )
    );

    foreach ($sample_services as $service) {
        $post_id = wp_insert_post(array(
            'post_title' => $service['title'],
            'post_content' => $service['content'],
            'post_status' => 'publish',
            'post_type' => 'services'
        ));

        if ($post_id) {
            // Set category
            wp_set_object_terms($post_id, $service['category'], 'service_category');

            // Set meta data
            update_post_meta($post_id, '_service_price', $service['price']);
            update_post_meta($post_id, '_service_currency', 'AZN');
            update_post_meta($post_id, '_service_duration', $service['duration']);
            update_post_meta($post_id, '_service_features', $service['features']);
            update_post_meta($post_id, '_service_icon', $service['icon']);
        }
    }
}
add_action('after_switch_theme', 'alachiqgroup_pro_create_sample_services');

// Force immediate flush of rewrite rules for services fix
function alachiqgroup_pro_immediate_flush_rewrites() {
    flush_rewrite_rules();
}
add_action('init', 'alachiqgroup_pro_immediate_flush_rewrites', 999);

// Force flush rewrite rules for decoration products and services (run once)
function alachiqgroup_pro_force_flush_rewrites() {
    // Only run this once by checking if we've already done it
    if (!get_option('alachiqgroup_pro_rewrites_flushed_v2')) {
        flush_rewrite_rules();
        update_option('alachiqgroup_pro_rewrites_flushed_v2', true);
    }
}
add_action('init', 'alachiqgroup_pro_force_flush_rewrites', 20);

// Add custom rewrite rules for decoration products and services
function alachiqgroup_pro_add_rewrite_rules() {
    add_rewrite_rule(
        '^decoration-products/([^/]+)/?$',
        'index.php?post_type=decoration_products&name=$matches[1]',
        'top'
    );

    add_rewrite_rule(
        '^service/([^/]+)/?$',
        'index.php?post_type=services&name=$matches[1]',
        'top'
    );
}
add_action('init', 'alachiqgroup_pro_add_rewrite_rules', 10);

// Ensure services page uses the correct template
function alachiqgroup_pro_services_page_template($template) {
    if (is_page('services')) {
        $services_template = locate_template('page-services.php');
        if ($services_template) {
            return $services_template;
        }
    }
    return $template;
}
add_filter('template_include', 'alachiqgroup_pro_services_page_template');

// AJAX save gallery function
function alachiqgroup_pro_save_gallery_ajax() {
    if (!isset($_POST['post_id']) || !isset($_POST['gallery_ids'])) {
        wp_die('Missing data');
    }

    $post_id = intval($_POST['post_id']);
    $gallery_ids = sanitize_text_field($_POST['gallery_ids']);

    if (!current_user_can('edit_post', $post_id)) {
        wp_die('Permission denied');
    }

    // Handle empty gallery (deletion of all images)
    if (empty($gallery_ids)) {
        delete_post_meta($post_id, '_project_gallery');
        delete_post_meta($post_id, 'project_gallery_backup');
        delete_post_meta($post_id, '_gallery_test');
        wp_die('success');
    }

    // Process gallery IDs
    $ids_array = array_filter(array_map('intval', explode(',', $gallery_ids)));

    if (empty($ids_array)) {
        wp_die('No valid image IDs');
    }

    // Save to multiple meta keys for redundancy
    $result1 = update_post_meta($post_id, '_project_gallery', $ids_array);
    $result2 = update_post_meta($post_id, 'project_gallery_backup', $ids_array);
    $result3 = update_post_meta($post_id, '_gallery_test', $ids_array);

    wp_die($result1 ? 'success' : 'failed');
}
add_action('wp_ajax_save_project_gallery', 'alachiqgroup_pro_save_gallery_ajax');

// AJAX function to get attachment URL
function alachiqgroup_pro_get_attachment_url() {
    if (!isset($_POST['attachment_id'])) {
        wp_die('0');
    }

    $attachment_id = intval($_POST['attachment_id']);
    $image_data = wp_get_attachment_image_src($attachment_id, 'thumbnail');

    if ($image_data) {
        wp_die($image_data[0]);
    } else {
        wp_die('0');
    }
}
add_action('wp_ajax_get_attachment_url', 'alachiqgroup_pro_get_attachment_url');



// Create default project categories
function alachiqgroup_pro_create_default_categories() {
    $categories = array(
        'interyer' => 'İnteryer',
        'lanshaft' => 'Landşaft',
        'qeyri-yasayis' => 'Qeyri-yaşayış',
        '1-mertebe' => '1 mərtəbə',
        '2-mertebe' => '2 mərtəbə',
        '3-mertebe' => '3 mərtəbə'
    );

    foreach ($categories as $slug => $name) {
        if (!term_exists($slug, 'project_category')) {
            wp_insert_term($name, 'project_category', array('slug' => $slug));
        }
    }
}
add_action('init', 'alachiqgroup_pro_create_default_categories');

// Helper function to get project categories (DRY principle)
function alachiqgroup_pro_get_project_categories($hide_empty = false) {
    return get_terms(array(
        'taxonomy' => 'project_category',
        'hide_empty' => $hide_empty,
        'orderby' => 'name',
        'order' => 'ASC'
    ));
}

// Helper function to get projects page URL with category parameter
function alachiqgroup_pro_get_projects_url($category_slug = '') {
    $projects_page = get_page_by_path('projects');
    $base_url = $projects_page ? get_permalink($projects_page) : home_url('/projects/');

    if (!empty($category_slug)) {
        return add_query_arg('category', $category_slug, $base_url);
    }

    return $base_url;
}

// Add custom columns to projects admin list
function alachiqgroup_pro_project_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['project_area'] = pll___safe('Sahə (m²)');
    $new_columns['project_floors'] = pll___safe('Mərtəbələr');
    $new_columns['project_type'] = pll___safe('Növ');
    $new_columns['taxonomy-project_category'] = pll___safe('Kateqoriya');
    $new_columns['date'] = $columns['date'];

    return $new_columns;
}
add_filter('manage_projects_posts_columns', 'alachiqgroup_pro_project_columns');

// Populate custom columns
function alachiqgroup_pro_project_column_content($column, $post_id) {
    switch ($column) {
        case 'project_area':
            $area = get_post_meta($post_id, '_project_area', true);
            echo $area ? esc_html($area) : '—';
            break;

        case 'project_floors':
            $floors = get_post_meta($post_id, '_project_floors', true);
            echo $floors ? esc_html($floors) : '—';
            break;

        case 'project_type':
            $type = get_post_meta($post_id, '_project_type', true);
            $types = array(
                'residential' => pll___safe('Yaşayış'),
                'commercial' => pll___safe('Ticarət'),
                'interior' => pll___safe('İnteryer Dizayn'),
                'landscape' => pll___safe('Landşaft')
            );
            echo isset($types[$type]) ? esc_html($types[$type]) : '—';
            break;
    }
}
add_action('manage_projects_posts_custom_column', 'alachiqgroup_pro_project_column_content', 10, 2);

// Add custom columns to carousel slides admin list
function alachiqgroup_pro_carousel_slide_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['slide_image'] = pll___safe('Şəkil');
    $new_columns['slide_subtitle'] = pll___safe('Təsvir');
    $new_columns['slide_buttons'] = pll___safe('Düymələr');
    $new_columns['menu_order'] = pll___safe('Sıra');
    $new_columns['date'] = $columns['date'];

    return $new_columns;
}
add_filter('manage_carousel_slides_posts_columns', 'alachiqgroup_pro_carousel_slide_columns');

// Populate custom columns for carousel slides
function alachiqgroup_pro_carousel_slide_column_content($column, $post_id) {
    switch ($column) {
        case 'slide_image':
            if (has_post_thumbnail($post_id)) {
                echo get_the_post_thumbnail($post_id, array(60, 40));
            } else {
                echo '—';
            }
            break;

        case 'slide_subtitle':
            $subtitle = get_post_meta($post_id, '_slide_subtitle', true);
            echo $subtitle ? wp_trim_words(esc_html($subtitle), 10) : '—';
            break;

        case 'slide_buttons':
            $button1_text = get_post_meta($post_id, '_slide_button1_text', true);
            $button2_text = get_post_meta($post_id, '_slide_button2_text', true);
            $buttons = array();
            if ($button1_text) $buttons[] = esc_html($button1_text);
            if ($button2_text) $buttons[] = esc_html($button2_text);
            echo $buttons ? implode(', ', $buttons) : '—';
            break;

        case 'menu_order':
            $order = get_post_field('menu_order', $post_id);
            echo $order ? esc_html($order) : '0';
            break;
    }
}
add_action('manage_carousel_slides_posts_custom_column', 'alachiqgroup_pro_carousel_slide_column_content', 10, 2);

// Enqueue media uploader for project, carousel slides, and decoration products admin
function alachiqgroup_pro_admin_scripts($hook) {
    global $post_type;

    if ($hook == 'post-new.php' || $hook == 'post.php') {
        if ('projects' === $post_type || 'carousel_slides' === $post_type || 'decoration_products' === $post_type || 'services' === $post_type) {
            wp_enqueue_media();
        }
    }
}
add_action('admin_enqueue_scripts', 'alachiqgroup_pro_admin_scripts');

// Security enhancements
function alachiqgroup_pro_security() {
    // Remove WordPress version from head
    remove_action('wp_head', 'wp_generator');
    
    // Remove RSD link
    remove_action('wp_head', 'rsd_link');
    
    // Remove wlwmanifest link
    remove_action('wp_head', 'wlwmanifest_link');
}
add_action('init', 'alachiqgroup_pro_security');





<?php
if (!defined('ABSPATH')) {
    exit;
}

use EPIC_WP\Polylang_Automatic_AI_Translation\Models\Translation_Models;
?>

<div id='pllat_openai_api_key_wrapper' class='pllat-api-field-wrapper' style='display: none;'>
    <div>
        <h4><?php pll_e('OpenAI API Key'); ?></h4>
        <input type='password' id='pllat_openai_api_key' name='pllat_openai_api_key' value='<?php echo esc_attr($openai_api_key); ?>' class='regular-text' placeholder='<?php pll_e('API key'); ?>' />

        <?php if (!empty($openai_api_key)) : ?>
            <p class="description"><u style="color: red;"><?php pll_e('Important!'); ?></u> <?php pll_e('Make sure you have credit in your OpenAI account, otherwise the plugin won\'t translate your content.'); ?> <a href="https://platform.openai.com/settings/organization/billing/overview" target="_blank"><?php pll_e('Check your balance here'); ?> &raquo;</a></p>
        <?php else : ?>
            <p class="description"><a href="https://platform.openai.com/account/api-keys" target="_blank"><?php pll_e('Get your OpenAI API keys here.'); ?></a></p>
        <?php endif; ?>
    </div>

    <div class="pllat-model-field-container">
        <h4>
            <?php pll_e('OpenAI Model'); ?>
            <span class="dashicons dashicons-editor-help js-help-tip" 
                  data-tooltip="<?php echo esc_attr(Translation_Models::get_tooltip('openai')); ?>">
            </span>
        </h4>
        <select name='pllat_openai_translation_model' id='pllat_openai_translation_model' class='regular-text'>
            <?php 
            if (empty($openai_models)) {
                echo '<option value="">' . pll__('No models available') . '</option>';
            } else {
                foreach ($openai_models as $model => $config) : ?>
                    <option value='<?php echo esc_attr($model); ?>' <?php selected($selected_model, $model); ?>><?php echo esc_html($config['label']); ?></option>
                <?php endforeach;
            } ?>
        </select>
        <p class="description"><?php echo Translation_Models::get_model_description('openai'); ?></p>
    </div>
</div>

<div id='pllat_openrouter_api_key_wrapper' class='pllat-api-field-wrapper' style='display: none;'>
    <div>
        <h4><?php pll_e('OpenRouter API Key'); ?></h4>
        <input type='password' id='pllat_openrouter_api_key' name='pllat_openrouter_api_key' value='<?php echo esc_attr($openrouter_api_key); ?>' class='regular-text' placeholder='<?php pll_e('API key'); ?>' />

        <?php if (!empty($openrouter_api_key)) : ?>
            <p class="description"><u style="color: red;"><?php pll_e('Important!'); ?></u> <?php pll_e('Make sure you have credit in your OpenRouter account, otherwise the plugin won\'t translate your content.'); ?> <a href="https://openrouter.ai/account" target="_blank"><?php pll_e('Check your balance here'); ?> &raquo;</a></p>
        <?php else : ?>
            <p class="description"><a href="https://openrouter.ai/keys" target="_blank"><?php pll_e('Get your OpenRouter API keys here.'); ?></a></p>
        <?php endif; ?>
    </div>

    <div class="pllat-model-field-container">
        <h4>
            <?php pll_e('OpenRouter Model'); ?>
            <span class="dashicons dashicons-editor-help js-help-tip" 
                  data-tooltip="<?php echo esc_attr(Translation_Models::get_tooltip('openrouter')); ?>">
            </span>
        </h4>
        <select name='pllat_openrouter_translation_model' id='pllat_openrouter_translation_model' class='regular-text'>
            <?php 
            if (empty($openrouter_models)) {
                echo '<option value="">' . pll__('No models available') . '</option>';
            } else {
        
                
                // Paid Models
                if (!empty($openrouter_models['paid'])) {
                    echo '<optgroup label="' . pll__('Paid Models') . '">';
                    foreach ($openrouter_models['paid'] as $model => $config) {
                        echo '<option value="' . esc_attr($model) . '" ' . selected($selected_model, $model, false) . '>' . esc_html($config['label']); ?></option>
                    <?php }
                    echo '</optgroup>';
                }

                // Free Models
                if (!empty($openrouter_models['free'])) {
                    echo '<optgroup label="' . pll__('Free Models') . '">';
                    foreach ($openrouter_models['free'] as $model => $config) {
                        echo '<option value="' . esc_attr($model) . '" ' . selected($selected_model, $model, false) . '>' . esc_html($config['label']); ?></option>
                    <?php }
                    echo '</optgroup>';
                }
            } ?>
        </select>
        <div id="pllat-free-model-notice" class="hidden my-3 border-solid border border-l-4 border-yellow-500 bg-yellow-50 px-3 py-2.5 rounded-sm">
            <span class="m-0"><span class="text-yellow-600"><strong><?php pll_e('Free model notice:'); ?></strong></span> <?php pll_e('You are using a free model which has limited free usage and rate limits. For production use, we recommend using a paid model to ensure reliable translations.'); ?></span>
        </div>
        <p class="description"><?php echo Translation_Models::get_model_description('openrouter'); ?></p>
    </div>
</div> 
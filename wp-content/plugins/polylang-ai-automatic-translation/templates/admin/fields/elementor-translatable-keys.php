<?php
if (!defined('ABSPATH')) {
    exit;
}

$description = pll__('Specify which Elementor widget fields should be translated. Enter the field names separated by commas (e.g., "title, text, button_text"). These are the internal field names used by Elementor widgets. Common examples: "title" for headings, "editor" for text editor, "button_text" for buttons. To include nested fields, use dot notation (e.g. "icon_list.text").');
?>
<textarea name='pllat_elementor_translatable_keys' class='regular-text' rows='3' placeholder='title, text, button_text'><?php echo esc_textarea($value); ?></textarea>
<p class="description"><?php echo $description; ?></p> 
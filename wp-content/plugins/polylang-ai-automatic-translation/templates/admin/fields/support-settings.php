<?php //phpcs:disable SlevomatCodingStandard.Functions.RequireMultiLineCall.RequiredMultiLineCall ⁠
/**
 * Support Settings Section Template
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Templates\Admin\Fields
 *
 * @uses $args // Sent from render_support_settings_section
 * @var bool   $log_exists  Whether the debug log file exists.
 * @var string $log_content The content of the debug log file (or info text).
 * @var ?int   $log_size    The size of the log file in bytes, or null.
 */

\defined( 'ABSPATH' ) || exit;
?>

<p><?php \_e( 'Need help? Check the documentation or contact support below.' ); ?></p>
<p>
    <a href="https://support.epicwpsolutions.com/en/knowledgebase" target="_blank" class="button button-secondary">
    <?php \_e( 'Documentation' ); ?>
    </a>
    <a href="https://support.epicwpsolutions.com/en" target="_blank" class="button button-secondary">
    <?php \_e( 'Create support ticket' ); ?>
    </a>
    <a href="<?php echo \admin_url( 'site-health.php' ); ?>" target="_blank" class="button button-secondary">
        <?php \_e( 'Health Check' ); ?>
    </a>
</p>

<hr class="my-4" />

<h3><?php \_e( 'Debug log preview' ); ?></h3>

<?php if ( $log_exists ?? false ) : ?>
    <?php $download_url = \admin_url( 'admin.php?action=pllat_download_logs' ); // Define download URL here ?>
    <p class="mb-6">
    <?php
    printf(
        \__(
            'Below you will find the last 200 lines of the plugin debug log. To inspect the full log, <a href="%s">download the file</a>.',
        ),
        \esc_url( $download_url ),
    );
    ?>
    </p>
    <textarea 
        id="pllat-debug-log-textarea" 
        readonly="readonly" 
        class="w-full h-80 font-mono whitespace-pre overflow-auto !bg-[#1D2327] text-gray-100 border border-gray-600 p-5 box-border rounded-md text-[12px] antialiased"
    ><?php echo \esc_textarea( $log_content ?? '' ); ?></textarea>

    <div class="mt-2 flex gap-2 items-center justify-end"> 
        <a href="<?php echo \esc_url( $download_url ); ?>" class="button button-primary"> 
            <?php \_e( 'Download Log File' ); ?>
        </a>

        <button type="submit" id="pllat_clear_logs_button" name="pllat_clear_logs" class="button delete">
            <?php
            $button_text = \__( 'Clear Debug Log' );
            if ( isset( $log_size ) && null !== $log_size ) {
                $formatted_size = \size_format( $log_size, 2 );
                $button_text   .= sprintf( ' (%s)', $formatted_size );
            }
            echo \esc_html( $button_text );
            ?>
        </button>
        <span class="spinner" style="float: none; margin-top: 0; display: none;"></span>

    </div>

<?php else : ?>
    <p><?php \_e( 'The debug log file does not exist or is empty.' ); ?></p>
    <?php
endif;

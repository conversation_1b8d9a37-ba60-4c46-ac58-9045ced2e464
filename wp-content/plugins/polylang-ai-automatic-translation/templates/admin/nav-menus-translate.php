<?php 
if (!defined('ABSPATH')) {
    exit;
}
?>

<div>
    <h3><?php _e('Menu Translation', 'polylang-ai-autotranslate'); ?></h3>
    <span class="block mb-3"><?php _e('Copy an existing menu with all the translated versions of the menu items.', 'polylang-ai-autotranslate'); ?></span>

    <div class="flex gap-2">
        <div>
            <label class="block mb-2 font-semibold"><?php _e('Copy menu:', 'polylang-ai-autotranslate'); ?></label>
            <select name="menu_copy_source">
                <option>-- <?php _e('Select menu', 'polylang-ai-autotranslate'); ?> --</option>
                <?php foreach ($menus as $menu) { ?>
                    <option value="<?php echo $menu->term_id; ?>"><?php echo $menu->name; ?></option>
                <?php } ?>
            </select>
        </div>  
        <div>  
            <label class="block mb-2 font-semibold"><?php _e('In language:', 'polylang-ai-autotranslate'); ?></label>
            <select name="menu_copy_language">
                <option>-- <?php _e('Select language', 'polylang-ai-autotranslate'); ?> --</option>
                <?php foreach ($available_languages as $language) { ?>
                    <option value="<?php echo $language['slug']; ?>"><?php echo $language['name'] . ' (' . $language['slug'] . ')'; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>    
</div>
<?php
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use EPIC_WP\Polylang_Automatic_AI_Translation\Models\Translation_Models;

$model = Settings::get_translation_model($active_translation_api);
$models = $active_translation_api === 'openai' ? 
    Translation_Models::get_openai_models() : 
    array_merge(
        Translation_Models::get_openrouter_models()['free'],
        Translation_Models::get_openrouter_models()['paid']
    );
$model_label = isset($models[$model]) ? $models[$model]['label'] : $model;

?>

<div class="py-5 px-3">

    <?php if ($active_translation_api_key) { ?>

        <!-- Exclude button in top right corner -->
        <div class="mb-4 flex justify-end">
            <label class="control-button">
                <input type="checkbox" name="pllat_exclude_from_translation" <?php checked($exclude_from_translation, 1); ?> />
                <?php pll_e('Exclude from translation'); ?>
                <span class="dashicons dashicons-editor-help js-help-tip" 
                      data-tooltip="<?php esc_attr_e('When checked, this content will be excluded from automatic translations. You need to save the content first.', 'polylang-automatic-translation'); ?>">
                </span>
            </label>
            <div class="control-button flex items-center gap-2">
                <span class="dashicons dashicons-admin-settings"></span>
                <a href="<?php echo PLATT_PLUGIN_SETTINGS_PAGE; ?>">
                    <div class="flex items-center gap-2">
                        <?php echo ucfirst($active_translation_api) . ' - ' . $model_label; ?>
                    </div>
                </a>
            </div>
        </div>

        <div class="mb-3">
            <label class="text-md font-semibold">
                <?php pll_e('Select languages'); ?>:
                <span class="dashicons dashicons-editor-help js-help-tip" 
                      data-tooltip="<?php esc_attr_e('Select one or multiple languages you would like this content automatically translated to.', 'polylang-automatic-translation'); ?>">
                </span>
            </label>
        </div>

        <div class="mb-8">
            <?php 
            if (!empty($available_languages)) { 
                include PLLAT_PLUGIN_DIR . 'templates/admin/language-selection.php';
            } 
            ?>
        </div>

        <div class="mb-6">
            <label for="pllat_additional_instructions" class="text-md font-semibold mb-2 block">
                <?php pll_e('Any additional instructions for the AI translator (optional)'); ?>:
                <span class="dashicons dashicons-editor-help js-help-tip" 
                      data-tooltip="<?php esc_attr_e('Add specific instructions for the AI translator (up to 1000 characters), such as brand names that should not be translated or for example whether to write formal or informal.', 'polylang-automatic-translation'); ?>">
                </span>
            </label>
            <textarea id="pllat_additional_instructions_field" name="pllat_additional_instructions" class="regular-text w-full p-4" rows="5" maxlength="1000" placeholder="<?php pll_e('E.g. The name of our brand is Green Fields. Never translate this name.'); ?>"></textarea>
        </div>

        <!-- Action buttons with Force mode on the right -->
        <div class="mb-5 flex items-center justify-between w-full">
            <div class="flex items-center gap-2">
                <button class="px-5 py-4 text-md bg-[#2271b1] font-semibold text-white shadow-md rounded-lg border-0 cursor-pointer flex items-center" id="pllat_update_translations_button" data-type="<?php echo $type; ?>" data-id="<?php echo $id; ?>" data-entity="<?php echo $entity; ?>" <?php echo ($exclude_from_translation) ? 'disabled' : ''; ?>>
                    <span class="dashicons dashicons-translation mr-2"></span>
                    <?php pll_e('Generate Translations with AI'); ?>
                </button>
                
                <!-- Cancel button - initially hidden -->
                <button class="px-5 py-4 text-md font-semibold shadow-md rounded-lg border-0 cursor-pointer flex items-center !bg-red-500 !border-red-500 !text-white" id="pllat_cancel_translations_button" data-type="<?php echo $type; ?>" data-id="<?php echo $id; ?>" style="display: none;">
                    <span class="dashicons dashicons-no-alt mr-2"></span>
                    <?php pll_e('Stop translation'); ?>
                </button>
            </div>
            
            <!-- Force mode button on the right -->
            <label for="pllat_force_translation" class="control-button mb-0">
                <input type="checkbox" id="pllat_force_translation" name="pllat_force_translation">
                <?php pll_e('Enable Force mode'); ?>
                <span class="dashicons dashicons-editor-help js-help-tip" 
                    data-tooltip="<?php esc_attr_e('Force mode will generate translations again, regardless of already existing translations for the selected languages.', 'polylang-automatic-translation'); ?>">
                </span>
            </label>
        </div>

        <div class="mb-8">
            <div id="pllat_translation_status_container" class="mt-4">
                <div class="flex items-center gap-2">
                    <div id="pllat_update_inform_message_loader" class="mb-4" style="display: none;">
                        <!-- Loader will be inserted here by JavaScript -->
                    </div>
                    <div id="pllat_update_inform_message" style="display: none;"></div>
                </div>
            </div>
        </div>

        <?php if ($is_debug_mode) { ?>
            <?php include PLLAT_PLUGIN_DIR . 'templates/admin/single-translation-debug.php'; ?>
        <?php } ?>    

    <?php } else { ?>

        <div>
            <p><?php echo pll__('Please connect a translation API key first') . ' ' . pll__('at the') . ' <a href="' . PLATT_PLUGIN_SETTINGS_PAGE . '" target="_blank">' . pll__('plugin settings page') . '</a>.'; ?></p>
        </div>
        
    <?php } ?>

</div>
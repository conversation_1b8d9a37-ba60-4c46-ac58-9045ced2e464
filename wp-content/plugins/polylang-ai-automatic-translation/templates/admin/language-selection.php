<?php 
if (!defined('ABSPATH')) {
    exit;
}
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
?>

<?php if (!empty($available_languages)) { ?>
    <div class="mb-5">
        <div id="pllat_included_languages" class="gap-1 flex flex-wrap">
            <label for="pllat_language_select_all" class="js-language-select-all-container p-3 flex gap-1 items-center rounded-lg bg-gray-200">
                <input type="checkbox" id="pllat_language_select_all" class="js-language-select-all !m-0 !mr-1" value="all" <?php echo $all_checked; ?> />
                <span class="js-text"><?php echo $all_checked ? 'Deselect all' : 'Select all'; ?></span>
            </label>
            <?php foreach ($available_languages as $available_language) {
                $name = 'pllat_include_language_checkbox_' . $available_language;
                $language_data = $this->language_manager->get_language_data($available_language);
                $checked = in_array($available_language, $included_languages) ? 'checked' : '';
                ?>
                <label for="<?php echo $name; ?>" class="p-3 flex gap-1 items-center rounded-lg bg-gray-200">
                    <input type="checkbox" id="<?php echo $name; ?>" name="languages[]" class="js-included-language-checkbox !m-0 !mr-1" value="<?php echo $available_language; ?>" <?php echo $checked; ?> />
                    <div class="flex items-center">
                        <img src="<?php echo $language_data['flag']; ?>" />
                    </div>
                    <span><?php echo $language_data['name']; ?> (<?php echo strtoupper($language_data['slug']); ?>)</span>
                </label>
            <?php } ?>
        </div>
    </div>
<?php } ?>
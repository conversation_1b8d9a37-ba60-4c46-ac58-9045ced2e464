<?php
/**
 * Settings page admin template.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Templates
 *
 * @var array<string,string> $tabs    Settings tabs.
 * @var string               $current Current tab.
 * @var string               $section Current section.
 */

defined( 'ABSPATH' ) || exit;

$cls = static fn( $t ) => 'nav-tab nav-tab-' . $t . ' ' . ( $current === $t ? 'nav-tab-active' : '' );
$url = static fn( $t ) => add_query_arg(
    array(
        'page' => 'polylang-ai-automatic-translation',
        'tab'  => $t,
    ),
    admin_url( 'admin.php' ),
);

?>
<div class="wrap">
    <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>

    <h2 class="nav-tab-wrapper">
    <?php foreach ( $tabs as $key => $label ) : ?>
        <a href="<?php echo esc_url( $url( $key ) ); ?>" class="<?php echo esc_attr( $cls( $key ) ); ?>">
            <?php echo esc_html( $label ); ?>
        </a>
    <?php endforeach; ?>
    </h2>

    <?php if ( $section && has_action( "pllat_settings_page_section_{$section}" ) ) : ?>
        <?php do_action( "pllat_settings_page_section_{$section}" ); ?>
    <?php else : ?>
        <form method="post" action="options.php">
            <?php
            $hook = rtrim( "polylang-ai-automatic-translation-{$section}", '-' );
            \settings_fields( $hook );
            \do_settings_sections( $hook );
            \submit_button();
            ?>
        </form>
    <?php endif; ?>
</div>

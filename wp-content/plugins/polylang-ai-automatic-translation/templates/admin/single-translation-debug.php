<?php
if (!defined('ABSPATH')) {
    exit;
}
?>

<?php if (!empty($estimation)) { ?>
    <div class="mb-6">
        <table class="wp-list-table widefat striped">
            <tr>
                <th class="!font-semibold"><?php pll_e('Field'); ?></th>
                <th class="!font-semibold"><?php pll_e('Words'); ?></th>
                <th class="!font-semibold"><?php pll_e('Tokens'); ?></th>
                <th class="!font-semibold"><?php pll_e('Tokens incl. prompt'); ?></th>
            </tr>
            <?php foreach ($estimation as $field => $data) { ?>
                <tr>
                    <td><?php echo $data['label']; ?></td>
                    <td><?php echo $data['words']; ?></td>
                    <td><?php echo $data['tokens']; ?></td>
                    <td><?php echo $data['tokens_including_prompt']; ?></td>
                </tr>
            <?php } ?>
        </table>
    </div>
<?php } ?>

<div class="mb-6">
    <strong><?php pll_e('Translation queue'); ?>:</strong>
</div>

<div class="flex gap-5 justify-between">

    <div class="w-full p-7 bg-gray-100 rounded-lg border-solid border-gray-200">
        <strong><?php pll_e('Available fields'); ?>:</strong>
        <?php if (!empty($available_fields)) { ?>
            <ul>
                <?php foreach ($available_fields as $field) { ?>
                    <li class="italic"><?php echo $field; ?></li>
                <?php } ?>
            </ul>
        <?php } ?>    
    </div>

    <div class="w-full p-7 bg-gray-100 rounded-lg border-solid border-gray-200">
        <strong><?php pll_e('Available meta fields'); ?>:</strong>
        <?php if (!empty($available_meta_fields)) { ?>
            <ul>
                <?php foreach ($available_meta_fields as $field) { ?>
                    <li class="italic"><?php echo $field; ?></li>
                <?php } ?>
            </ul>
        <?php } ?>    
    </div>
</div>

<pre>
    <?php print_r($queue); ?>
</pre>

<?php //phpcs:disable

namespace PLLAT\Translator\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Strings_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;

class Bulk_String_Manager {
    /**
     * Strings manager instance.
     *
     * @var Strings_Manager
     */
    private readonly Strings_Manager $strings_manager;

    /**
     * Constructor.
     *
     * @param Translator       $translator       Translator instance.
     * @param Language_Manager $language_manager Language manager instance.
     */
    public function __construct(
        private readonly Translator $translator,
        private readonly Language_Manager $language_manager
    ) {
        $this->strings_manager = new Strings_Manager($language_manager);
    }

    /**
     * Gets all available strings that are not translated in the given language
     *
     * @param  string $string_group String group to filter by.
     * @param  string $language     Language code (e.g., 'en', 'fr').
     * @return array<string>
     */
    public function get_untranslated_strings(string $string_group, string $language): array {
        return $this->strings_manager->get_untranslated_strings($string_group, $language);
    }

    /**
     * Processes the given strings for translation.
     *
     * @param array<mixed>        $strings      Array of strings to process.
     * @param string              $string_group String group to filter by.
     * @param string              $language     Language code (e.g., 'en', 'fr').
     * @param array<string,mixed> $args         Additional arguments.
     */
    public function process_strings(array $strings, string $string_group, string $language, array $args = []): void {
        $results = $this->strings_manager->translate_strings($this->translator, $strings, $string_group, $language, $args);
        $pll_language = $this->language_manager->get_language($language);

        foreach ($results as $hash => $result) {
            $this->strings_manager->update_string($result['string_key'], $pll_language, $result['translation']);
            $this->strings_manager->mark_as_processed($result['string_key'], $string_group, $language);
        }
    }

    /**
     * Returns all available strings from a copy of PLL_Admin_Strings::get_strings
     *
     * @param  bool   $string_only Whether to return only the strings.
     * @param  string $group       String group to filter by.
     * @return array<mixed>
     */
    public function get_strings(bool $string_only = false, string $group = ''): array {
        return $this->strings_manager->get_strings($string_only, $group);
    }

    /**
     * Returns all available strings from a copy of PLL_Admin_Strings::get_strings
     *
     * @param  string $group String group to filter by.
     * @return array<mixed>
     */
    public function get_all_strings(string $group): array {
        return $this->strings_manager->get_strings(true, $group);
    }
}

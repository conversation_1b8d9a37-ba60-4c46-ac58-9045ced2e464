<?php //phpcs:disable Universal.Operators.DisallowShortTernary.Found, WordPress.PHP.DevelopmentFunctions.error_log_error_log

namespace PLLAT\Translator\Services;

/**
 * Bulk translation configuration class.
 *
 * @mixin Bulk_Action_Scheduler_Config
 */
class Bulk_Config {
    /**
     * Prefix for options.
     *
     * @var string
     */
    private const OPT_PREFIX = 'pllat_bulk_';

    /**
     * Option key for bulk translation activation status.
     *
     * @var string
     */
    private const OPT_ACTIVATED = 'translation_activated';

    /**
     * Option key for bulk translation running status.
     *
     * @var string
     */
    private const OPT_RUNNING = 'translation_running';

    /**
     * Option key for bulk translation post types.
     *
     * @var string
     */
    private const OPT_POST_TYPES = 'translation_post_types';

    /**
     * Option key for bulk translation taxonomies.
     *
     * @var string
     */
    private const OPT_TAXONOMIES = 'translation_taxonomies';

    /**
     * Option key for bulk translation languages.
     *
     * @var string
     */
    private const OPT_LANGUAGES = 'translation_languages';

    /**
     * Option key for bulk translation size.
     *
     * @var string
     */
    private const OPT_BULK_SIZE = 'translation_size';

    /**
     * Option key for additional instructions for bulk translation.
     *
     * @var string
     */
    private const OPT_ADDED_INSTR = 'translation_additional_instructions';

    /**
     * Option key for bulk translation string groups.
     *
     * @var string
     */
    private const OPT_STRING_GROUPS = 'translation_string_groups';

    /**
     * Option key for bulk translation statistics.
     *
     * @var string
     */
    private const OPT_STATS = 'translation_stats';

    /**
     * Option key for the current logs timestamp.
     *
     * @var string
     */
    private const OPT_CURR_LOG_TS = 'translation_current_logs_timestamp';

    /**
     * Option key for force mode status.
     *
     * @var string
     */
    private const OPT_FORCE_MODE = 'force_mode';

    /**
     * Option key for term filters.
     *
     * @var string
     */
    private const OPT_TERM_FILTERS = 'term_filters';

    /**
     * WPDB instance.
     *
     * @var \wpdb
     */
    private readonly \wpdb $db;

    /**
     * Constructor
     *
     * @param  Bulk_Action_Scheduler_Config $basc Bulk action scheduler config.
     */
    public function __construct( private readonly Bulk_Action_Scheduler_Config $basc ) {
        $this->db = $GLOBALS['wpdb'];
    }

    /**
     * Magic method to proxy calls to BASC
     *
     * @param  string       $name Method name.
     * @param  array<mixed> $args Arguments.
     * @return mixed
     */
    public function __call( string $name, array $args ): mixed {
        if ( \method_exists( $this->basc, $name ) ) {
            return $this->basc->{$name}( ...$args );
        }

        return null;
    }

    /**
     * Check if the bulk translation feature is activated.
     *
     * @return bool True if activated, false otherwise.
     */
    public function is_activated(): bool {
        return (bool) $this->get_uncached_option( self::OPT_ACTIVATED );
    }

    /**
     * Activate the bulk translation feature.
     *
     * @return void
     */
    public function activate(): void {
        $this->update_uncached_option( self::OPT_ACTIVATED, '1' );
    }

    /**
     * Deactivate the bulk translation feature.
     *
     * @return void
     */
    public function deactivate(): void {
        $this->delete_uncached_option( self::OPT_ACTIVATED );
    }

    /**
     * Check if the bulk translation is running.
     *
     * @return bool True if running, false otherwise.
     */
    public function is_running(): bool {
        return (bool) $this->get_uncached_option( self::OPT_RUNNING );
    }

    /**
     * Set the running state of the bulk translation.
     *
     * @param bool $running True to set as running, false otherwise.
     * @return void
     */
    public function set_running( bool $running ): void {
        if ( $running ) {
            $this->update_uncached_option( self::OPT_RUNNING, '1' );
        } else {
            $this->delete_uncached_option( self::OPT_RUNNING );
        }
    }

    /**
     * Check if the bulk translation is stopped.
     *
     * @return bool True if stopped, false otherwise.
     */
    public function is_stopped(): bool {
        return ! $this->is_running() && ! $this->is_activated();
    }

    /**
     * Get the active post types for bulk translation.
     *
     * @return array<string>
     */
    public function get_active_post_types(): array {
        $post_types = $this->get_uncached_option( self::OPT_POST_TYPES ) ?: array();
        return \apply_filters( 'pllat_get_active_bulk_translation_post_types', $post_types );
    }

    /**
     * Set the active post types for bulk translation.
     *
     * @param array<string> $post_types List of post types.
     */
    public function set_active_post_types( array $post_types ): void {
        $this->update_uncached_option( self::OPT_POST_TYPES, $post_types );
    }

    /**
     * Get the active taxonomies for bulk translation.
     *
     * @return array<string>
     */
    public function get_active_taxonomies(): array {
        $taxonomies = $this->get_uncached_option( self::OPT_TAXONOMIES ) ?: array();
        return \apply_filters( 'pllat_get_active_bulk_translation_taxonomies', $taxonomies );
    }

    /**
     * Set the active taxonomies for bulk translation.
     *
     * @param array<string> $taxonomies List of taxonomies.
     */
    public function set_active_taxonomies( array $taxonomies ): void {
        $this->update_uncached_option( self::OPT_TAXONOMIES, $taxonomies );
    }

    /**
     * Get the active languages for bulk translation.
     *
     * @return array<string>
     */
    public function get_active_languages(): array {
        $languages = $this->get_uncached_option( self::OPT_LANGUAGES ) ?: array();
        return $languages;
    }

    /**
     * Set the active languages for bulk translation.
     *
     * @param array<string> $languages List of languages.
     */
    public function set_active_languages( array $languages ): void {
        $this->update_uncached_option( self::OPT_LANGUAGES, $languages );
    }

    /**
     * Get the active string groups for bulk translation.
     *
     * @return array<string> List of active string groups.
     */
    public function get_active_string_groups(): array {
        $string_groups = $this->get_uncached_option( self::OPT_STRING_GROUPS ) ?: array();
        return \apply_filters( 'pllat_get_active_bulk_translation_string_groups', $string_groups );
    }

    /**
     * Set the active string groups for bulk translation.
     *
     * @param array<string> $string_groups List of string groups.
     */
    public function set_active_string_groups( array $string_groups ): void {
        $this->update_uncached_option( self::OPT_STRING_GROUPS, $string_groups );
    }

    /**
     * Get the bulk size for translation.
     *
     * @return int Bulk size.
     */
    public function get_bulk_size(): int {
        return (int) $this->get_uncached_option( self::OPT_BULK_SIZE ) ?: 3000;
    }

    /**
     * Set the bulk size for translation.
     *
     * @param int $size Bulk size.
     * @return void
     */
    public function set_bulk_size( int $size ): void {
        $this->update_uncached_option( self::OPT_BULK_SIZE, $size );
    }

    /**
     * Get the default language.
     *
     * @return string Default language.
     */
    public function get_default_language(): string {
        return \pll_default_language();
    }

    /**
     * Get additional instructions for bulk translation.
     *
     * @return string Additional instructions.
     */
    public function get_additional_instructions(): string {
        return $this->get_uncached_option( self::OPT_ADDED_INSTR ) ?: '';
    }

    /**
     * Set additional instructions for bulk translation.
     *
     * @param string $instructions Additional instructions.
     */
    public function set_additional_instructions( string $instructions ): void {
        '' !== $instructions
            ? $this->update_uncached_option( self::OPT_ADDED_INSTR, $instructions )
            : $this->delete_uncached_option( self::OPT_ADDED_INSTR );
    }

    /**
     * Get the statistics for bulk translation.
     *
     * @return array<string,mixed> Statistics data.
     */
    public function get_stats(): array {
        return $this->get_uncached_option( self::OPT_STATS ) ?: array();
    }

    /**
     * Set the statistics for bulk translation.
     *
     * @param array<string,mixed> $stats Statistics data.
     */
    public function set_stats( array $stats ): void {
        $this->update_uncached_option( self::OPT_STATS, $stats );
    }

    /**
     * Delete the statistics for bulk translation.
     *
     * @return void
     */
    public function delete_stats(): void {
        $this->delete_uncached_option( self::OPT_STATS );
    }

    /**
     * Set the current logs timestamp.
     *
     * @param string $timestamp Timestamp value.
     * @return void
     */
    public function set_current_logs_timestamp( string $timestamp ): void {
        $this->update_uncached_option( self::OPT_CURR_LOG_TS, $timestamp );
    }

    /**
     * Get the current logs timestamp.
     *
     * @return string Current logs timestamp.
     */
    public function get_current_logs_timestamp(): string {
        return $this->get_uncached_option( self::OPT_CURR_LOG_TS ) ?: '';
    }

    /**
     * Delete the current logs timestamp.
     *
     * @return void
     */
    public function delete_current_logs_timestamp(): void {
        $this->delete_uncached_option( self::OPT_CURR_LOG_TS );
    }

    /**
     * Get the force mode status.
     *
     * @return bool True if force mode is enabled, false otherwise.
     */
    public function get_force_mode(): bool {
        return (bool) $this->get_uncached_option( self::OPT_FORCE_MODE );
    }

    /**
     * Set the force mode status.
     *
     * @param bool $force_mode True to enable force mode, false otherwise.
     * @return void
     */
    public function set_force_mode( bool $force_mode ): void {
        $this->update_uncached_option( self::OPT_FORCE_MODE, $force_mode );
    }

    /**
     * Get the term filters for bulk translation.
     *
     * @return array<string,mixed> List of term filters.
     */
    public function get_term_filters(): array {
        return (array) \get_option( self::OPT_TERM_FILTERS, array() );
    }

    /**
     * Set the term filters for bulk translation.
     *
     * @param array<string,mixed> $term_filters List of term filters.
     */
    public function set_term_filters( array $term_filters ): void {
        \update_option( self::OPT_TERM_FILTERS, $term_filters );
    }

    /**
     * Update an uncached option in the database.
     *
     * @param  string $key   Option key.
     * @param  mixed  $value Option value.
     * @return void
     */
    private function update_uncached_option( string $key, $value ): void {
        if ( ! isset( $value ) ) {
            $this->delete_uncached_option( $key );
            return;
        }
        $key = self::OPT_PREFIX . $key;

        $value = \maybe_serialize( $value );

        // Use direct SQL query for better performance.
        $result = $this->db->query(
            $this->db->prepare(
                <<<'SQL'
                INSERT INTO %i (option_name, option_value, autoload)
                VALUES (%s, %s, 'no')
                ON DUPLICATE KEY UPDATE option_value = VALUES(option_value)
                SQL,
                $this->db->options,
                $key,
                $value,
            ),
        );

        if ( false === $result ) {
            \error_log( "Failed to update option: {$key}" );
        }

        \wp_cache_delete( $key, 'options' );
    }

    /**
     * Get an uncached option from the database.
     *
     * @param string $key Option key.
     * @return mixed Option value.
     */
    private function get_uncached_option( string $key ) {
        $key = self::OPT_PREFIX . $key;

        return \maybe_unserialize(
            $this->db->get_var(
                $this->db->prepare(
                    'SELECT option_value FROM %i WHERE option_name = %s',
                    $this->db->options,
                    $key,
                ),
            ),
        );
    }

    /**
     * Delete an uncached option from the database.
     *
     * @param string $key Option key.
     * @return void
     */
    private function delete_uncached_option( string $key ): void {
        $key = self::OPT_PREFIX . $key;

        $this->db->delete( $this->db->options, array( 'option_name' => $key ) );
    }
}

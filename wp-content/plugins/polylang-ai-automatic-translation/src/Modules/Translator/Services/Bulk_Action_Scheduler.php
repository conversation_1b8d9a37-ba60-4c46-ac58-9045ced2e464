<?php //phpcs:disable

namespace PLLAT\Translator\Services;

use ActionScheduler_Action;
use Automattic\Jetpack\Constants;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use WP_User;

class Bulk_Action_Scheduler {
    const GROUP                = 'pllat-bulk-translation';
    const PROCESS_HOOK         = 'pllat_process_bulk_translation_item';
    const PROCESS_HOOK_STRINGS = 'pllat_process_bulk_translation_strings';

    /**
     * Indicates whether the user is logged in.
     *
     * @var ?bool
     */
    private ?bool $logged_in = null;

    public function __construct(
        private readonly Bulk_Config $bulk_config,
        private readonly Bulk_Manager $bulk_manager,
        private readonly Bulk_Stats_Manager $stats_manager,
        private readonly Bulk_Logger $bulk_logger
    ) {
        $this->register_hooks();
    }

    /**
     * Handle the bulk translation item.
     *
     * @param string              $type     Item type (e.g., 'post', 'term', 'string').
     * @param string              $id       Item ID.
     * @param string              $language Language code (e.g., 'en', 'fr').
     * @param array<string,mixed> $args     Additional arguments.
     */
    public function handle_bulk_translation_item( string $type, string $id, string $language, array $args = array() ): void {
        $this->logged_in ??= $this->maybe_login();

        try {
            if ( ! $this->bulk_config->is_activated() || $this->bulk_config->is_stopped() ) {
                return;
            }

            $this->bulk_logger->log_message(
                \sprintf(
                    'Processing bulk translation for "%s" (ID: %d) to %s',
                    $type,
                    $id,
                    \strtoupper( $language ),
                ),
            );

            $this->bulk_config->set_running( true );

            $entity = $args['entity'];
            if ( empty( $entity ) ) {
                return;
            }

            $this->bulk_manager->prepare_item( $type, $id );
            $this->bulk_manager->process_item( $type, $id, $language, $args );
            $item = $this->bulk_manager->get_item( $type, $id );
            $this->stats_manager->update_stats_processed( $type, $entity, $language, 1 );
            $this->stats_manager->store_stats();
            $this->bulk_logger->log_message(
                \sprintf(
                    'Successfully translated "<a href="%s" target="_blank">%s</a>" (ID: %d) to %s',
                    $item->get_edit_link(),
                    $item->get_title(),
                    $id,
                    \strtoupper( $language ),
                ),
            );

            $stats = $this->stats_manager->get_stats();

            if ( $stats->is_queue_empty() ) {
                $this->bulk_config->deactivate();
                $this->bulk_config->set_running( false );
                $this->clear_scheduled_actions();
                Helpers::log( 'Bulk translation stopped.' );
            }
        } catch ( \Throwable $e ) {
            Helpers::log(
                'Error processing bulk translation batch: ' . $e->getMessage(),
                'error',
                array(
                    'args'     => $args,
                    'id'       => $id,
                    'language' => $language,
                    'trace'    => $e->getTrace(),
                    'type'     => $type,
                ),
            );
            $this->log_async_error( $e->getMessage() );
            $this->bulk_logger->log_message( $e->getMessage(), 'error' );
        }
    }

    /**
     * Handle the bulk translation strings.
     *
     * @param array<string>       $strings  Array of strings to translate.
     * @param string              $group    Group name (e.g., 'post', 'term').
     * @param string              $language Language code (e.g., 'en', 'fr').
     * @param array<string,mixed> $args     Additional arguments.
     */
    public function handle_bulk_translation_strings( array $strings, string $group, string $language, array $args = array() ): void {
        try {
            if ( ! $this->bulk_config->is_activated() ) {
                return;
            }

            if ( $this->bulk_config->is_stopped() ) {
                return;
            }

            $this->bulk_config->set_running( true );

            $string_count = \count( $strings );

            $this->bulk_manager->process_strings( $strings, $group, $language, $args );
            $this->stats_manager->update_stats_processed( 'strings', $group, $language, $string_count );
            $this->stats_manager->store_stats();

            foreach ( $strings as $string ) {
                $this->bulk_logger->log_message(
                    \sprintf(
                        'Successfully translated string: "%s" (group: %s) to %s',
                        $string,
                        $group,
                        \strtoupper( $language ),
                    ),
                );
            }

            $stats = $this->stats_manager->get_stats();
            if ( $stats->is_queue_empty() ) {
                $this->bulk_config->deactivate();
                $this->bulk_config->set_running( false );
                $this->clear_scheduled_actions();
                Helpers::log( 'Bulk translation stopped.' );
            }
        } catch ( \Throwable $e ) {
            Helpers::log(
                'Error processing bulk translation strings: ' . $e->getMessage(),
                'error',
                array(
                    'args'     => $args,
                    'language' => $language,
                    'strings'  => $strings,
                    'trace'    => $e->getTrace(),
                ),
            );
            $this->log_async_error( $e->getMessage() );
            $this->bulk_logger->log_message( $e->getMessage(), 'error' );
        }
    }

    public function clear_scheduled_actions(): void {
        $this
            ->clear_actions( self::PROCESS_HOOK )
            ->clear_actions( self::PROCESS_HOOK_STRINGS );
    }

    /**
     * Get all scheduled actions for a specific hook and status.
     *
     * @param  string $hook   Hook name.
     * @param  string $status Action status.
     * @return array<int,ActionScheduler_Action>
     */
    public function get_actions( string $hook, string $status ): array {
        return \as_get_scheduled_actions(
            array(
                'group'    => self::GROUP,
                'hook'     => $hook,
                'per_page' => 100000,
                'status'   => $status,
            ),
        );
    }

    /**
     * Get the async errors.
     *
     * @param  int   $n Number of errors to retrieve.
     * @return array<int,array{message:string,timestamp:int}>
     */
    public function get_async_errors( int $n = 100 ): array {
        $errors = \get_option( 'pllat_bulk_async_errors', array() );
        return \array_slice( $errors, -$n );
    }

    public function clear_async_errors(): void {
        \delete_option( 'pllat_bulk_async_errors' );
    }

    /**
     * Get the bulk configuration.
     *
     * @return Bulk_Config
     */
    public function get_config(): Bulk_Config {
        return $this->bulk_config;
    }

    private function register_hooks(): void {
        \add_action( self::PROCESS_HOOK, array( $this, 'handle_bulk_translation_item' ), 10, 4 );
        \add_action( self::PROCESS_HOOK_STRINGS, array( $this, 'handle_bulk_translation_strings' ), 10, 3 );
        $this->bulk_config->register_hooks();
    }

    private function clear_actions( string $hook ): static {
        $actions = $this->get_actions( $hook, \ActionScheduler_Store::STATUS_PENDING );

        foreach ( $actions as $action ) {
            \as_unschedule_action( $action->get_hook(), $action->get_args(), $action->get_group() );
        }

        return $this;
    }

    private function log_async_error( string $message ): void {
        $errors   = \get_option( 'pllat_bulk_async_errors', array() );
        $errors[] = array(
            'message'   => $message,
            'timestamp' => \time(),
        );
        $errors   = \array_slice( $errors, -100 );
        \update_option( 'pllat_bulk_async_errors', $errors );
    }

    /**
     * If a user is not logged in the wp-cli - Log him in
     */
    private function maybe_login(): ?bool {
        if ( \is_user_logged_in() || ! Constants::is_true( 'WP_CLI' ) ) {
            return false;
        }

        $user = $this->get_admin();

        if ( ! $user ) {
            return null;
        }

        \wp_set_auth_cookie( $user->ID, true );

        // Documented in WP Core.
        \do_action( 'wp_login', $user->user_login, $user );
        \wp_set_current_user( $user->ID, $user->user_login );

        return true;
    }

    private function get_admin(): ?WP_User {
        $admin = \get_users(
            array(
                'role'    => 'administrator',
                'orderby' => 'ID',
                'order'   => 'ASC',
                'number'  => 1,
            ),
        );

        return current( $admin );
    }
}

<?php //phpcs:disable SlevomatCodingStandard.Classes.SuperfluousAbstractClassNaming.SuperfluousPrefix

namespace PLLAT\Translator\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;

/**
 * Manages bulk translation entities.
 */
abstract class Abstract_Bulk_Entity_Manager {
    /**
     * Constructor.
     *
     * @param  Bulk_Config      $config           The configuration object.
     * @param  Translator       $translator       The translator object.
     * @param  Language_Manager $language_manager The language manager object.
     */
    public function __construct(
        protected readonly Bulk_Config $config,
        protected readonly Translator $translator,
        protected readonly Language_Manager $language_manager,
    ) {
    }

    /**
     * Get all untranslated IDs for the given entity type
     *
     * @param  string        $entity_type The type of entity (post_type or taxonomy).
     * @param  array<string> $languages   The target languages.
     * @return array<string,array<int>>
     */
    abstract public function get_translatable_ids( string $entity_type, array $languages = array() ): array;

    /**
     * Prepare a single item for translation
     *
     * @param int           $id The entity ID.
     * @param array<string> $languages The target languages.
     * @return void
     */
    abstract public function prepare_item( int $id, array $languages = array() ): void;

    /**
     * Process a single item for translation
     *
     * @param int                 $id The entity ID.
     * @param string              $language_slug The target language.
     * @param array<string,mixed> $args Additional arguments.
     * @return void
     */
    abstract public function process_item( int $id, string $language_slug, array $args = array() ): void;

    /**
     * Get a translatable entity instance
     *
     * @param int $id The entity ID.
     * @return Translatable_Entity
     */
    abstract public function get_item( int $id ): Translatable_Entity;
}

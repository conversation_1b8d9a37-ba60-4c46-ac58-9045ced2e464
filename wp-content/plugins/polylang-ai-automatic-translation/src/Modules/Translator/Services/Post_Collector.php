<?php //phpcs:disable Universal.Arrays.MixedKeyedUnkeyedArray.Found, WordPress.DB.SlowDBQuery, Squiz.Commenting.FunctionComment.IncorrectTypeHint, WordPress.DB.PreparedSQL.InterpolatedNotPrepared, SlevomatCodingStandard.Complexity.Cognitive.ComplexityTooHigh
/**
 * Post_Collector class file.
 *
 * @package    Polylang Automatic AI Translation
 * @subpackage Common
 */

namespace PLLAT\Translator\Services;

use PLL_Language;
use WP_Post;
use WP_Query;

/**
 * Handles the collection of translatable post IDs.
 */
class Post_Collector {
    /**
     * Default language object.
     *
     * @var PLL_Language
     */
    private PLL_Language $lng;

    /**
     * Constructor.
     *
     * @param  Bulk_Term_Filter_Manager $tfm Term filter manager.
     */
    public function __construct( private readonly Bulk_Term_Filter_Manager $tfm ) {
        $this->lng = \pll_default_language( \OBJECT );
    }

    /**
     * Get translatable IDs for a specific post type and languages.
     *
     * @param  string        $post_type Post type slug.
     * @param  array<string> $languages Array of language codes.
     * @param  int<-1,max>   $limit     Limit for the number of posts to fetch.
     * @return array<string,array<int>> Translatable IDs for each language.
     */
    public function get_translatable_ids( string $post_type, array $languages, int $limit = 10000 ): array {
        $args = array(
            'per_page'  => $limit,
            'post_type' => $post_type,
        );

        return $this->collect_ids( $args, $languages );
    }

    /**
     * Get translatable IDs for a specific post type.
     *
     * @param  string $post_type Post type slug.
     * @return array<int>
     */
    public function get_all_ids( string $post_type ): array {
        return \wp_list_pluck(
            $this->get_posts(
                array(
                    'per_page'  => -1,
                    'post_type' => $post_type,
                ),
            ),
            'ID',
        );
    }

    /**
     * Get translatable IDs for a specific post type and languages.
     *
     * @param  array{post_type?:string, per_page?: int} $args      Query arguments.
     * @param  array<string>                            $languages Array of language codes.
     * @param  callable|null                            $filter    Optional filter function to determine if a post can be translated.
     * @return array<string,array<int>>                            Translatable IDs for each language.
     */
    private function collect_ids( array $args, array $languages, ?callable $filter = null ): array {
        $data     = \array_fill_keys( $languages, array() );
        $filter ??= $this->can_translate( ... );
        $posts    = $this->get_posts( $args );
        $queue    = $this->get_queue( \wp_list_pluck( $posts, 'ID' ) );

        foreach ( $posts as $p ) {
            $lng_map = $this->get_translations( $p );
            $tr_data = $queue[ $p->ID ] ?? array();

            foreach ( $languages as $lang ) {
                if ( ! $filter( $lng_map, $tr_data, $lang ) ) {
                    continue;
                }

                // Missing translation and not queued.
                $data[ $lang ][] = (int) $p->ID;
            }
        }

        return $data;
    }

    /**
     * Check if a post can be translated
     *
     * @param  array<string,int>   $lng_map Map of languages and their IDs.
     * @param  array<string,mixed> $tr_data Translation data.
     * @param  string              $lang    Language code.
     * @return bool
     */
    private function can_translate( array $lng_map, array $tr_data, string $lang ): bool {
        // Post has no existing translation.
        $has_no_translation = ( $lng_map[ $lang ] ?? 0 ) <= 0;

        // Post is either not queued yet (new post) OR has non-empty queue data.
        $can_be_queued = ! isset( $tr_data[ $lang ] ) || array() !== $tr_data[ $lang ];

        return $has_no_translation && $can_be_queued;
    }

    /**
     * Get posts based on query arguments.
     *
     * @param  array{post_type?:string, per_page?: int} $args      Query arguments.
     * @return array<int,WP_Post> Posts.
     */
    private function get_posts( $args ): array {
        global $wpdb;

        // Build and execute the query
        $query    = new WP_Query( $this->get_query_args( $args ) );
        $post_ids = $wpdb->get_col( $query->request );

        // Prime the post cache with our results - this efficiently converts
        // and stores all the post objects at once
        \_prime_post_caches( $post_ids, false, false );

        // Now retrieve the objects from cache as WP_Post objects
        $posts = \array_map( 'get_post', $post_ids );

        // Filter out any false values (unlikely but possible)
        return \array_filter( $posts );
    }

    /**
     * Get translation queue for a list of post IDs.
     *
     * @param  array<int> $ids List of post IDs.
     * @return array<int,array<string,mixed>>
     */
    private function get_queue( array $ids ): array {
        global $wpdb;

        if ( array() === $ids ) {
            return array();
        }

        $id_string = \implode( ', ', $ids );

        $ret = array();
        $res = $wpdb->get_results(
            $wpdb->prepare(
                <<<SQL
                SELECT post_id, meta_value FROM %i
                WHERE 1=1
                    AND meta_key = '_pllat_translation_queue'
                    AND post_id IN ($id_string)
                SQL,
                $wpdb->postmeta,
            ),
            ARRAY_A,
        );

        foreach ( $res as $data ) {
            $post_id = $data['post_id'];
            $queue   = \maybe_unserialize( $data['meta_value'] );

            $ret[ $post_id ] = $queue ? (array) $queue : array();
        }

        return $ret;
    }

    /**
     * Get translations for a specific post.
     *
     * @param  WP_Post $post Post object.
     * @return array<string,int>
     */
    private function get_translations( WP_Post $post ): array {
        $terms = \get_the_terms( $post, 'post_translations' );

        return false !== $terms && ! \is_wp_error( $terms )
            ? ( \maybe_unserialize( \array_shift( $terms )?->description ?? '' ) ?: array() )
            : array();
    }

    /**
     * Get query arguments for fetching posts.
     *
     * @param  array{post_type?:string, per_page?: int} $args Query arguments.
     * @return array<string,mixed>
     */
    private function get_query_args( array $args ): array {
        $args = array(
            'cache_results'          => false,
            'lazy_load_term_meta'    => false,
            'meta_query'             => array(
                array(
                    'relation' => 'OR',
                    array(
                        'compare' => 'NOT EXISTS',
                        'key'     => '_pllat_exclude_from_translation',
                    ),
                    array(
                        'compare' => 'IN',
                        'key'     => '_pllat_exclude_from_translation',
                        'value'   => array( '', null ),
                    ),
                ),
            ),
            'no_found_rows'          => true,
            'posts_per_page'         => $args['per_page'],
            'post_status'            => 'any',
            'post_type'              => $args['post_type'],
            'suppress_filters'       => true,
            'tax_query'              => array(
                array(
                    'field'    => 'term_id',
                    'taxonomy' => 'language',
                    'terms'    => $this->lng->term_id,
                ),
            ),
            'update_post_meta_cache' => false,
            'update_post_term_cache' => false,
        );

        $post_type_term_filters = $this->tfm->get_term_filters_for_post_type( $args['post_type'] );

        if ( array() === $post_type_term_filters ) {
            return $args;
        }

        foreach ( $post_type_term_filters as $taxonomy => $term_ids ) {

            $args['tax_query']['relation'] ??= 'AND';

            if ( \count( $post_type_term_filters ) > 1 ) {
                $args['tax_query'][1] ??= array(
                    'relation' => 'OR',
                );
                $args['tax_query'][1][] = array(
                    'field'    => 'term_id',
                    'taxonomy' => $taxonomy,
                    'terms'    => $term_ids,
                );
            } else {
                $args['tax_query'][] = array(
                    'field'    => 'term_id',
                    'taxonomy' => $taxonomy,
                    'terms'    => $term_ids,
                );
            }
        }

        return $args;
    }
}

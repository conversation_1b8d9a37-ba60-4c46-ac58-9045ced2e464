<?php //phpcs:disable

namespace PLLAT\Translator\Services;

use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;

class Bulk_Manager {


    private const TRANSIENT_KEY               = 'pllat_bulk_translation_queue';
    private const TRANSIENT_KEY_COMPRESSED    = 'pllat_bulk_translation_queue_compressed';
    private const TRANSIENT_KEY_IS_COMPRESSED = 'pllat_bulk_translation_queue_is_compressed';
    private const COMPRESSION_THRESHOLD       = 1000000; // 1MB

    public function __construct(
        private readonly Bulk_Term_Manager $term_manager,
        private readonly Bulk_Term_Filter_Manager $term_filter_manager,
        private readonly Bulk_Post_Manager $post_manager,
        private readonly Bulk_String_Manager $string_manager,
        private readonly Bulk_Config $config) {
    }

    public function get_item(string $type, int|string $id): Translatable_Entity {
        return match($type) {
            'posts' => $this->post_manager->get_item($id),
            'terms' => $this->term_manager->get_item($id),
            default => throw new \InvalidArgumentException('Invalid type provided.'),
        };

    }

    /**
     * Build the translation queue for all entity types
     *
     * @param array<string> $post_types
     * @param array<string> $taxonomies
     * @param array<string> $string_groups
     * @param array<string> $languages
     */
    public function build_queue( array $post_types, array $taxonomies, array $string_groups, array $languages ): void {
        $queue = array(
            'posts'   => array(),
            'strings' => array(),
            'terms'   => array(),
        );

        $bulk_size  = $this->config->get_bulk_size();
        $force_mode = $this->config->get_force_mode();

        if ( ! empty( $post_types ) ) {
            foreach ( $post_types as $post_type ) {
                $queue['posts'][ $post_type ] = array();

                if ( $force_mode ) {
                    $ids = $this->post_manager->get_all_ids( $post_type );
                    foreach ( $languages as $language ) {
                        $queue['posts'][ $post_type ][ $language ] = \array_slice( $ids, 0, $bulk_size );
                    }
                } else {
                    $ids = $this->post_manager->get_translatable_ids( $post_type, $languages );
                    foreach ( $languages as $language ) {
                        if ( empty( $ids[ $language ] ) ) {
                            continue;
                        }

                        $queue['posts'][ $post_type ][ $language ] = \array_slice(
                            $ids[ $language ],
                            0,
                            $bulk_size,
                        );
                    }
                }
            }
        }

        // Get all term IDs in parallel
        if ( ! empty( $taxonomies ) ) {
            foreach ( $taxonomies as $taxonomy ) {
                $queue['terms'][ $taxonomy ] = array();

                if ( $force_mode ) {
                    $ids = $this->term_manager->get_all_ids( $taxonomy );
                    foreach ( $languages as $language ) {
                        $queue['terms'][ $taxonomy ][ $language ] = \array_slice( $ids, 0, $bulk_size );
                    }
                } else {
                    $ids = $this->term_manager->get_translatable_ids( $taxonomy, $languages );
                    foreach ( $languages as $language ) {
                        if ( empty( $ids[ $language ] ) ) {
                            continue;
                        }

                        $queue['terms'][ $taxonomy ][ $language ] = \array_slice(
                            $ids[ $language ],
                            0,
                            $bulk_size,
                        );
                    }
                }
            }
        }

        // Get all strings in parallel
        if ( ! empty( $string_groups ) ) {
            foreach ( $string_groups as $string_group ) {
                $queue['strings'][ $string_group ] = array();

                if ( $force_mode ) {
                    $strings = $this->string_manager->get_all_strings( $string_group );
                    foreach ( $languages as $language ) {
                        $queue['strings'][ $string_group ][ $language ] = \array_slice(
                            $strings,
                            0,
                            $bulk_size,
                        );
                    }
                } else {
                    foreach ( $languages as $language ) {
                        $strings = $this->string_manager->get_untranslated_strings( $string_group, $language );
                        if ( empty( $strings ) ) {
                            continue;
                        }

                        // Apply bulk size limit to strings as well
                        $queue['strings'][ $string_group ][ $language ] = \array_slice(
                            $strings,
                            0,
                            $bulk_size,
                        );
                    }
                }
            }
        }

        $this->store_queue( $queue );
    }

    /**
     * Schedule posts for translation
     *
     * @param array<string,mixed> $queue      Queue to schedule.
     * @param array<string>       $post_types Post types to schedule.
     * @param array<string>       $languages  Languages to schedule.
     */
    public function schedule_posts( array $queue, array $post_types, array $languages ): void {
        foreach ( $post_types as $post_type ) {
            $this->schedule_items( $queue, 'posts', $post_type, $languages );
        }
    }

    /**
     * Schedule terms for translation
     *
     * @param array<string,mixed> $queue      Queue to schedule.
     * @param array<string>       $taxonomies Taxonomies to schedule.
     * @param array<string>       $languages  Languages to schedule.
     */
    public function schedule_terms( array $queue, array $taxonomies, array $languages ): void {
        foreach ( $taxonomies as $taxonomy ) {
            $this->schedule_items( $queue, 'terms', $taxonomy, $languages );
        }
    }

    /**
     * Schedule strings for translation
     *
     * @param array<string,mixed> $queue         Queue to schedule.
     * @param array<string>       $string_groups String groups to schedule.
     * @param array<string>       $languages     Languages to schedule.
     */
    public function schedule_strings( array $queue, array $string_groups, array $languages ): void {
        $args  = $this->prepare_args();
        $index = 0;

        foreach ( $string_groups as $string_group ) {
            foreach ( $languages as $language ) {
                $strings = $queue['strings'][ $string_group ][ $language ];
                if ( ! $strings ) {
                    continue;
                }

                \as_schedule_single_action(
                    timestamp: \time() + ( ++$index ),
                    hook: Bulk_Action_Scheduler::PROCESS_HOOK_STRINGS,
                    args: array(
                        'strings'  => $strings,
                        'group'    => $string_group,
                        'language' => $language,
                        'args'     => $args,
                    ),
                    group: Bulk_Action_Scheduler::GROUP,
                    priority: 10,
                );

            }
        }
    }

    /**
     * Schedule items for translation
     *
     * @param array<string,mixed> $queue     Queue to schedule.
     * @param string              $type      Type of item to schedule.
     * @param string              $entity    Entity type to schedule.
     * @param array<string>       $languages Array of languages to schedule.
     */
    public function schedule_items( array $queue, string $type, string $entity, array $languages ): void {
        $args  = $this->prepare_args( array( 'entity' => $entity ) );
        $index = 0;

        foreach ( $languages as $language ) {
            $ids = $queue[ $type ][ $entity ][ $language ] ?? [];
            foreach ( $ids as $id ) {
                $ret = \as_schedule_single_action(
                    timestamp: \time() + ( ++$index ),
                    hook: Bulk_Action_Scheduler::PROCESS_HOOK,
                    args: array(
                        'type'     => $type,
                        'id'       => $id,
                        'language' => $language,
                        'args'     => $args,
                    ),
                    group: Bulk_Action_Scheduler::GROUP,
                    priority: 10,
                );
            }
        }
    }

    /**
     * Prepare an item for translation
     *
     * @param  'posts'|'terms' $type      Type of item to prepare.
     * @param  int|string      $id        ID of the item to prepare.
     * @param  array<string>   $languages Languages to prepare the item for.
     */
    public function prepare_item( string $type, int|string $id, array $languages = array() ):void {
        match($type) {
            'posts' => $this->post_manager->prepare_item($id, $languages),
            'terms' => $this->term_manager->prepare_item($id, $languages),
        };
    }

    /**
     * Process an item for translation
     *
     * @param  'posts'|'terms'     $type     Type of item to process.
     * @param  int|string          $id       ID of the item to process.
     * @param  string              $language Language to process the item for.
     * @param  array<string,mixed> $args     Additional arguments for processing.
     */
    public function process_item( string $type, int|string $id, string $language, array $args = array() ):void {
        // Pass force mode to the item processor so we can set field keys virtually.
        $force_mode = $this->config->get_force_mode();
        if ( $force_mode ) {
            $args['force_mode'] = true;
        }

        if ( 'posts' === $type ) {
            $this->post_manager->process_item( $id, $language, $args );
            return;
        }

        if ( 'terms' === $type ) {
            $this->term_manager->process_item( $id, $language, $args );
            return;
        }
    }

    /**
     * Process strings for translation
     *
     * @param  array<string,mixed> $strings  Array of strings to process.
     * @param  string              $group    Group of strings to process.
     * @param  string              $language Language to process the strings for.
     * @param  array<string,mixed> $args     Additional arguments for processing.
     */
    public function process_strings( array $strings, string $group, string $language, array $args = array() ): void {
        $this->string_manager->process_strings( $strings, $group, $language, $args );
    }

    /**
     * Prepare bulk translation arguments
     *
     * @param  array<string,mixed> $args Array of arguments to prepare.
     * @return array<string,mixed>
     */
    private function prepare_args( array $args = array() ): array {
        // Check whether there is ai website context set
        $website_context = Settings::get_website_ai_context();
        if ( ! empty( $website_context ) ) {
            $args['website_context'] = $website_context;
        }

        // Check whether there are additional ai instructions set
        $additional_instructions = $this->config->get_additional_instructions();
        if ( ! empty( $additional_instructions ) ) {
            $args['additional_instructions'] = $additional_instructions;
        }
        return $args;
    }

    public function get_string_manager(): Bulk_String_Manager {
        return $this->string_manager;
    }

    /**
     * Store the queue in a transient, with compression if needed
     *
     * @param array<string, array<string, array<string, array<int>>>> $queue Queue to store.
     */
    private function store_queue( array $queue ): void {
        if ( \strlen( \serialize( $queue ) ) > self::COMPRESSION_THRESHOLD ) {
            $compressed = \gzcompress( \serialize( $queue ), 9 );
            \set_transient( self::TRANSIENT_KEY_COMPRESSED, $compressed );
            \set_transient( self::TRANSIENT_KEY_IS_COMPRESSED, true );
        } else {
            \set_transient( self::TRANSIENT_KEY, $queue );
            \delete_transient( self::TRANSIENT_KEY_IS_COMPRESSED );
        }
    }

    /**
     * Get the queue from transient
     *
     * @return array<string, array<string, array<string, array<int>>>>
     */
    public function get_queue(): array {
        if ( \get_transient( self::TRANSIENT_KEY_IS_COMPRESSED ) ) {
            $compressed = \get_transient( self::TRANSIENT_KEY_COMPRESSED );
            return $compressed ? \unserialize( \gzuncompress( $compressed ) ) : array();
        }
        return \get_transient( self::TRANSIENT_KEY ) ?: array();
    }

    /**
     * Get taxonomies with terms for a post type
     *
     * @param  string $post_type
     * @return array<string,mixed>
     */
    public function get_taxonomies_with_terms( string $post_type ): array {
        return $this->term_filter_manager->get_taxonomies_with_terms( $post_type );
    }

    /**
     * Get term filters for a post type
     *
     * @param  string $post_type
     * @return array<string,mixed>
     */
    public function get_term_filters_for_post_type( string $post_type ): array {
        return $this->term_filter_manager->get_term_filters_for_post_type( $post_type );
    }

    /**
     * Get all term filters
     *
     * @return array<string,mixed>
     */
    public function get_all_term_filters(): array {
        return $this->term_filter_manager->get_all_term_filters();
    }

    /**
     * Set term filters for a post type
     *
     * @param string $post_type
     * @param array<string,mixed>  $filters
     */
    public function set_term_filters_for_post_type( string $post_type, array $filters ): void {
        $this->term_filter_manager->set_term_filters_for_post_type( $post_type, $filters );
    }

    /**
     * Set all term filters
     *
     * @param array<string,mixed> $filters
     */
    public function set_all_term_filters( array $filters ): void {
        $this->term_filter_manager->set_all_term_filters( $filters );
    }
}

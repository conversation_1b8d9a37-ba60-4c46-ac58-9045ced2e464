<?php // phpcs:disable

namespace PLLAT\Translator\Services;

class Bulk_Logger {

    private string $log_dir;
    private ?string $current_log_file;
    private int $max_log_files = 10;

    public function __construct(private readonly Bulk_Config $config) {
        $this->log_dir = PLLAT_PLUGIN_LOG_DIR . '/bulk-logs';
        if (!file_exists($this->log_dir)) {
            wp_mkdir_p($this->log_dir);
            file_put_contents($this->log_dir . '/.htaccess', "Deny from all\n");
            file_put_contents($this->log_dir . '/index.php', "<?php\n// Silence is golden.");
        }
        $this->set_current_log_file();
    }

    public function can_log(): bool {
        return is_writable($this->log_dir);
    }

    public function start_new_log(): void {
        if (!$this->current_log_file) {
            $this->create_new_log_file();
        }
        $this->cleanup_old_logs();
        $this->log_message("Starting new bulk translation session..");
    }

    private function create_new_log_file(): void {
        $timestamp = date('Y-m-d_H-i-s');
        file_put_contents($this->log_dir . "/bulk_translation_{$timestamp}.log", '');
        $this->config->set_current_logs_timestamp($timestamp);
        $this->set_current_log_file();
    }

    public function log_message(string $message, string $level = 'info'): void {
        if (!$this->current_log_file) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $formatted_message = sprintf("[%s] [%s] %s\n", $timestamp, strtoupper($level), $message);
        file_put_contents($this->current_log_file, $formatted_message, FILE_APPEND);
    }

    public function get_current_log_contents(): string {
        if (!$this->current_log_file || !file_exists($this->current_log_file)) {
            return '';
        }
        return file_get_contents($this->current_log_file);
    }

    /**
     * Get the contents of the current log file
     *
     * @return array<string>
     */
    public function get_logs(): array {
        $content = $this->get_current_log_contents();
        return !empty($content) ? array_values(array_filter(explode("\n", $content))) : [];
    }

    public function cleanup_old_logs(): void {
        $files = glob($this->log_dir . '/bulk_translation_*.log');
        if (!empty($files) && count($files) >= $this->max_log_files) {
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            $files_to_delete = array_slice($files, 0, count($files) - $this->max_log_files + 1);
            foreach ($files_to_delete as $file) {
                if (file_exists($file)) {
                    unlink($file);
                }
            }
        }
    }

    private function set_current_log_file(): void {
        $timestamp = $this->config->get_current_logs_timestamp();
        if (!$timestamp) {
            $this->current_log_file = null;
            return;
        }
        $this->current_log_file = $this->log_dir . "/bulk_translation_{$timestamp}.log";
    }

    public function get_current_log_file(): ?string {
        return $this->current_log_file;
    }

    public function reset_current_log_file(): void {
        $this->current_log_file = null;
        $this->config->delete_current_logs_timestamp();
    }
}

<?php //phpcs:disable

namespace PLLAT\Translator\Services;

class Bulk_Stats_Manager {

    /**
     * @var Bulk_Stats
     */
    private Bulk_Stats $stats;

    /**
     * Cache for queue counts.
     *
     * @var array<string,int>
     */
    private array $queue_counts_cache = [];

    public function __construct(private readonly Bulk_Config $config) {

        $stored_stats = $this->load_stats();
        if (!empty($stored_stats)) {
            $this->stats = new Bulk_Stats($stored_stats);
        } else {
            $this->stats = new Bulk_Stats();
        }
    }

    public function get_stats(): Bulk_Stats {
        return $this->stats;
    }

    /**
     * Calculate stats for the given queue.
     *
     * @param array<mixed>  $queue            The queue data.
     * @param string        $type             The type of entity (e.g., 'posts', 'terms', 'strings').
     * @param string        $entity           The entity name (e.g., 'post', 'term').
     * @param array<string> $active_languages The active languages.
     */
    public function calculate_stats(array $queue, string $type, string $entity, array $active_languages): void {
        foreach ($active_languages as $language) {
            $count = $this->get_count_for_language($queue, $type, $entity, $language);
            $this->stats->update_stats_total($type, $entity, $language, $count);
        }
    }

    /**
     * Get the count of items in the queue for a specific language.
     *
     * @param array<mixed> $queue The queue data.
     * @param string $type The type of entity (e.g., 'posts', 'terms', 'strings').
     * @param string $entity The entity name (e.g., 'post', 'term').
     * @param string $language The language code (e.g., 'en', 'fr').
     *
     * @return int The count of items in the queue for the specified language.
     */
    public function get_count_for_language(array $queue, string $type, string $entity, string $language): int {

        $cache_key = "{$type}_{$entity}_{$language}";

        if (isset($this->queue_counts_cache[$cache_key])) {
            return $this->queue_counts_cache[$cache_key];
        }

        if ($type === 'strings') {
            $count = count($queue['strings'][$entity][$language] ?? []);
        } else {
            $count = empty($queue[$type][$entity][$language]) ? 0 : count($queue[$type][$entity][$language]);
        }

        $this->queue_counts_cache[$cache_key] = $count;
        return $count;
    }

    public function update_stats_processed(string $type, string $entity, string $language, int $count): void {
        $this->stats->update_stats_processed($type, $entity, $language, $count);
    }

    public function store_stats(): void {
        $current_stats = $this->stats->get_stats();
        if (!empty($current_stats)) {
            $this->config->set_stats($current_stats);
        }
        // Clear the cache after storing
        $this->queue_counts_cache = [];
    }

    /**
     * Load stats from the config.
     *
     * @return array<string,mixed>
     */
    public function load_stats(): array {
        return $this->config->get_stats() ?: [];
    }

    public function delete_stats(): void {
        $this->config->delete_stats();
        $this->stats->reset();
    }
}

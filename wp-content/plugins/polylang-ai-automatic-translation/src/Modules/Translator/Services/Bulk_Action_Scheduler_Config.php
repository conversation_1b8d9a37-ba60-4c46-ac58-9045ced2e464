<?php //phpcs:disable Generic.CodeAnalysis.UnusedFunctionParameter.Found
/**
 * Bulk_Action_Scheduler_Config class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Translator
 */

namespace PLLAT\Translator\Services;

/**
 * Configuration class for Action Scheduler
 */
class Bulk_Action_Scheduler_Config {
    private const OPTION_PREFIX     = 'pllat_';
    private const TIME_LIMIT_OPTION = self::OPTION_PREFIX . 'action_scheduler_time_limit';
    private const BATCH_SIZE_OPTION = self::OPTION_PREFIX . 'action_scheduler_batch_size';

    // Default values (WordPress defaults).
    private const DEFAULT_TIME_LIMIT = 60;
    private const DEFAULT_BATCH_SIZE = 25;

    /**
     * Get the time limit for the Action Scheduler
     *
     * @return int
     */
    public function get_time_limit(): int {
        return (int) \get_option( self::TIME_LIMIT_OPTION, self::DEFAULT_TIME_LIMIT );
    }

    /**
     * Set the time limit for the Action Scheduler
     *
     * @param int $value The time limit value.
     */
    public function set_time_limit( int $value ): void {
        \update_option( self::TIME_LIMIT_OPTION, \max( 10, \min( 300, $value ) ) );
    }

    /**
     * Get the batch size for the Action Scheduler
     *
     * @return int
     */
    public function get_batch_size(): int {
        return (int) \get_option( self::BATCH_SIZE_OPTION, self::DEFAULT_BATCH_SIZE );
    }

    /**
     * Set the batch size for the Action Scheduler
     *
     * @param int $value The batch size value.
     */
    public function set_batch_size( int $value ): void {
        \update_option( self::BATCH_SIZE_OPTION, \max( 1, \min( 100, $value ) ) );
    }

    /**
     * Register hooks for Action Scheduler configuration
     */
    public function register_hooks(): void {
        // Always force concurrent batches to 1 to prevent stats discrepancy.
        \add_filter(
            'action_scheduler_queue_runner_concurrent_batches',
            static fn() => 1,
        );
    }

    /**
     * Filter the time limit for the Action Scheduler
     *
     * @param  int $value The current time limit value.
     * @return int
     */
    public function filter_time_limit( int $value ): int {
        return $this->get_time_limit();
    }

    /**
     * Filter the batch size for the Action Scheduler
     *
     * @param  int $value The current batch size value.
     * @return int
     */
    public function filter_batch_size( int $value ): int {
        return $this->get_batch_size();
    }
}

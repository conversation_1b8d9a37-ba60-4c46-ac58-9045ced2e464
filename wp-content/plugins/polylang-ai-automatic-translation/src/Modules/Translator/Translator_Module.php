<?php
/**
 * Translator_Module class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Translator
 */

namespace PLLAT\Translator;

use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator_Factory;
use XWP\DI\Decorators\Module;

/**
 * Translator module definition.
 */
#[Module(
    hook: 'pll_init',
    priority: 1,
    handlers: array( Handlers\Bulk_Translation_Handler::class ),
    services: array(
        Services\Bulk_Config::class,
        Services\Bulk_Action_Scheduler_Config::class,
        Language_Manager::class,
    ),
)]
class Translator_Module {
    /**
     * Module definition.
     *
     * @return array<string,mixed>
     */
    public static function configure(): array {
        return array(
            Translator::class => \DI\factory( array( Translator_Factory::class, 'create_translator' ) )
                ->parameter( 'translator', \DI\factory( Settings::get_active_translation_api( ... ) ) ),
        );
    }
}

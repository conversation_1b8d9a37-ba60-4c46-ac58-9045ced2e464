<?php

namespace PLLAT\Test\Handlers;

use EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor\HTML_Content_String_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor\WP_Block_Content_String_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor\Elementor_Content_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use PLLAT\Common\Interfaces\Extracts_String;
use PLLAT\Test\Services\Test_Runner_Factory;
use WP_CLI;
use XWP\DI\Decorators\CLI_Command;
use XWP\DI\Decorators\CLI_Handler;

/**
 * Handles commands for testing the plugin
 */
#[CLI_Handler( namespace: 'pllat', description: 'Polylang AI Automatic Translation', container: 'pllat' )]
class Test_Command_Handler {
    /**
     * Test the content extraction and replacement of Elementor post content
     *
     * @param  'post'|'file'|'input' $source The content source.
     * @param  array<string,string>  $files  The files to test.
     */
    #[CLI_Command(
        command: 'test-extract-replace-elementor',
        summary: 'Test the content extraction and replacement',
        args: array(
            array(
                'description' => 'Content source',
                'name'        => 'source',
                'options'     => array( 'file', 'elementor' ),
                'type'        => 'positional',
                'var'         => 'source',
            ),
        ),
        params: array( 'files' => 'test.files' ),
    )]
    public function run_extract_replace_test_elementor( string $source, array $files ): void {
        $content = $this->get_content( $source, $files );

        $content_editor    = new Elementor_Content_Editor();
        $extracted_strings = $content_editor->extract_strings( $content );

        \WP_CLI::log( 'Extracted strings: ' . \print_r( $extracted_strings, true ) );

        $translated_strings = array();
        foreach ( $extracted_strings as $hash => $string ) {
            $translated_strings[ $hash ] = 'REPLACED: ' . $string['value'];
        }

        $updated_elementor_data = $content_editor->replace_translated_strings(
            \json_decode( $content, true ),
            $extracted_strings,
            $translated_strings,
        );

        \WP_CLI::log( 'Updated elementor data: ' . \print_r( $updated_elementor_data, true ) );
    }

    /**
     * Test content extraction and replacement
     *
     * @param  'post'|'file'|'input' $source The content source.
     * @param  array<string,string>  $files  The files to test.
     */
    #[CLI_Command(
        command: 'test-extract-replace',
        summary: 'Test the content extraction and replacement',
        args: array(
            array(
                'default'     => 'input',
                'description' => 'Content source',
                'name'        => 'source',
                'optional'    => true,
                'options'     => array( 'post', 'file', 'input' ),
                'type'        => 'positional',
                'var'         => 'source',
            ),
        ),
        params: array( 'files' => 'test.files' ),
    )]
    public function run_extract_replace_test( string $source, array $files ): void {
        $content = $this->get_content( $source, $files );

        // First extract html strings
        $content_editor    = new HTML_Content_String_Editor();
        $extracted_strings = $content_editor->extract_strings( $content );

        // Then extract wp block strings
        $block_editor            = new WP_Block_Content_String_Editor(
            Settings::get_wp_block_translatable_keys(),
        );
        $extracted_block_strings = $block_editor->extract_strings( $content );
        $all_extracted_strings   = array( ...$extracted_strings, ...$extracted_block_strings );

        $search_replace_pairs = \array_map(
            static fn( $str ) => array(
                'replace' => 'REPLACED: {{' . $str . '}}',
                'search'  => $str,
            ),
            $all_extracted_strings,
        );

        // Get only the pairs where the search string also exists in extracted_strings
        $html_search_replace_pairs = \array_filter(
            $search_replace_pairs,
            static fn( $pair ) => \in_array( $pair['search'], $extracted_strings ),
        );

        // Get only the strings found in the wp blocks
        $block_search_replace_pairs = \array_filter(
            $search_replace_pairs,
            static fn( $pair ) => \in_array( $pair['search'], $extracted_block_strings ),
        );

        // Replace html, attributes and wp block strings in content
        $replaced_content = $content_editor->replace_strings( $content, $html_search_replace_pairs );
        $replaced_content = $block_editor->replace_strings( $replaced_content, $block_search_replace_pairs );

        // Get the replacements
        $strings_replacements       = $content_editor->get_string_replacements();
        $attribute_replacements     = $content_editor->get_attribute_string_replacements();
        $block_strings_replacements = $block_editor->get_string_replacements();

        // All found strings counted
        WP_CLI::log( 'All found strings count: ' . \count( $all_extracted_strings ) );
        WP_CLI::log( 'Found html strings count: ' . \count( $extracted_strings ) );
        WP_CLI::log( 'Found block editor strings count: ' . \count( $extracted_block_strings ) );

        // All replaced strings counted
        WP_CLI::log( 'Replaced html strings count: ' . \count( $strings_replacements ) );
        WP_CLI::log(
            'Replaced html attribute strings count: ' . \count(
                $attribute_replacements,
            ),
        );
        WP_CLI::log( 'Replaced block editor strings count: ' . \count( $block_strings_replacements ) );

        // All found strings
        WP_CLI::log( 'Found html strings: ' . \print_r( $extracted_strings, true ) );
        WP_CLI::log( 'Found block editor strings: ' . \print_r( $extracted_block_strings, true ) );

        // All replaced strings
        WP_CLI::log( 'Replaced html strings: ' . \print_r( $strings_replacements, true ) );
        WP_CLI::log(
            'Replaced attribute strings: ' . \print_r(
                $attribute_replacements,
                true,
            ),
        );
        WP_CLI::log( 'Replaced block editor strings: ' . \print_r( $block_strings_replacements, true ) );

        // The final content
        WP_CLI::log( $replaced_content );
    }

    /**
     * Test the content extraction
     *
     * @param  Extracts_String       $extractor The extractor to test.
     * @param  'post'|'file'|'input' $source    The content source.
     * @param  array<string,string>  $files     The files to test.
     */
    #[CLI_Command(
        command: 'test-extraction',
        summary: 'Test the content extraction',
        args: array(
            array(
                'description' => 'Type of extraction to test',
                'format'      => Test_Runner_Factory::class . '::make_extractor',
                'name'        => 'extractor',
                'optional'    => false,
                'options'     => array( Test_Runner_Factory::class . '::get_extractors' ),
                'repeating'   => false,
                'type'        => 'positional',
                'var'         => 'extractor',
            ),
            array(
                'default'     => 'input',
                'description' => 'Content source',
                'name'        => 'source',
                'optional'    => true,
                'options'     => array( 'post', 'file', 'input' ),
                'type'        => 'positional',
                'var'         => 'source',
            ),
            array(
                'default'     => '',
                'description' => 'Content to test',
                'name'        => 'content',
                'optional'    => true,
                'type'        => 'assoc',
            ),
        ),
        params: array( 'files' => 'test.files' ),
    )]
    public function run_extraction_test( Extracts_String $extractor, string $source, array $files ): void {
        $content = $this->get_content( $source, $files );

        //phpcs:ignore
        \print_r( $extractor->extract_strings( $content ) );

        die;
    }

    /**
     * Test the content replacement
     *
     * @param  'post'|'file'|'input' $source    The content source.
     * @param  array<string,string>  $files     The files to test.
     * @return string
     */
    private function get_content( string $source, array $files ): string {
        return match ( $source ) {
            'post' => $this->get_post_content(),
            'file' => $this->get_file_content( $files ),
            'input' => $this->get_input_content(),
            'elementor' => $this->get_elementor_content(),
        };
    }

    /**
     * Get the post content
     *
     * @return string
     */
    private function get_post_content(): string {
        $post_id = (int) CLI_Handler::prompt( 'Enter the post ID:', false );

        $post = \get_post( $post_id );

        if ( ! $post ) {
            WP_CLI::error( "Post with ID $post_id not found." );
        }

        return $post->post_content;
    }

    /**
     * Get the content from the user
     *
     * @param  array<string,string> $files The files to test.
     *
     * @return string
     */
    private function get_file_content( array $files ): string {
        $file = CLI_Handler::choice( 'Available files:', \array_keys( $files ) );

        return \xwp_wpfs()->get_contents( $files[ $file ] );
    }

    /**
     * Get the content from the user input
     *
     * @return string
     */
    private function get_input_content(): string {
        return CLI_Handler::prompt( 'Enter the content to test:', true );
    }

    /**
     * Get the elementor content
     *
     * @return string
     */
    private function get_elementor_content(): string {
        $post_id = (int) CLI_Handler::prompt( 'Enter the post ID:', false );

        $post = \get_post( $post_id );

        if ( ! $post ) {
            WP_CLI::error( "Post with ID $post_id not found." );
        }

        $elementor_data = \get_post_meta( $post_id, '_elementor_data', true );
        return $elementor_data;
    }
    //phpcs:enable
}

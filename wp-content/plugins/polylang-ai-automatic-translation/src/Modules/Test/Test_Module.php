<?php
namespace PLLAT\Test;

use XWP\DI\Container;
use XWP\DI\Decorators\Module;

#[Module( hook: 'init', priority: 5, handlers: array( Handlers\Test_Command_Handler::class ) )]
class Test_Module {
    /**
     * Check if the module can be initialized.
     *
     * @param  Container $ctr Container instance.
     * @return bool
     */
    public static function can_initialize( Container $ctr ): bool {
        return 'production' !== $ctr->get( 'app.env' ) || $ctr->get( 'app.debug' );
    }

    /**
     * Configure the module.
     *
     * @return array<string,mixed>
     */
    public static function configure(): array {
        return array(
            'test.files'      => \DI\factory( array( Services\Test_Runner_Factory::class, 'get_test_files' ) )
                ->parameter( 'basedir', \DI\get( 'app.path' ) ),
            'test.files.opts' => \DI\factory( static fn( $f ) => \array_keys( $f ) )
                ->parameter( 'f', \DI\get( 'test.files' ) ),
        );
    }
}

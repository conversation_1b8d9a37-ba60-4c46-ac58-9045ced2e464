<?php
/**
 * Health_Test interface file.
 *
 * @package    Polylang AI Automatic Translation
 * @subpackage Status
 */

namespace PLLAT\Status\Interfaces;

use DI\Definition\Definition;
use PLLAT\Status\Enums\TestType;
use XWP\DI\Container;

/**
 * Describes a health test.
 */
interface Health_Test {
    /**
     * Test prefix.
     *
     * @var string
     */
    public const TEST_PFX = 'pllat_';

    /**
     * Test name.
     *
     * @var string
     */
    public const NAME = 'test';

    /**
     * Get the test definition.
     *
     * @return array<string,Definition>
     */
    public static function define(): array;

    /**
     * Get the invocation params.
     *
     * @return array<string,Definition>
     */
    public function get_params(): array;

    /**
     * Get the test type.
     *
     * @return TestType
     */
    public function get_type(): TestType;

    /**
     * Get the prefixed test ID.
     *
     * @return string
     */
    public function get_id(): string;

    /**
     * Get the test name.
     *
     * @return string
     */
    public function get_name(): string;

    /**
     * Get the test data.
     *
     * @param Container $ctr Container instance.
     * @return array{
     *   label:              string,
     *   test:               callable|string,
     *   has_rest?:          bool,
     *   async_direct_test?: callable,
     *   skip_cron?:         bool,
     * }
     */
    public function get_data( Container $ctr ): array;
}

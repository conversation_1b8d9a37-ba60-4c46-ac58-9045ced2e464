<?php //phpcs:disable Squiz.Commenting.FunctionComment.Missing, WordPress.PHP.DiscouragedPHPFunctions.system_calls_exec
/**
 * Scheduler_Test class file.
 *
 * @package    Polylang AI Automatic Translation
 * @subpackage Status
 */

namespace PLLAT\Status\Services;

use Automattic\Jetpack\Constants;
use PLLAT\Status\Enums\TestResult;
use PLLAT\Status\Enums\TestType;
use Poliander\Cron\CronExpression;
use XWP\DI\Container;

/**
 * Test the scheduler status.
 */
class Scheduler_Test extends Base_Health_Test {
    public const NAME = 'scheduler';

    /**
     * Run the test
     *
     * @return array<string,mixed>
     */
    public function __invoke(): array {
        $result = Constants::is_true( 'DISABLE_WP_CRON' )
            ? $this->test_external()
            : $this->test_internal();

        return $this->format_result( $result );
    }

    public function get_type(): TestType {
        return TestType::Async;
    }

    public function get_name(): string {
        return 'scheduler';
    }

    public function get_data( Container $ctr ): array {
        return array(
            'async_direct_test' => $this->get_callback( $ctr ),
            'has_rest'          => true,
            'label'             => \pll__( 'Scheduled task handler' ),
            'skip_cron'         => false,
            'test'              => $this->get_rest_ep(),
        );
    }

    public function get_params(): array {
        return array();
    }

    private function test_external(): array {
        $job = $this->get_cronjobs();

        if ( ! $job || ! $job->getNext() ) {
            return array(
                'actions'     => \sprintf(
                    '<a href="%s" target="_blank">%s</a>',
                    \admin_url( 'https://runcloud.io/blog/external-cron-jobs-in-wordpress' ),
                    \pll__( 'Learn how you can set up an external cron job' ),
                ),
                'description' => \pll__(
                    'Scheduled tasks are using the external cron handler, but we could not find any cron jobs. Please make sure that the cron job is set up correctly.',
                ),
                'label'       => \pll__( 'Scheduled task handler' ),
                'status'      => TestResult::Fail,
            );
        }

        return array(
            'description' => \pll__(
                'You are using an external cron handler, which is recommended for better performance.',
            ),
            'label'       => \pll__( 'Scheduled task handler' ),
            'status'      => TestResult::Pass,
        );
    }

    /**
     * Get the expression for the wp-cron job.
     *
     * @return ?CronExpression
     */
    private function get_cronjobs(): ?CronExpression {
        foreach ( \explode( "\n", \exec( 'crontab -l' ) ) as $job_line ) {
            if ( ! \preg_match( '/(wp-cron\.php|wp cron)/i', $job_line ) ) {
                continue;
            }

            if ( ! \preg_match( '/(^.+ \*)/', $job_line, $matches ) ) {
                continue;
            }

            return new CronExpression( $matches[1] );
        }

        return null;
    }

    private function test_internal(): array {
        return array(
            'description' => \pll__(
                'You are using an external cron handler, which is recommended for better performance.',
            ),
            'label'       => \pll__( 'Scheduled task handler' ),
            'status'      => TestResult::Pass,
        );
    }
}

<?php //phpcs:disable SlevomatCodingStandard.Arrays.AlphabeticallySortedByKeys.IncorrectKeyOrder
/**
 * Debug_Data class file.
 *
 * @package    Polylang AI Automatic Translation
 * @subpackage Status
 */

namespace PLLAT\Status\Services;

use Automattic\Jetpack\Constants;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use PLLAT\Translator\Services\Bulk_Config;
use SC_License;

/**
 * Debug data service.
 */
class Debug_Data {
    /**
     * Constructor.
     *
     * @param Bulk_Config $bc Bulk configuration.
     * @param SC_License  $lc License object.
     */
    public function __construct(
        private readonly Bulk_Config $bc,
        private readonly SC_License $lc,
    ) {
    }

    /**
     * Get the complete debug data for the plugin.
     *
     * @return array<string,mixed>
     */
    public function __invoke(): array {
        return array(
            'description' => \pll__( 'Polylang AI Translation plugin debug information.' ),
            'fields'      => array(
                'version'         => $this->debug_version(),
                'license-key'     => $this->debug_license(),
                'bulk-translator' => $this->debug_bulk_translator(),
                'translator-api'  => $this->debug_translator_api(),
            ),
            'label'       => \pll__( 'Polylang AI Translation' ),
        );
    }

    /**
     * Get the debug data for the plugin.
     *
     * @return array<string,mixed>
     */
    private function debug_version(): array {
        return array(
            'label' => \pll__( 'Version' ),
            'value' => \PLLAT_PLUGIN_VERSION,
        );
    }

    /**
     * Get the debug data for the license key.
     *
     * @return array<string,mixed>
     */
    private function debug_license(): array {
        return array(
            'label'   => \pll__( 'License key' ),
            'value'   => $this->lc->get_license_key(),
            'private' => true,
        );
    }

    /**
     * Get the debug data for the bulk translator.
     *
     * @return array<string,mixed>
     */
    private function debug_bulk_translator(): array {
        $force_mode  = $this->bc->get_force_mode();
        $bulk_size   = $this->bc->get_bulk_size();
        $batch_size  = $this->bc->get_batch_size();
        $time_limit  = $this->bc->get_time_limit();
        $cron_runner = Constants::is_true( 'DISABLE_WP_CRON' );

        return array(
            'label' => \pll__( 'Bulk translator' ),
            'value' => array(
                \pll__( 'Forced mode' ) => $force_mode
                    ? \__( 'Enabled', 'default' )
                    : \__( 'Disabled', 'default' ),
                \pll__( 'Bulk size' )   => $bulk_size,
                \pll__( 'Batch size' )  => $batch_size,
                \pll__( 'Time limit' )  => $time_limit,
                \pll__( 'Scheduler' )   => $cron_runner
                    ? \pll__( 'External scheduler' )
                    : \pll__( 'Internal scheduler' ),
            ),
            'debug' => array(
                'forced-mode' => $force_mode ? 'true' : 'false',
                'bulk-size'   => $bulk_size,
                'batch-size'  => $batch_size,
                'time-limit'  => $time_limit,
                'scheduler'   => $cron_runner ? 'external' : 'internal',
            ),
        );
    }

    /**
     * Get the debug data for the translation API.
     *
     * @return array<string,mixed>
     */
    private function debug_translator_api(): array {
        return array(
            'label' => \pll__( 'Translation Settings' ),
            'value' => array(
                \pll__( 'Provider' )  => Settings::get_active_translation_api(),
                \pll__( 'LLM Model' ) => Settings::get_translation_model(),
            ),
            'debug' => array(
                'provider'  => Settings::get_active_translation_api(),
                'llm-model' => Settings::get_translation_model(),
            ),
        );
    }
}

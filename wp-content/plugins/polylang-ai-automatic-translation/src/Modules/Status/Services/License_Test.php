<?php //phpcs:disable Squiz.Commenting.FunctionComment.Missing

namespace PLLAT\Status\Services;

use PLLAT\Status\Enums\TestResult;
use PLLAT\Status\Enums\TestType;
use WP_Error;
use XWP\DI\Container;

/**
 * Test the license status.
 */
class License_Test extends Base_Health_Test {
    public const NAME = 'license';

    /**
     * Run the test
     *
     * @param  string|int    $activated Has the license been activated.
     * @param  bool|WP_Error $licensed  Is the license valid.
     * @return array<string,mixed>
     */
    public function __invoke( string|int $activated, bool|\WP_Error $licensed ): array {
        $result = array(
            'description' => \pll__( 'Your license is active and valid.' ),
            'label'       => \pll__( 'License is active' ),
            'status'      => TestResult::Pass,
        );

        if ( ! $activated || ! $licensed ) {
            $result = array(
                'actions'     => \sprintf(
                    '<a href="%s">%s</a>',
                    \admin_url( 'admin.php?page=polylang-ai-automatic-translation&tab=license' ),
                    \pll__( 'Activate now' ),
                ),
                'description' => \pll__(
                    'Your license is not activated. Please activate it to use the plugin.',
                ),
                'label'       => \pll__( 'License is not activated' ),
                'status'      => TestResult::Fail,
            );
        }

        return $this->format_result( $result );
    }

    public function get_type(): TestType {
        return TestType::Direct;
    }

    public function get_data( Container $ctr ): array {
        return array(
            'label' => \pll__( 'License activation' ),
            'test'  => $this->get_callback( $ctr ),
        );
    }

    public function get_params(): array {
        return array(
            'activated' => \DI\get( 'app.activated' ),
            'licensed'  => \DI\get( 'app.licensed' ),
        );
    }
}

<?php //phpcs:disable Squiz.Commenting.FunctionComment.Missing
/**
 * Base_Health_Test class file.
 *
 * @package    Polylang AI Automatic Translation
 * @subpackage Status
 */

namespace PLLAT\Status\Services;

use PLLAT\Status\Enums\TestResult;
use PLLAT\Status\Interfaces\Health_Test;
use XWP\DI\Container;

/**
 * Base definition for a health test.
 */
abstract class Base_Health_Test implements Health_Test {
    public static function define(): array {
        return array( static::NAME => \DI\get( static::class ) );
    }

    /**
     * Get the prefixed test ID.
     *
     * @return string
     */
    public function get_id(): string {
        return self::TEST_PFX . $this->get_name();
    }

    public function get_name(): string {
        return static::NAME;
    }

    protected function get_callback( Container $ctr ): callable {
        return fn() => $ctr->call( static::class, $this->get_params() );
    }

    /**
     * Get the rest endpoint for the test.
     *
     * @return string
     */
    protected function get_rest_ep(): string {
        return \rest_url( "pllat/v1/health-tests/{$this->get_name()}" );
    }

    /**
     * Format the result.
     *
     * @param  array{status: TestResult,description:string, actions?: string, label: string } $result The result to format.
     * @return array{
     *   description: string,
     *   actions?:    string,
     *   label:       string,
     *   status:      value-of<TestResult>,
     *   badge:       array{
     *     label: string,
     *     color: string
     *   }
     * }
     */
    protected function format_result( array $result ): array {
        $result['status']      = $result['status']->value;
        $result['test']        = $this->get_id();
        $result['badge']       = array(
            'color' => 'blue',
            'label' => \pll__( 'Polylang AI' ),
        );
        $result['description'] = \sprintf( '<p>%s</p>', $result['description'] );

        if ( isset( $result['actions'] ) ) {
            $result['actions'] = \sprintf( '<p>%s</p>', $result['actions'] );
        }

        return $result;
    }
}

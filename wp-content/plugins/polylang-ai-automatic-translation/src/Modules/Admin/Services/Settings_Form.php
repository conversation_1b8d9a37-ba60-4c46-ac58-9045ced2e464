<?php //phpcs:disable SlevomatCodingStandard.Functions.RequireMultiLineCall.RequiredMultiLineCall, SlevomatCodingStandard.Arrays.AlphabeticallySortedByKeys.IncorrectKeyOrder, SlevomatCodingStandard.Complexity.Cognitive.ComplexityTooHigh
/**
 * Settings_Form class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Admin\Services
 */

namespace PLLAT\Admin\Services;

use DI\Attribute\Inject;
use EPIC_WP\Polylang_Automatic_AI_Translation\Debug_Log;
use EPIC_WP\Polylang_Automatic_AI_Translation\Features;
use EPIC_WP\Polylang_Automatic_AI_Translation\Models\Translation_Models as Models;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use SC_License;

/**
 * Renders settings form and handles AJAX actions.
 */
class Settings_Form {
    /**
     * Constructor
     *
     * @param string     $app_path Application path.
     * @param SC_License $license License object.
     */
    public function __construct( #[Inject( 'app.path' )] private string $app_path, private SC_License $license ) {
    }

    /**
     * Registers admin settings fields
     */
    public function register_fields(): void {
        \add_settings_section(
            'pllat_translator_settings',
            \pll__( 'Translator settings' ),
            array( $this, 'render_translator_settings_section' ),
            'polylang-ai-automatic-translation',
        );

        // Register translator API fields.
        \add_settings_field(
            'pllat_translator_api',
            \pll__( 'Translator API' ),
            array( $this, 'render_translator_api_field' ),
            'polylang-ai-automatic-translation',
            'pllat_translator_settings',
        );

        \add_settings_field(
            'pllat_api_keys',
            '', // Empty label.
            array( $this, 'render_api_key_fields' ),
            'polylang-ai-automatic-translation',
            'pllat_translator_settings',
        );

        \add_settings_field(
            'pllat_website_ai_context',
            \pll__( 'AI Context' ),
            array( $this, 'render_website_ai_context_field' ),
            'polylang-ai-automatic-translation',
            'pllat_translator_settings',
        );

        \add_settings_field(
            'pllat_max_output_tokens',
            \pll__( 'Max output tokens' ),
            array( $this, 'render_max_output_tokens_field' ),
            'polylang-ai-automatic-translation',
            'pllat_translator_settings',
        );

        \add_settings_section(
            'pllat_support_settings',
            \pll__( 'Support settings' ),
            array( $this, 'render_support_settings_section' ),
            'polylang-ai-automatic-translation-support',
        );

        // Register debug mode checkbox field.
        \add_settings_field(
            'pllat_debug_mode',
            \pll__( 'Debug mode' ),
            array( $this, 'render_debug_mode_field' ),
            'polylang-ai-automatic-translation-support',
            'pllat_support_settings',
        );

        // Register feature settings section.
        \add_settings_section( 'pllat_feature_settings', \pll__( 'Features' ), array( $this, 'render_feature_settings_section' ), 'polylang-ai-automatic-translation-features' );

        // Register feature settings.
        \add_settings_field(
            'pllat_feature_url_replacement',
            \pll__( 'Internal URL replacer' ),
            array( $this, 'render_feature_url_replacement_field' ),
            'polylang-ai-automatic-translation-features',
            'pllat_feature_settings',
        );

        // Renders Gutenberg translatable keys field.
        \add_settings_field(
            'pllat_wp_block_translatable_keys',
            \pll__( 'Block Editor Settings to Translate' ),
            array( $this, 'render_gutenberg_translatable_keys_field' ),
            'polylang-ai-automatic-translation-features',
            'pllat_feature_settings',
        );

        // Only add Elementor field if Elementor is active.
        if ( Settings::is_elementor_active() ) {
            \add_settings_field(
                'pllat_elementor_translatable_keys',
                \pll__( 'Elementor Widget Fields to Translate' ),
                array( $this, 'render_elementor_translatable_keys_field' ),
                'polylang-ai-automatic-translation-features',
                'pllat_feature_settings',
            );

            \register_setting(
                'polylang-ai-automatic-translation-features',
                'pllat_elementor_translatable_keys',
                array( 'sanitize_callback' => \xwp_str_to_arr( ... ) ),
            );
        }

        // Register the actual settings to store the values.
        \register_setting(
            'polylang-ai-automatic-translation',
            'pllat_translator_api',
            array(
                'sanitize_callback' => static function ( $input ) {
                    $allowed_values = array( 'openai', 'openrouter' );
                    return \in_array( $input, $allowed_values, true ) ? $input : 'openai';
                },
            ),
        );
        \register_setting( 'polylang-ai-automatic-translation', 'pllat_openai_api_key' );
        \register_setting(
            'polylang-ai-automatic-translation',
            'pllat_openai_translation_model',
            array(
                'sanitize_callback' => static function ( $input ) {
                    $models = \array_keys( Models::get_openai_models() );
                    return \in_array( $input, $models, true ) ? $input : 'gpt-4o';
                },
            ),
        );
        \register_setting( 'polylang-ai-automatic-translation', 'pllat_openrouter_api_key' );
        \register_setting(
            'polylang-ai-automatic-translation',
            'pllat_openrouter_translation_model',
            array(
                'sanitize_callback' => static function ( $input ) {
                    $models = \array_merge(
                        \array_keys( Models::get_openrouter_models()['free'] ),
                        \array_keys( Models::get_openrouter_models()['paid'] ),
                    );
                    return \in_array( $input, $models, true ) ? $input : 'openai/gpt-4o-2024-11-20';
                },
            ),
        );
        \register_setting( 'polylang-ai-automatic-translation', 'pllat_website_ai_context' );
        \register_setting( 'polylang-ai-automatic-translation', 'pllat_max_output_tokens' );

        // Register support settings in the new group.
        \register_setting( 'polylang-ai-automatic-translation-support', 'pllat_debug_mode' );

        // Register feature settings in the new group.
        \register_setting( 'polylang-ai-automatic-translation-features', 'pllat_feature_url_replacement' );
        \register_setting(
            'polylang-ai-automatic-translation-features',
            'pllat_wp_block_translatable_keys',
            array(
                'sanitize_callback' => static function ( $input ) {
                    if ( ! $input ) {
                        return array();
                    }
                    if ( \is_array( $input ) ) {
                        return \array_filter( \array_map( 'trim', $input ) );
                    }
                    if ( \is_string( $input ) ) {
                        $keys = \array_map( 'trim', \explode( ',', $input ) );
                        return \array_filter( $keys );
                    }
                    return array();
                },
            ),
        );
    }

    /**
     * Renders settings page
     */
    public function render(): void {
        $tab = \xwp_fetch_get_var( 'tab', 'settings' );

        \xwp_get_template(
            $this->app_path . 'templates/admin/settings-page.php',
            array(
                'tabs'    => $this->get_tabs(),
                'current' => $tab,
                'section' => 'settings' === $tab ? '' : $tab,
            ),
        );
    }

    /**
     * Admin menu settings page section
     */
    public function render_translator_settings_section(): void {
        echo '<p>' . \pll_esc_html__( 'Configure your AI settings below.' ) . '</p>';
    }

    /**
     * Renders feature settings section
     */
    public function render_feature_settings_section(): void {
        echo '<p>' . \pll_esc_html__( 'Enable or disable features below:' ) . '</p>';
    }

    /**
     * Renders translator API field
     */
    public function render_translator_api_field(): void {
        $selected_api = Settings::get_active_translation_api();
        require $this->app_path . 'templates/admin/fields/translator-api.php';
    }

    /**
     * Renders API key fields
     */
    public function render_api_key_fields(): void {
        $active_api         = Settings::get_active_translation_api();
        $openai_api_key     = Settings::get_translation_api_key( 'openai' );
        $openrouter_api_key = Settings::get_translation_api_key( 'openrouter' );
        $openai_models      = array();
        $openrouter_models  = array();

        switch ( $active_api ) {
            case 'openai':
                $openai_models = Models::get_openai_models();
                break;
            case 'openrouter':
                $openrouter_models = Models::get_openrouter_models();
                break;
        }

        $selected_model = Settings::get_translation_model( $active_api );
        require $this->app_path . 'templates/admin/fields/api-key-fields.php';
    }

    /**
     * Renders max output tokens field
     */
    public function render_max_output_tokens_field(): void {
        $value = Settings::get_max_output_tokens();
        // require $this->app_path . 'templates/admin/fields/max-output-tokens.php';
        \xwp_get_template(
            $this->app_path . 'templates/admin/fields/max-output-tokens.php',
            array(
                'value' => $value,
            ),
        );
    }

    /**
     * Renders website AI context field
     */
    public function render_website_ai_context_field(): void {
        $value = \get_option( 'pllat_website_ai_context' );
        require $this->app_path . 'templates/admin/fields/website-ai-context.php';
    }

    /**
     * Renders debug mode checkbox field
     */
    public function render_debug_mode_field(): void {
        $value = \get_option( 'pllat_debug_mode' );
        require $this->app_path . 'templates/admin/fields/debug-mode.php';
    }

    /**
     * Renders URL replacement checkbox field
     */
    public function render_feature_url_replacement_field(): void {
        $value = Features::is_url_replacement_enabled();
        require $this->app_path . 'templates/admin/fields/url-replacement.php';
    }

    /**
     * Renders Gutenberg translatable keys field
     */
    public function render_gutenberg_translatable_keys_field(): void {
        $keys  = Settings::get_wp_block_translatable_keys();
        $value = \implode( ', ', $keys );
        require $this->app_path . 'templates/admin/fields/gutenberg-translatable-keys.php';
    }

    /**
     * Renders support settings section, passing log data to the template.
     */
    public function render_support_settings_section(): void {
        $log_exists  = Debug_Log::log_exists();
        $log_content = $log_exists ? Debug_Log::read() : '';
        $log_size    = $log_exists ? Debug_Log::get_log_size() : null;

        if ( $log_exists && '' === $log_content && 0 === $log_size ) {
            $log_content = \pll__( '(Debug log file is empty)' );
        }

        // Pass data to the template
        \xwp_get_template(
            $this->app_path . 'templates/admin/fields/support-settings.php',
            array(
                'log_exists'  => $log_exists,
                'log_content' => $log_content,
                'log_size'    => $log_size,
            ),
        );
    }

    /**
     * Renders Elementor translatable keys field
     */
    public function render_elementor_translatable_keys_field(): void {
        $keys  = Settings::get_elementor_translatable_keys();
        $value = \implode( ', ', $keys );
        require $this->app_path . 'templates/admin/fields/elementor-translatable-keys.php';
    }

    /**
     * Get admin tabs
     *
     * @return array<string,string>
     */
    protected function get_tabs(): array {
        $tabs = array(
            'settings' => \__( 'Settings', 'polylang-ai-autotranslate' ),
            'features' => \__( 'Features', 'polylang-ai-autotranslate' ),
            'support'  => \__( 'Support', 'polylang-ai-autotranslate' ),
            'license'  => \__( 'License', 'polylang-ai-autotranslate' ),
        );

        return ! $this->license->is_activated()
            ? \xwp_array_slice_assoc( $tabs, 'license' )
            : $tabs;
    }
}

<?php //phpcs:disable SlevomatCodingStandard.Functions.RequireMultiLineCall.RequiredMultiLineCall
/**
 * Settings_Ajax_Handler class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Admin
 */

namespace PLLAT\Admin\Handlers;

use EPIC_WP\Polylang_Automatic_AI_Translation\Models\Translation_Models as Models;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use XWP\DI\Decorators\Ajax_Action;
use XWP\DI\Decorators\Ajax_Handler;

/**
 * Handles ajax calls for settings page
 */
#[Ajax_Handler( prefix: 'pllat' )]
class Settings_Ajax_Handler {
    /**
     * Guard against unauthorized access
     */
    public function nonce_guard(): void {
        \wp_send_json_error( array( 'message' => 'Security check failed' ) );
    }

    /**
     * Guard against unauthorized access
     */
    public function cap_guard(): void {
        \wp_send_json_error( array( 'message' => 'Permission denied' ) );
    }

    /**
     * AJAX handler to fetch translation models for a specific API
     *
     * @param string $api Translator API.
     */
    #[Ajax_Action(
        action: 'get_translation_api_models',
        public: false,
        method: Ajax_Action::AJAX_POST,
        nonce: array( 'nonce' => 'pllat_admin_nonce' ),
        cap: 'manage_options',
        vars: array( 'translator_api' => '' ),
    )]
    public function get_api_models( string $api ): void {
        if ( ! \in_array( $api, array( 'openai', 'openrouter' ), true ) ) {
            \wp_send_json_error( array( 'message' => 'Invalid API selection' ) );
        }

        $selected = Settings::get_translation_model( $api );
        $options  = match ( $api ) {
            'openai'     => $this->model_options_html( Models::get_openai_models(), $selected ),
            'openrouter' => $this->openrouter_model_options_html( Models::get_openrouter_models(), $selected ),
        };

        \wp_send_json_success(
            array(
                'description' => Models::get_model_description( $api ),
                'html'        => $options,
            ),
        );
    }

    /**
     * AJAX handler for saving translator API selection
     *
     * @param string $api_key API key.
     * @param string $model   Translation model.
     * @param string $api     Translator API.
     */
    #[Ajax_Action(
        action: 'save_translator_api_settings',
        public: false,
        method: Ajax_Action::AJAX_POST,
        nonce: array( 'nonce' => 'pllat_admin_nonce' ),
        cap: 'manage_options',
        vars: array(
            'api_key'        => '',
            'model'          => '',
            'translator_api' => '',
        ),
    )]
    public function save_translator_settings( string $api_key, string $model, string $api ): void {
        // Validate API selection.
        if ( ! \in_array( $api, array( 'openai', 'openrouter' ), true ) ) {
            \wp_send_json_error( array( 'message' => 'Invalid API selection' ) );
        }

        \update_option( 'pllat_translator_api', $api );
        \update_option( 'pllat_' . $api . '_api_key', $api_key );
        \update_option( 'pllat_' . $api . '_translation_model', $model );

        // Return success message.
        \wp_send_json_success(
            array(
                'api'     => $api,
                'api_key' => $api_key,
                'message' => \pll__( 'Translator API settings saved successfully' ),
                'model'   => $model,
            ),
        );
    }

    /**
     * Generate HTML for OpenRouter model options with optgroups
     *
     * @param  array<string,mixed> $or_models OpenRouter models.
     * @param  string              $sel_model    Selected model.
     * @return string
     */
    private function openrouter_model_options_html( array $or_models, string $sel_model ): string {
        if ( ! $or_models ) {
            return '<option value="">' . \pll__( 'No models available' ) . '</option>';
        }

        return '' .
            $this->model_options_html( $or_models['free'], $sel_model, \pll__( 'Free Models' ), ) .
            $this->model_options_html( $or_models['paid'], $sel_model, \pll__( 'Paid Models' ), );
    }

    /**
     * Generate HTML for model options
     *
     * @param  array<string,array<string,mixed>> $models   Models.
     * @param  string                            $selected Selected model.
     * @param  string                            $group Optgroup label.
     * @return string
     */
    private function model_options_html( array $models, string $selected, string $group = '' ): string {
        if ( ! $models && ! $group ) {
            return '<option value="">' . \pll__( 'No models available' ) . '</option>';
        }

        $html = $group ? \sprintf( '<optgroup label="%s">', \esc_attr( $group ) ) : '';

        foreach ( $models as $model => $config ) {
            $html .= \sprintf(
                '<option value="%s" %s>%s</option>',
                \esc_attr( $model ),
                \selected( $selected, $model, false ),
                \esc_html( $config['label'] ),
            );
        }

        $html .= $group ? '</optgroup>' : '';

        return $html;
    }
}

<?php
/**
 * Admin_Asset_Handler class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Admin
 */

namespace PLLAT\Admin\Handlers;

use DI\Attribute\Inject;
use WP_Screen;
use XWP\Dependency\Resources\Script;
use XWP\DI\Decorators\Action;
use XWP\DI\Decorators\Filter;
use XWP\DI\Decorators\Handler;
use XWP_Asset_Bundle;

/**
 * Handles admin assets
 */
#[Handler( tag: 'init', priority: 11, context: Handler::CTX_ADMIN )]
class Admin_Asset_Handler {
    /**
     * Current screen
     *
     * @var WP_Screen
     */
    private WP_Screen $screen;
    /**
     * Admin pages
     *
     * @var array<string,string|array{0: string, 1: string}> $pages
     */
    private array $pages = array(
        'languages_page_polylang-ai-automatic-translation' => '',
        'languages_page_polylang-ai-translate-bulk'        => '',
        'nav-menus'                                        => '',
        'post'                                             => '',
        'term'                                             => '',
    );

    /**
     * Constructor
     *
     * @param XWP_Asset_Bundle $bundle Asset bundle.
     */
    public function __construct( XWP_Asset_Bundle $bundle ) {
        $bundle->load();
    }

    /**
     * Check if the current screen is valid for loading assets
     *
     * @return bool
     */
    #[Filter( tag: 'pllat_can_enqueue_script', priority: 10 )]
    #[Filter( tag: 'pllat_can_enqueue_style', priority: 10 )]
    public function is_asset_enqueueable(): bool {
        return $this->is_valid_screen( $this->get_screen() );
    }

    /**
     * Localize admin script
     *
     * @return array<string,mixed>
     */
    #[Filter( tag: 'localize_script_args_pllat-admin', priority: 10 )]
    public function localize_admin_script(): array {
        return array(
            'l10n'        => array(
                'ajax_url' => \admin_url( 'admin-ajax.php' ),
                'nonce'    => \wp_create_nonce( 'pllat_admin_nonce' ),
            ),
            'object_name' => 'PLLAT_Admin',
        );
    }

    /**
     * Check if the current screen is valid
     *
     * @param  WP_Screen $screen Screen object.
     * @return bool
     */
    private function is_valid_screen( WP_Screen $screen ): bool {
        $page = $this->pages[ $screen->base ] ?? false;

        if ( false === $page ) {
            return false;
        }

        if ( '' === $page ) {
            return true;
        }

        [ $arg, $val ] = $page;
        $req           = \xwp_fetch_req_var( $arg, '' );

        return '' !== $req && ( '*' === $val || $req === $val );
    }

    /**
     * Get the current screen
     *
     * @return WP_Screen
     */
    private function get_screen(): WP_Screen {
        return $this->screen ??= \get_current_screen();
    }
}

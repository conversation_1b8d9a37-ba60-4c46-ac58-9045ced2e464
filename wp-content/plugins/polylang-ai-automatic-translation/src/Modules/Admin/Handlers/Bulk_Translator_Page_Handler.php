<?php //phpcs:disable WordPress.WP.EnqueuedResourceParameters.NotInFooter
/**
 * Bulk_Translator_Page_Handler class file.
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Admin
 */

namespace PLLAT\Admin\Handlers;

use PLLAT\Translator\Services\Bulk_Service;
use XWP\DI\Decorators\Action;
use XWP\DI\Decorators\Handler;

/**
 * Handles display and functionality of the bulk translation page in the admin area.
 */
#[Handler( tag: 'init', priority: 20, context: Handler::CTX_ADMIN )]
class Bulk_Translator_Page_Handler {
    /**
     * Register the bulk translation page in the admin menu.
     *
     * @param  string $base_path The base path for the plugin.
     */
    #[Action(
        tag: 'admin_menu',
        priority: 11,
        context: Action::CTX_ADMIN,
        invoke: Action::INV_PROXIED,
        args:0,
        params: array( 'app.path' ),
    )]
    public function add_menu( string $base_path ): void {
        \add_submenu_page(
            parent_slug:'mlang',
            page_title: \pll__( 'AI Bulk Translate' ),
            menu_title: \pll__( 'AI Bulk Translate' ),
            capability:'manage_options',
            menu_slug: 'polylang-ai-translate-bulk',
            callback: static function () use ( $base_path ) {
                \xwp_get_template( $base_path . '/templates/admin/bulk-translation-page.php' );
            },
        );
    }

    /**
     * Enqueue scripts and styles for the bulk translation page.
     *
     * @param string       $hook     Current admin page hook.
     * @param Bulk_Service $svc      Service instance for bulk translation.
     * @param string       $base_url Plugin base URL.
     * @param string       $version  Plugin version.
     */
    #[Action(
        tag: 'admin_enqueue_scripts',
        priority: 10,
        context: Action::CTX_ADMIN,
        invoke: Action::INV_PROXIED,
        args: 1,
        params: array( Bulk_Service::class, 'app.url', 'app.ver' ),
    )]
    public function enqueue_scripts( string $hook, Bulk_Service $svc, string $base_url, string $version ): void {
        if ( 'languages_page_polylang-ai-translate-bulk' !== $hook ) {
            return;
        }

        \wp_enqueue_script(
            handle:'pllat-bulk-translation',
            src: "{$base_url}/dist/js/build/bulk-translator.js",
            deps: array( 'wp-element', 'wp-components', 'wp-api-fetch' ),
            ver: $version,
            args: array( 'in_footer' => true ),
        );

        \wp_localize_script(
            'pllat-bulk-translation',
            'pllat',
            array(
                'ajaxUrl'    => \admin_url( 'admin-ajax.php' ),
                'canLog'     => $svc->can_log(),
                'languages'  => $svc->get_available_languages(),
                'plugin_url' => $base_url,
                'strings'    => array(
                    'deselectAll' => \pll__( 'Deselect all' ),
                    'loadingText' => \pll__( 'One moment..' ),
                    'selectAll'   => \pll__( 'Select all' ),
                    'settings'    => array(
                        'additionalInstructions' => \pll__( 'Additional Instructions' ),
                        'bulkSize'               => \pll__( 'Bulk Size' ),
                        'forceMode'              => \pll__( 'Enable Force Mode' ),
                        'forceModeHelp'          => \pll__(
                            'Force mode will generate translations again for all fields, regardless of already existing translations for the selected languages.',
                        ),
                        'save'                   => \pll__( 'Save' ),
                    ),
                ),
            ),
        );
    }
}

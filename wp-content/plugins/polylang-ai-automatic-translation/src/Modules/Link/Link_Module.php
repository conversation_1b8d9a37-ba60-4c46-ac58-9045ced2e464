<?php

namespace PLLAT\Link;

use EPIC_WP\Polylang_Automatic_AI_Translation\Features;
use XWP\DI\Decorators\Module;

#[Module(
    hook: 'init',
    priority: 19,
    context: Module::CTX_FRONTEND | Module::CTX_REST,
    handlers: array(
        Handlers\Link_Replacement_Handler::class,
    ),
    services: array(
        Services\Content_Resolver::class,
        Services\HTML_Processor::class,
        Services\Link_Replacer::class,
        Services\Link_Translation_Service::class,
        Services\URL_Parser::class,
    ),
)]
class Link_Module {
    /**
     * Can we initialize the module.
     *
     * @return bool
     */
    public static function can_initialize(): bool {
        // Check if URL replacement is enabled.
        $enabled = Features::is_url_replacement_enabled();
        return $enabled ?? false;
    }

    /**
     * Get the module definition.
     *
     * @return array<string,mixed>
     */
    public static function configure(): array {
        return array();
    }
}

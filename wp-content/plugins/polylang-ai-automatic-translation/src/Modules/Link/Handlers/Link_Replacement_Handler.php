<?php

namespace PLLAT\Link\Handlers;

use PLLAT\Link\Services\Link_Replacer;
use XWP\DI\Decorators\Filter;
use XWP\DI\Decorators\Handler;

#[Handler( tag: 'init', priority: 20, context: Handler::CTX_FRONTEND | Handler::CTX_REST )]
class Link_Replacement_Handler {
    /**
     * Can we initialize the handler.
     *
     * We can only initialize this handler if <PERSON><PERSON><PERSON> is active and the current language is set.
     *
     * @return bool
     */
    public static function can_initialize(): bool {
        return '' !== (string) \pll_current_language();
    }

    /**
     * Constructor.
     *
     * @param  Link_Replacer $replacer The service responsible for replacing links in content.
     */
    public function __construct( private readonly Link_Replacer $replacer ) {
    }

    #[Filter( tag: 'elementor/frontend/the_content', priority: 'PHP_INT_MAX' )]
    #[Filter( tag: 'wp_nav_menu_items', priority: 'PHP_INT_MAX' )]
    #[Filter( tag: 'the_content', priority: 'PHP_INT_MAX' )]
    public function replace_urls( string $content ): string {
        return $this->replacer->replace_urls( $content );
    }
}

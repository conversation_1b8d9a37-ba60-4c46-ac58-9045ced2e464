<?php //phpcs:disable WordPress.Arrays.ArrayDeclarationSpacing.AssociativeArrayFound
/**
 * Compatibility functions and helpers
 *
 * @package Polylang AI Automatic Translation
 * @subpackage Common
 */

/**
 * Check if Polylang is deactivating
 *
 * @return bool
 */
function pllat_is_pll_deactivating(): bool {
    $pll = array( 'polylang/polylang.php', 'polylang-pro/polylang.php' );
    $def = array( 'action' => '', 'plugin' => '' );
    $get = xwp_get_arr( $def );

    // @phpstan-ignore-next-line
    return 'deactivate' === $get['action'] && in_array( $get['plugin'], $pll, true );
}

/**
 * Is WooCommerce active?
 *
 * @return bool
 */
function pllat_has_wc(): bool {
    return did_action( 'woocommerce_loaded' ) || function_exists( 'WC' );
}

/**
 * Is Elementor active?
 *
 * @return bool
 */
function pllat_has_elementor(): bool {
    return did_action( 'elementor/loaded' ) || function_exists( 'Elementor\Plugin' );
}

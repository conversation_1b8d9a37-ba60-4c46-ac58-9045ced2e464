<?php
declare(strict_types=1);

namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post;
use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post_Queue_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Translatable_Term;
use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Translatable_Term_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Translatable_Term_Queue_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator_Factory;
use Exception;

class Helpers {
    /**
     * Get the active Polylang post types
     */
    public static function get_active_post_types(): array {
        $post_types = \array_filter(
            \get_post_types(),
            static fn( $post_type ) => \pll_is_translated_post_type( $post_type ),
        );
        return \array_values( $post_types );
    }

    public static function get_available_post_types(): array {
        $exclude_post_types = [
            'wp_block',
            'wp_template_part',
            'wp_navigation',
            'shop_order_placehold',
            'shop_order'
        ];
        $post_types = array_filter(self::get_active_post_types(), function($post_type) use ($exclude_post_types) {
            return !in_array($post_type, $exclude_post_types);
        });
        return \array_values( $post_types );
    }

    /**
     * Get the active Polylang taxonomies
     */
    public static function get_activate_taxonomies(): array {
        $taxonomies = \array_filter(
            \get_taxonomies(),
            static fn( $taxonomy ) => \pll_is_translated_taxonomy( $taxonomy ),
        );
        return \array_values( $taxonomies );
    }

    /**
     * Get the available Polylang taxonomies that are not excluded
     */ 
    public static function get_available_taxonomies(): array {
        $exclude_taxonomies = [
        ];
        $taxonomies = array_filter(self::get_activate_taxonomies(), function($taxonomy) use ($exclude_taxonomies) {
            return !in_array($taxonomy, $exclude_taxonomies);
        });
        return \array_values( $taxonomies );
    }

    /**
     * Get all meta data as a flatten array
     */
    public static function get_flatten_post_meta( int $post_id ): array {
        $post_meta = \get_post_meta( $post_id );
        if ( ! $post_meta ) { // phpcs:ignore SlevomatCodingStandard.ControlStructures.DisallowEmpty.DisallowedEmpty
            return array();
        }
        // @phpstan-ignore-next-line - array_map preserves keys if the input array keys are strings.
        return \array_map(
            static fn( $row ) => $row[0] ?? null,
            $post_meta,
        );
    }

    /**
     * Get all meta data as a flatten array
     */
    public static function get_flatten_term_meta( int $term_id ): array {
        $term_meta = \get_term_meta( $term_id );
        if ( ! $term_meta ) { // phpcs:ignore SlevomatCodingStandard.ControlStructures.DisallowEmpty.DisallowedEmpty
            return array();
        }
        // @phpstan-ignore-next-line - array_map preserves keys if the input array keys are strings.
        return \array_map(
            static fn( $row ) => $row[0] ?? null,
            $term_meta,
        );
    }

    /**
     * Helper function to find the changed fields between two arrays
     */
    public static function get_changed_fields( array $before_array, array $after_array, array $available_fields ): array {
        $changes = array();
        foreach ( $available_fields as $field ) {
            $before_value = $before_array[ $field ] ?? null;
            $after_value  = $after_array[ $field ] ?? null;

            if ( $before_value === $after_value ) {
                continue;
            }

            $changes[] = $field;
        }
        return $changes;
    }

    /**
     * Adds indexes from starting from 1
     */
    public static function number_strings_array( array $strings ): array {
        if ( array() === $strings ) {
            return array();
        }
        // @phpstan-ignore-next-line - array_combine can return false but not here.
        return \array_combine( \range( 1, \count( $strings ) ), $strings );
    }

    /**
     * Encodes an array to json
     */
    public static function encode_json( $data, int $options = \JSON_UNESCAPED_UNICODE ): string {
        $json = \wp_json_encode( $data, $options );
        if ( false === $json ) {
            // It's okay to not escape json_last_error_msg in an exception message.
            // phpcs:ignore WordPress.Security.EscapeOutput.ExceptionNotEscaped
            throw new \Exception( 'Error encoding JSON: ' . \json_last_error_msg() );
        }
        return $json;
    }

    /**
     * Decodes a json string to an array
     */
    public static function decode_json( string $json, bool $associative = true ): array {
        $data = \json_decode( $json, $associative );
        if ( null === $data && JSON_ERROR_NONE !== \json_last_error() ) {
            // It's okay to not escape json_last_error_msg in an exception message.
            // phpcs:ignore WordPress.Security.EscapeOutput.ExceptionNotEscaped
            throw new \Exception( 'Error decoding JSON: ' . \json_last_error_msg() );
        }
        // If $data is null after decoding (e.g., decoding the string "null"), return empty array.
        return (array) ( $data ?? array() );
    }

    /**
     * Logs messages in the main log file of the plugin
     */
    public static function log( string $message, string $type = 'info', array $context = array() ): void {
        Debug_Log::write( $message, $type, $context );

        if ( 'error' !== $type ) {
            return;
        }

        \error_log( 'PLLAT Plugin Error: ' . $message );
    }

    public static function is_elementor_active(): bool {
        return \did_action( 'elementor/loaded' ) > 0;
    }

    public static function is_woocommerce_active(): bool {
        $active_plugins = \apply_filters( 'active_plugins', \get_option( 'active_plugins' ) );
        return \in_array( 'woocommerce/woocommerce.php', $active_plugins, true );
    }

    public static function is_polylang_active(): bool {
        $active_plugins = \apply_filters( 'active_plugins', \get_option( 'active_plugins' ) );
        return \in_array( 'polylang/polylang.php', $active_plugins, true ) || \in_array(
            'polylang-pro/polylang.php',
            $active_plugins,
            true,
        );
    }

    public static function get_translatable_post_manager( int $post_id ): Translatable_Post_Manager {
        /** @var Language_Manager|null $language_manager */
        $language_manager = \xwp_app( 'pllat' )->get( Language_Manager::class );
        if ( ! $language_manager instanceof Language_Manager ) {
            throw new \Exception( 'Could not resolve Language_Manager service.' );
        }

        $translator        = Translator_Factory::create_translator( Settings::get_active_translation_api() );
        $translatable_post = new Translatable_Post( $post_id, $language_manager );
        $queue_manager     = new Translatable_Post_Queue_Manager( $translatable_post, $language_manager );
        $post_manager      = new Translatable_Post_Manager(
            $translatable_post,
            $queue_manager,
            $translator,
            $language_manager,
        );
        return $post_manager;
    }

    public static function get_translatable_term_manager( int $term_id ): Translatable_Term_Manager {
        /** @var Language_Manager|null $language_manager */
        $language_manager = \xwp_app( 'pllat' )->get( Language_Manager::class );
        if ( ! $language_manager instanceof Language_Manager ) {
            throw new \Exception( 'Could not resolve Language_Manager service.' );
        }

        $translator        = Translator_Factory::create_translator( Settings::get_active_translation_api() );
        $translatable_term = new Translatable_Term( $term_id, $language_manager );
        $queue_manager     = new Translatable_Term_Queue_Manager( $translatable_term, $language_manager );
        $term_manager      = new Translatable_Term_Manager(
            $translatable_term,
            $queue_manager,
            $translator,
            $language_manager,
        );
        return $term_manager;
    }
}

<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Models;

class Translation_Models {
    /**
     * Get OpenAI models configuration
     *
     * @return array
     */
    public static function get_openai_models(): array {
        return array(
            'gpt-4.1'      => array(
                'label' => 'GPT-4.1',
            ),
            'gpt-4.1-mini' => array(
                'label' => 'GPT-4.1 Mini',
            ),
            'gpt-4o'       => array(
                'label' => 'GPT-4o (most used)',
            ),
            'gpt-4o-mini'  => array(
                'label' => 'GPT-4o Mini',
            ),
        );
    }

    /**
     * Get OpenRouter models configuration
     *
     * @return array
     */
    public static function get_openrouter_models(): array {
        return array(
            'free' => array(
                'google/gemini-2.0-flash-exp:free'     => array(
                    'label' => \pll__( 'Gemini Flash (Google)' ),
                ),
                'google/gemini-2.0-flash-lite-preview-02-05:free' => array(
                    'label' => \pll__( 'Gemini Flash Lite (Google)' ),
                ),
                'google/gemini-2.0-pro-exp-02-05:free' => array(
                    'label' => \pll__( 'Gemini Pro (Google)' ),
                ),
            ),
            'paid' => array(
                'anthropic/claude-3.5-sonnet'    => array(
                    'label' => \pll__( 'Claude 3.5 Sonnet (Anthropic)' ),
                ),
                'anthropic/claude-3.7-sonnet'    => array(
                    'label' => \pll__( 'Claude 3.7 Sonnet (Anthropic)' ),
                ),
                'deepseek/deepseek-chat-v3-0324' => array(
                    'label' => \pll__( 'DeepSeek V3 0324 (DeepSeek)' ),
                ),
                'openai/gpt-4o-2024-11-20'       => array(
                    'label' => \pll__( 'GPT-4o (OpenAI)' ),
                ),
                'openai/gpt-4o-mini'             => array(
                    'label' => \pll__( 'GPT-4o Mini (OpenAI)' ),
                ),
            ),
        );
    }

    /**
     * Get cost tooltip for a specific API
     *
     * @param string $api_type
     * @return string
     */
    public static function get_tooltip( string $api_type ): string {
        $tooltip = '';
        switch ( $api_type ) {
            case 'openai':
                $tooltip = \pll__(
                    'Direct access to OpenAI API is on average cheaper than using OpenRouter, but you are limited by your account\'s rate limits. Read more here: https://platform.openai.com/docs/usage-policies/rate-limits',
                );
                break;

            case 'openrouter':
                $tooltip = \pll__(
                    'The paid AI models of OpenRouter are on average more expensive, but provide access to most of the models available on the market.',
                );
                break;
        }

        return $tooltip;
    }

    /**
     * Get model description for a specific API
     *
     * @param string $api_type
     * @return string
     */
    public static function get_model_description( string $api_type ): string {
        switch ( $api_type ) {
            case 'openai':
                return \pll__(
                    'Currently we only support GPT-4o as it provides the best balance of cost, speed and translation quality compared to all other available models.',
                );
            case 'openrouter':
                return \pll__(
                    'OpenRouter is a unified API that gives you access to multiple AI models from different providers. You can choose between free and paid models - paid models like Claude 3.5 Sonnet provide excellent translation quality but have associated costs. Openrouter is <strong><u>more expensive</u></strong> because the platform adds an additional fee to the cost of the model.',
                );
            default:
                return '';
        }
    }
}

<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Action_Scheduler;

use ActionScheduler_Store;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;

/**
 * Abstract class for scheduling translation actions
 */
abstract class Abstract_Single_Translation_Background_Processor {

    const TRANSIENT_ERROR_EXPIRATION = 60 * 60 * 24;
    const TRANSIENT_LATEST_ERROR_KEY_PREFIX = 'pllat_as_single_translation_error_';
    const TRANSIENT_TIMESTAMP_KEY_PREFIX = 'pllat_as_single_translation_timestamp_';
    const TRANSIENT_TIMESTAMP_EXPIRATION = 60 * 60 * 24; // 24 hours
    
    /**
     * Action group name
     * 
     * @var string
     */
    protected string $group;
    
    /**
     * Process hook name
     * 
     * @var string
     */
    protected string $process_hook;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->register_hooks();
    }

    /**
     * Get the item type (post or term)
     * 
     * @return string The item type
     */
    abstract protected function get_item_type(): string;
    
    /**
     * Register action hooks
     * 
     * @return void
     */
    protected function register_hooks(): void {
        add_action($this->process_hook, [$this, 'handle_translation'], 10, 3);
    }
    
    /**
     * Schedule a translation
     * 
     * @param int $item_id The item ID to translate (post or term)
     * @param string $language_slug The language to translate to
     * @param array $args Additional arguments for translation
     * @return int|bool The scheduled action ID or false
     */
    public function schedule_translation(int $item_id, string $language_slug, array $args = []) {
        $action_args = $this->prepare_action_args($item_id, $language_slug, $args);
        
        $action_id = \as_enqueue_async_action(
            $this->process_hook,
            $action_args,
            $this->group
        );
        
        return $action_id;
    }
    
    /**
     * Prepare arguments for the action
     * 
     * @param int $item_id The item ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return array Prepared arguments
     */
    abstract protected function prepare_action_args(int $item_id, string $language_slug, array $args): array;
    
    /**
     * Get the item ID key name for this scheduler (e.g., 'post_id' or 'term_id')
     * 
     * @return string The item ID key name
     */
    abstract protected function get_item_id_key(): string;
    
    /**
     * Process the item using appropriate manager
     * 
     * @param int $item_id The item ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return void
     */
    abstract protected function process_item(int $item_id, string $language_slug, array $args): void;
    
    /**
     * Handle a scheduled translation
     * 
     * @param int $item_id The item ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return void
     */
    public function handle_translation(int $item_id, string $language_slug, array $args = []): void {
        try {
            $this->store_translation_timestamp($item_id, $language_slug);                        
            $this->process_item($item_id, $language_slug, $args);            
        } catch (\Throwable $e) {
            $this->log_error($item_id, $language_slug, $args, $e);
        }
    }
        
    /**
     * Clear all scheduled translation actions
     * 
     * @return void
     */
    public function clear_scheduled_actions(): void {
        \as_unschedule_all_actions($this->process_hook, [], $this->group);
    }
    
    /**
     * Get pending translation actions for a specific item
     * 
     * @param int $item_id The item ID
     * @return array Array of pending actions
     */
    public function get_pending_translation_actions(int $item_id): array {

        $args = [
            'status' => [ActionScheduler_Store::STATUS_PENDING, ActionScheduler_Store::STATUS_RUNNING],
            'group' => $this->group,
            'hook' => $this->process_hook,
        ];
        
        $actions = as_get_scheduled_actions($args);
        $item_key = $this->get_item_id_key();
        
        // Filter actions to only include those for this item
        return array_filter($actions, function($action) use ($item_id, $item_key) {
            $args = $action->get_args();
            return isset($args[$item_key]) && $args[$item_key] == $item_id;
        });
    }
    
    /**
     * Get the translation status for an item
     * 
     * @param int $item_id The item ID
     * @return array Status information with keys 'status', 'pending_languages', 'processing_languages', 'message'
     */
    public function get_translation_status(int $item_id): array {

        $status = [
            'status' => 'completed',
            'pending_languages' => [],
            'processing_languages' => [],
            'processing_timestamps' => [],
            'errors' => [],
        ];

        // Check for pending actions
        $pending_actions = as_get_scheduled_actions([
            'status'    => ActionScheduler_Store::STATUS_PENDING,
            'group'     => $this->group,
            'hook'      => $this->process_hook,
        ]);
        
        // Check for currently running actions
        $running_actions = as_get_scheduled_actions([
            'status'    => ActionScheduler_Store::STATUS_RUNNING,
            'group'     => $this->group,
            'hook'      => $this->process_hook,
        ]);
                
        $item_key = $this->get_item_id_key();
        
        // Filter actions to only include those for this item
        $pending_actions = array_filter($pending_actions, function($action) use ($item_id, $item_key) {
            $args = $action->get_args();
            return isset($args[$item_key]) && $args[$item_key] == $item_id;
        });
        $running_actions = array_filter($running_actions, function($action) use ($item_id, $item_key) {
            $args = $action->get_args();
            return isset($args[$item_key]) && $args[$item_key] == $item_id;
        });

        // Get the latest error for this item
        $latest_error = $this->get_latest_error($item_id);
        $status['errors'] = $latest_error ? [$latest_error] : [];
                
        // If no actions are found and no errors, all translations are completed
        if (empty($pending_actions) && empty($running_actions) && empty($status['errors'])) {
            return $status;
        }
        
        // Get the language slugs from all pending actions
        foreach ($pending_actions as $action) {
            $args = $action->get_args();
            if (isset($args['language_slug'])) {
                $status['pending_languages'][] = $args['language_slug'];
            }
        }
        
        // Get the language slugs and starting timestamps from all running actions
        foreach ($running_actions as $action) {
            $args = $action->get_args();
            if (!empty($args['language_slug'])) {
                $lang = $args['language_slug'];
                $status['processing_languages'][] = $lang;
                
                // Get the actual timestamp or default to 0 if not available
                $timestamp = $this->get_translation_timestamp($item_id, $lang);
                $status['processing_timestamps'][$lang] = $timestamp !== false ? $timestamp : 0;
            }
        }

        // If we have pending or running actions, set status to processing
        if (!empty($pending_actions) || !empty($running_actions)) {
            $status['status'] = 'processing';
        }
        return $status;
    }
    
    /**
     * Cancel all scheduled translations for a specific item
     * 
     * @param int $item_id The item ID
     * @return int The number of cancelled actions
     */
    public function cancel_translations(int $item_id): int {
        $pending_actions = $this->get_pending_translation_actions($item_id);
        $cancelled_count = 0;
        
        foreach ($pending_actions as $action) {
            if (\as_unschedule_action($this->process_hook, $action->get_args(), $this->group)) {
                $cancelled_count++;
            }
        }
        
        // Clear any timestamps when cancelling translations
        $this->clear_translation_timestamps($item_id);
        
        return $cancelled_count;
    }

    /**
     * Log an error happening during an scheduled action translation
     * 
     * @param int $item_id The item ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @param \Throwable $e The error
     */
    protected function log_error(int $item_id, string $language_slug, array $args, \Throwable $e): void {
        // Post or term
        $item_type = $this->get_item_type();

        // Log the error to the plugin's log file
        $log_message = "Error while running scheduled action for translation of {$item_type} #{$item_id} to {$language_slug}: " . $e->getMessage();
        Helpers::log($log_message, 'error');

        // Set a transient to display the error message on the item edit page
        $key = self::TRANSIENT_LATEST_ERROR_KEY_PREFIX . $item_type . '_' . $item_id;
        set_transient($key, $e->getMessage(), self::TRANSIENT_ERROR_EXPIRATION);
    }

    /**
     * Get the latest error for an item
     * 
     * @param int $item_id The item ID
     * @return string The latest error message
     */
    public function get_latest_error(int $item_id): string {
        $key = self::TRANSIENT_LATEST_ERROR_KEY_PREFIX . $this->get_item_type() . '_' . $item_id;
        return get_transient($key);
    }

    /**
     * Clear the latest error for an item
     * 
     * @param int $item_id The item ID
     */
    public function clear_latest_error(int $item_id): void {
        $key = self::TRANSIENT_LATEST_ERROR_KEY_PREFIX . $this->get_item_type() . '_' . $item_id;
        delete_transient($key);
    }

    /**
     * Store timestamp for a translation action
     * 
     * @param int $item_id The item ID
     * @param string $language_slug The language slug
     * @return void
     */
    protected function store_translation_timestamp(int $item_id, string $language_slug): void {
        $key = $this->get_timestamp_key($item_id, $language_slug);
        set_transient($key, time(), self::TRANSIENT_TIMESTAMP_EXPIRATION);
    }
    
    /**
     * Get the stored timestamp for a translation action
     * 
     * @param int $item_id The item ID
     * @param string $language_slug The language slug
     * @return int|false The timestamp or false if not set
     */
    protected function get_translation_timestamp(int $item_id, string $language_slug) {
        $key = $this->get_timestamp_key($item_id, $language_slug);
        return get_transient($key);
    }
    
    /**
     * Get the transient key for storing a translation timestamp
     * 
     * @param int $item_id The item ID
     * @param string $language_slug The language slug
     * @return string The transient key
     */
    protected function get_timestamp_key(int $item_id, string $language_slug): string {
        return self::TRANSIENT_TIMESTAMP_KEY_PREFIX . $this->get_item_type() . '_' . $item_id . '_' . $language_slug;
    }

    /**
     * Clear translation timestamps for an item
     * 
     * @param int $item_id The item ID
     * @return void
     */
    public function clear_translation_timestamps(int $item_id): void {
        global $wpdb;
        
        $option_name_like = '_transient_' . self::TRANSIENT_TIMESTAMP_KEY_PREFIX . $this->get_item_type() . '_' . $item_id . '_%';
        
        // Get all transients for this item
        $transients = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE %s",
                $option_name_like
            )
        );
        
        // Delete each transient
        foreach ($transients as $transient) {
            $key = str_replace('_transient_', '', $transient->option_name);
            delete_transient($key);
        }
    }
} 
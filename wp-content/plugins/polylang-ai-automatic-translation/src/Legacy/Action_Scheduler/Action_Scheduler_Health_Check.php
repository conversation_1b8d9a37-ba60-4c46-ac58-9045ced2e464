<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Action_Scheduler;

class Action_Scheduler_Health_Check {    
    
    public function check_action_scheduler_health(): array {
        $status = [
            'cron_type' => $this->detect_cron_type(),
            'is_available' => false,
            'issues' => []
        ];

        // Check if Action Scheduler is available and initialized
        if (!class_exists('ActionScheduler') || !function_exists('as_get_scheduled_actions')) {
            $status['issues'][] = 'Oops.. Action Scheduler is not available. To be able to use the bulk translation feature, please make sure that your WordPress site has Action Scheduler enabled.';
            return $status;
        }

        // Ensure Action Scheduler is initialized
        if (!did_action('action_scheduler_init')) {
            $status['issues'][] = 'Oops.. Action Scheduler is not initialized. To be able to use the bulk translation feature, please make sure that your WordPress site has Action Scheduler initialized.';
            return $status;
        }

        $status['is_available'] = true;

        // Get the current time in WordPress timezone
        $current_time = current_time('timestamp');
        $last_run = $this->last_cron_trigger();

        return $status;
    }

    private function last_cron_trigger(): int {
        // Retrieve the cron event schedule
        $cron = _get_cron_array(); // Core WordPress function to fetch all cron events
        $last_run_time = 0;

        if (is_array($cron)) {
            foreach ($cron as $timestamp => $events) {
                foreach ($events as $event_name => $details) {
                    foreach ($details as $event) {
                        if (empty($event['args'])) {
                            // Get the last run timestamp
                            $last_run_time = max($last_run_time, $timestamp);
                        }
                    }
                }
            }
        }
        return $last_run_time;
    }

    private function detect_cron_type(): string {
        if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
            return 'server';
        }
        return 'wp-cron';
    }
}
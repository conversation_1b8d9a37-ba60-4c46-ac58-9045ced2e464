<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use PLL_Language;

class Language_Manager {

    public function get_default_language(): string {
        return pll_default_language();
    }

    public function get_post_by_language(int $post_id, string $language): int {
        return pll_get_post($post_id, $language);
    }

    public function get_available_languages(bool $exclude_default = false): array {
        $languages = pll_languages_list([
            'fields' => 'slug', 
            'hide_empty' => 0, 
            'hide_current' => 1
        ]);
        if ($exclude_default) {
            $default_language = $this->get_default_language();
            $languages = array_filter($languages, function($language) use ($default_language) {
                return $language !== $default_language;
            });
        }
        return $languages;
    }

    public function get_languages_list(): array {
        return PLL()->model->get_languages_list();
    }

    public function get_language_data(string $language): array {
        $languages = pll_the_languages(['raw' => 1, 'hide_if_empty' => 0]);
        if (!empty($languages[$language])) {
            return $languages[$language];
        }
        return [];
    }

    public function get_language_list_for_ui(): array {
        $languages = [];
        if (function_exists('pll_languages_list')) {
            $language_objects = PLL()->model->get_languages_list();
            foreach ($language_objects as $language) {
                $languages[$language->slug] = [
                    'name' => $language->name,
                    'flag' => $language->flag_url,
                    'code' => $language->slug
                ];
            }
        }
        return $languages;
    }

    public function get_language(string $language): PLL_Language {
        return PLL()->model->get_language($language);
    }

    public function get_post_translations(int $post_id): array {
        return pll_get_post_translations($post_id);
    }

    public function get_term_translations(int $term_id): array {
        return pll_get_term_translations($term_id);
    }

    public function set_post_language(int $post_id, string $language) {
        pll_set_post_language($post_id, $language);
    }

    public function set_term_language(int $term_id, string $language) {
        pll_set_term_language($term_id, $language);
    }

    public function get_post_language(int $post_id): string {
        return pll_get_post_language($post_id);
    }

    public function get_term_language(int $term_id): string {
        return pll_get_term_language($term_id);
    }

    public function save_post_translations(array $translations) {
        pll_save_post_translations($translations);
    }

    public function save_term_translations(array $translations) {
        pll_save_term_translations($translations);
    }

}
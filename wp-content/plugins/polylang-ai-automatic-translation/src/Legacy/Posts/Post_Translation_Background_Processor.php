<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Posts;

use EPIC_WP\Polylang_Automatic_AI_Translation\Action_Scheduler\Abstract_Single_Translation_Background_Processor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;

class Post_Translation_Background_Processor extends Abstract_Single_Translation_Background_Processor {


    public function __construct() {
        $this->group = 'pllat-single-post-translation';
        $this->process_hook = 'pllat_process_single_post_translation';
        parent::__construct();
    }
    
    /**
     * Prepare arguments for the action
     * 
     * @param int $post_id The post ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return array Prepared arguments
     */
    protected function prepare_action_args(int $post_id, string $language_slug, array $args): array {
        return [
            'post_id' => $post_id,
            'language_slug' => $language_slug,
            'args' => $args,
        ];
    }
    
    /**
     * Get the item ID key name for this scheduler
     * 
     * @return string The item ID key name
     */
    protected function get_item_id_key(): string {
        return 'post_id';
    }
    
    /**
     * Process the post using the post manager
     * 
     * @param int $post_id The post ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return void
     */
    protected function process_item(int $post_id, string $language_slug, array $args): void {
        $post_manager = Helpers::get_translatable_post_manager($post_id);
        $post_manager->process_queue_item($language_slug, $args);
    }
    
    /**
     * Get the type of item this scheduler handles
     *
     * @return string The item type ('post')
     */
    protected function get_item_type(): string {
        return 'post';
    }
    
    /**
     * Schedule a post translation (alias for schedule_translation to maintain backward compatibility)
     * 
     * @param int $post_id The post ID to translate
     * @param string $language_slug The language to translate to
     * @param array $args Additional arguments for translation
     * @return int|bool The scheduled action ID or false
     */
    public function schedule_post_translation(int $post_id, string $language_slug, array $args = []) {
        return $this->schedule_translation($post_id, $language_slug, $args);
    }
    
    /**
     * Handle a scheduled post translation (alias for handle_translation to maintain backward compatibility)
     * 
     * @param int $post_id The post ID
     * @param string $language_slug The language slug
     * @param array $args Additional arguments
     * @return void
     */
    public function handle_post_translation(int $post_id, string $language_slug, array $args = []): void {
        $this->handle_translation($post_id, $language_slug, $args);
    }
    
    /**
     * Get the translation status for a post (alias for get_translation_status to maintain backward compatibility)
     * 
     * @param int $post_id The post ID
     * @return array Status information
     */
    public function get_post_translation_status(int $post_id): array {
        return $this->get_translation_status($post_id);
    }
    
    /**
     * Cancel all scheduled translations for a specific post (alias for cancel_translations to maintain backward compatibility)
     * 
     * @param int $post_id The post ID
     * @return int The number of cancelled actions
     */
    public function cancel_post_translations(int $post_id): int {
        return $this->cancel_translations($post_id);
    }
} 
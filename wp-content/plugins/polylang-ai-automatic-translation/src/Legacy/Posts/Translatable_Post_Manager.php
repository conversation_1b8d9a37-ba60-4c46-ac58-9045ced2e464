<?php //phpcs:disable

namespace EPIC_WP\Polylang_Automatic_AI_Translation\Posts;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity_Queue_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Constants;
use WP_Post;

/**
 * Manager for translatable posts.
 *
 * @extends Translatable_Entity_Manager<Translatable_Post_Queue_Manager>
 */
class Translatable_Post_Manager extends Translatable_Entity_Manager {

    public function __construct(Translatable_Entity $entity, Translatable_Entity_Queue_Manager $queue_manager, Translator $translator, Language_Manager $language_manager) {
        parent::__construct($entity, $queue_manager, $translator, $language_manager);
    }

    public function process_queue_item(string $language, array $args = []) {

        $updating_id = $this->entity->get_translation_by_language($language);
        $updating = (bool) $updating_id;

        // Field keys can be set virtually in bulk force mode
        if (!empty($args['force_mode'])) {
            $field_keys = $this->queue_manager->get_available_fields();
        } else {
            $field_keys = $this->entity->get_queue_for_language($language);
        }

        $translation_input = $this->prepare_translation_input($language, $field_keys, $args);

        // Prepares placeholders for the prompt containing additional instructions and website context
        $additional_placeholders = $this->prepare_additional_placeholders($args);

        // Translate post fields
        if (!empty($translation_input)) {
            $translation = $this->translate_to($language, $translation_input, $additional_placeholders);
            if (!$updating) {
                $updating_id = $this->copy($language);
                $this->language_manager->set_post_language($updating_id, $language);
            }
            $translatable_post = new Translatable_Post($updating_id, $this->language_manager);
            $post_manager = new self($translatable_post, $this->queue_manager, $this->translator, $this->language_manager);
            $updated_id = $post_manager->update($translation);
            do_action(
                'pllat_post_translated',
                $updated_id,
                $this->entity->get_id(),
                $language,
                $this->translator
            );
        } else {
            $post_manager = $this;
            $updated_id = $updating_id;
        }

        // Translate post meta
        if (!empty($args['force_mode'])) {
            $meta_keys = $this->queue_manager->get_available_meta_fields();
        } else {
            $meta_keys = $this->entity->get_meta_queue_for_language($language);
        }
        $meta_translation_input = $this->prepare_meta_translation_input($language, $meta_keys, $updating, $args);
        if (!empty($meta_translation_input)) {
            $meta_translation = $this->translate_meta_to($language, $meta_translation_input, $additional_placeholders);
            $meta_translations = $meta_translation->get_all();
            if (!empty($meta_translations)) {
                foreach ($meta_translations as $key => $value) {
                    $this->update_meta($updated_id, $key, $value);
                    do_action(
                        'pllat_post_meta_field_translated',
                        $updated_id,
                        $this->entity->get_id(),
                        $key,
                        $value,
                        $language,
                        $this->translator
                    );
                }
                do_action(
                    'pllat_post_meta_translated',
                    $updated_id,
                    $this->entity->get_id(),
                    $language,
                    $this->translator
                );
            }
        }

        do_action(
            'pllat_queue_post_before_translation_processed',
            $this->entity->get_id(),
            $updated_id,
            $this->entity->get_language(),
            $language,
            $updating,
            $args
        );

        $this->update_translations([$language => $updated_id]);
        $this->queue_manager->remove_from_queue($language);
        $this->mark_processed();
        $post_manager->mark_processed();

        do_action(
            'pllat_queue_post_translation_processed',
            $this->entity->get_id(),
            $updated_id,
            $language
        );
    }

    protected function translate_to(string $target_language, array $translation_input = [], array $placeholders = []): Translation {

        $source_language = $this->entity->get_language();
        $post_type = get_post_type($this->entity->get_id());

        if ($post_type === 'product') {
            $translation = $this->translator->translate_product(
                $source_language,
                $target_language,
                $translation_input,
                $placeholders
            );
        } else {
            $translation = $this->translator->translate_post(
                $source_language,
                $target_language,
                $translation_input,
                $placeholders
            );
        }
        return $translation;
    }

    protected function translate_meta_to(string $target_language, array $translation_input = [], array $placeholders = []): Post_Meta_Translation {
        $source_language = $this->entity->get_language();
        return $this->translator->translate_post_meta(
            $source_language,
            $target_language,
            $translation_input,
            $placeholders
        );
    }

    protected function prepare_translation_input(string $language, array $field_keys = [], $args = []): array {
        $translation_input = [];
        $post_object = get_post($this->entity->get_id());
        if ($post_object) {
            $source_array = $post_object->to_array();
            foreach ($field_keys as $index => $field_key) {
                if ($index === Translatable_Entity::QUEUE_META_FIELD_KEY)
                    continue;

                if ($index === Translatable_Entity::QUEUE_CUSTOM_DATA_FIELD_KEY)
                    continue;

                if (!empty($source_array[$field_key])) {
                    $translation_input[$field_key] = $source_array[$field_key];
                }
            }
            $available_fields = Post_Translation::get_available_fields_for($this->entity->get_id());
            $translation_input = array_intersect_key($translation_input, array_flip($available_fields));
        }
        return apply_filters('pllat_post_translation_input', $translation_input, $this->entity, $language, $args);
    }

    protected function prepare_meta_translation_input(string $language, array $meta_keys = [], $args = []): array {
        $translation_input = [];
        $source_array = Helpers::get_flatten_post_meta($this->entity->get_id());
        foreach ($meta_keys as $index => $meta_key) {
            if ($index === Translatable_Entity::QUEUE_CUSTOM_DATA_FIELD_KEY)
                continue;

            if (empty($source_array[$meta_key])) continue;
            $translation_input[$meta_key] = $source_array[$meta_key];
        }
        $available_fields = Post_Meta_Translation::get_available_fields_for($this->entity->get_id());
        $translation_input = array_intersect_key($translation_input, array_flip($available_fields));
        return apply_filters('pllat_post_meta_translation_input', $translation_input, $this->entity, $language, $args);
    }

    protected function update(Translation $translation) {
        $post_id = $this->entity->get_id();
        $postarr = $translation->get_all();
        if (empty($postarr)) return $post_id;
        $postarr['ID'] = $post_id;
        $post_id = wp_update_post($postarr);
        if (is_wp_error($post_id)) {
            throw new \Exception($post_id->get_error_message());
        }
        return $post_id;
    }

    protected function copy(string $language) {

        // Copy based on Polylang pro or free version
        if (property_exists(PLL(), 'sync_post_model' )) {
            if (method_exists(PLL()->sync_post_model, 'copy')) {
                // Use latest copy post method
                $post_id = PLL()->sync_post_model->copy($this->entity->get_id(), $language, false);
            } else {
                // Fallback to deprecated method
                $post_id = PLL()->sync_post_model->copy_post($this->entity->get_id(), $language, false);
            }
        } else {
            $post_id = $this->polylang_free_copy_post($language);
        }

        // Meta fields that should not be copied to avoid conflicts
        $excluded_meta = Constants::get_excluded_copy_meta_fields();
        foreach ($excluded_meta as $meta_key) {
            delete_post_meta($post_id, $meta_key);
        }
        return $post_id;
    }

    protected function update_meta($id, $meta_key, $meta_value) {
        update_post_meta($id, $meta_key, $meta_value);
    }

    protected function delete_meta($id, $meta_key) {
        delete_post_meta($id, $meta_key);
    }

    protected function save_entity_translations(array $translations) {
        $this->language_manager->save_post_translations($translations);
    }

    private function polylang_free_copy_post(string $language) {

        global $wpdb;

        $post_id = $this->entity->get_id();
        $tr_id   = PLL()->model->post->get($post_id, PLL()->model->get_language($language));
        $tr_post = $post = get_post($post_id);

        if (!$tr_post instanceof WP_Post) {
            return 0;
        }

        if (!$tr_id){
            $tr_post->ID = 0;
            $tr_id       = wp_insert_post(wp_slash($tr_post->to_array()));
            PLL()->model->post->set_language($tr_id, $language);

            $translations = PLL()->model->post->get_translations($post_id);
            $translations[$language] = $tr_id;

            PLL()->model->post->save_translations($post_id, $translations);
            PLL()->sync->taxonomies->copy($post_id, $tr_id, $language);
            PLL()->sync->post_metas->copy($post_id, $tr_id, $language);

            do_action('pll_save_post', $post_id, $post, $translations);
        }

        $tr_post->ID = $tr_id;
        $tr_post->post_parent = (int) PLL()->model->post->get($post->post_parent, $language);

        $columns = array(
            'post_author',
            'post_date',
            'post_date_gmt',
            'post_content',
            'post_title',
            'post_excerpt',
            'comment_status',
            'ping_status',
            'post_name',
            'post_modified',
            'post_modified_gmt',
            'post_parent',
            'menu_order',
            'post_mime_type',
        );

        if (is_sticky($post_id)) {
            stick_post($tr_id);
        }

        $tr_post = array_intersect_key((array) $tr_post, array_combine($columns, $columns));
        $wpdb->update($wpdb->posts, $tr_post, array('ID' => $tr_id));

        clean_post_cache($tr_id);

        return $tr_id;
    }
}

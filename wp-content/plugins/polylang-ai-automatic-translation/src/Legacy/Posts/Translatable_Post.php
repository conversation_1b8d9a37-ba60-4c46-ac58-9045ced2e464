<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Posts;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;

class Translatable_Post extends Translatable_Entity {

    public function get_language(): string {
        return $this->language_manager->get_post_language($this->get_id());
    }

    public function get_translations(): array {
        return $this->language_manager->get_post_translations($this->get_id());
    }

    protected function get_meta(string $key, bool $single = false) {
        return get_post_meta($this->get_id(), $key, $single);
    }

    protected function is_excluded_from_translation_meta(): bool {
        return get_post_meta($this->get_id(), self::META_KEY_EXCLUDE, true) == true;
    }

    protected function create_translatable_entity(int $id): self {
        return new self($id, $this->language_manager);
    }

    public function get_title(): string {
        return get_the_title($this->get_id());
    }

    public function get_edit_link(): string {
        $link = get_edit_post_link($this->get_id());
        if ($link) {
            return $link;
        }
        // Fallback: construct the admin URL manually
        return admin_url(sprintf('post.php?post=%d&action=edit', $this->get_id()));
    }
}

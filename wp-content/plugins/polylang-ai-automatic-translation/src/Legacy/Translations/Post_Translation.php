<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translations;

class Post_Translation extends Translation {

    public static function get_available_fields() : array {
        return apply_filters('pllat_available_post_translation_fields', [
            'post_title',
            'post_content',
            'post_excerpt',
            'post_name',
        ]);
    }

    public static function get_available_fields_for(int $id) : array {
        return apply_filters('pllat_available_post_translation_fields_for', self::get_available_fields(), $id);
    }

}
<?php 
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translations;

class Term_Translation extends Translation {
    
    public static function get_available_fields() : array {
        return apply_filters('pllat_available_term_translation_fields', [
            'name',
            'description',
            'slug',
        ]);
    }

    public static function get_available_fields_for(int $id) : array {
        return apply_filters('pllat_available_term_translation_fields_for', self::get_available_fields(), $id);
    }
}
<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator\OpenAI;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Response_Handler_Interface;

class Response_Handler implements Response_Handler_Interface {
    public function handle_response( $response, bool $json_output = false, bool $correct_nesting = false ) {
        $response_body = $response->getBody();
        $result        = Helpers::decode_json( $response_body, true );
        $content       = $result['choices'][0]['message']['content'];

        if ( empty( $content ) ) {
            throw new \Exception( 'OpenAI API completion does not contain content.' );
        }
        return $json_output ? $this->handle_json_response( $content, $correct_nesting ) : $content;
    }

    private function handle_json_response( string $completion, bool $correct_nesting = false ): array {
        $completion = \str_replace( array( '```json', '```' ), '', $completion );

        if ( false === \strpos( $completion, '{' ) ) {
            $completion = '{' . $completion . '}';
        }

        $completion = Helpers::decode_json( $completion );

        if ( $correct_nesting ) {
            $completion = \array_map(
                static fn( $value ) => \is_array( $value ) ? \reset( $value ) : $value,
                $completion,
            );
        }

        return $completion;
    }
}

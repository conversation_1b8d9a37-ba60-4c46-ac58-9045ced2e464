<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator\OpenAI;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Token_Encoder_Interface;
use Yethee\Tiktoken\EncoderProvider;

class Token_Encoder implements Token_Encoder_Interface {
    private static ?EncoderProvider $provider = null;
    private string $model;

    public function __construct( string $model ) {
        $this->model = $model;
    }

    private function get_provider(): EncoderProvider {
        if ( null === self::$provider ) {
            self::$provider = new EncoderProvider();
        }
        return self::$provider;
    }

    public function count_tokens( string $text ): int {
        $provider = $this->get_provider();
        $encoder  = $provider->getForModel( $this->model );
        return \count( $encoder->encode( $text ) );
    }

    public function set_model( string $model ) {
        $this->model = $model;
    }
}

<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Token_Encoder_Interface;

class Prompt_Handler {

    private $token_encoder;

    public function __construct(Token_Encoder_Interface $token_encoder) {
        $this->token_encoder = $token_encoder;
    }

    public function get_prompt_messages(string $file, array $placeholders = [], bool $is_reasoning_model = false): array {
        
        $messages = $this->get_prompt_messages_from_file($file);
        $messages = $this->prepare_messages($messages, $is_reasoning_model);

        if (!empty($placeholders)) {
            $messages = $this->replace_message_placeholders($messages, $placeholders);
        }
        return $messages;
    }

    public function replace_message_placeholders(array $messages, array $placeholders = []): array {
        foreach ($messages as &$message) {
            foreach ($placeholders as $key => $value) {
                $message['content'] = str_replace('{{' . $key . '}}', $value, $message['content']);
            }
            // Remove placeholders that were not set
            $message['content'] = preg_replace('/{{\w+}}/', '', $message['content']);
        }
        return $messages;
    }

    private function prepare_messages(array $messages, bool $is_reasoning_model = false): array {
        foreach ($messages as &$message) {
            if (!empty($message['content_file'])) {
                $content = $this->get_prompt_text_from_file($message['content_file']);
                $content = $this->prepare_message_content($content);
                $message['content'] = $content;
                unset($message['content_file']);
            }
            if ($is_reasoning_model && $message['role'] === 'system') {
                $message['role'] = 'user';
            }
        }
        return $messages;
    }

    public function get_prompt_token_count(string $type, array $placeholders = []): int {
        $messages = $this->get_prompt_messages($type, $placeholders);
        $tokens = 0;
        foreach ($messages as $message) {
            $tokens += $this->token_encoder->count_tokens($message['content']);
        }
        return $tokens;
    }

    private function get_prompt_messages_from_file(string $type): array {
        $file_path = $this->get_prompt_file($type);
        $contents = file_get_contents($file_path);
        if (empty($contents)) {
            throw new \Exception('Error loading prompt content from file.');
        }
        return Helpers::decode_json($contents, true);
    }

    private function get_prompt_file(string $filename): string {
        $file_path = PLLAT_PLUGIN_DIR . 'prompts/' . $filename . '.json';
        if (!file_exists($file_path)) {
            throw new \Exception('Prompt file not found.');
        }
        return $file_path;
    }

    private function get_prompt_text_from_file(string $file): string {
        $file_path = $this->get_prompt_text_file($file);
        $contents = file_get_contents($file_path);
        if (empty($contents)) {
            throw new \Exception('Error loading prompt content from file.');
        }
        return $contents;
    }

    private function get_prompt_text_file(string $file): string {
        $file_path = PLLAT_PLUGIN_DIR . 'prompts/text/' . $file . '.txt';
        if (!file_exists($file_path)) {
            throw new \Exception('Prompt text file not found.');
        }
        return $file_path;
    }

    private function prepare_message_content(string $content): string {
        $content = trim($content);
        return $content;
    }
}
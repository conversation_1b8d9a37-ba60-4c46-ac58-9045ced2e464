<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Translator\OpenRouter;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Token_Encoder_Interface;
use Yethee\Tiktoken\EncoderProvider;

class Token_Encoder implements Token_Encoder_Interface {

    private static ?EncoderProvider $provider = null;
    private string $model;

    public function __construct(string $model) {
        $this->model = $model;
    }

    private function get_provider(): EncoderProvider {
        if (self::$provider === null) {
            self::$provider = new EncoderProvider();
        }
        return self::$provider;
    }

    public function count_tokens(string $text): int {
        $provider = $this->get_provider();
        $tokenizer_model = $this->get_tokenizer_model();
        $encoder = $provider->getForModel($tokenizer_model);
        return count($encoder->encode($text));
    }

    private function get_tokenizer_model(): string {
        return 'gpt-4o'; // Later implement tokenizer model mapping
    }

    public function set_model(string $model) {
        $this->model = $model;
    }
} 
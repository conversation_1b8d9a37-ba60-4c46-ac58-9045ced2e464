<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;

abstract class Translatable_Entity {
    public const QUEUE_META_FIELD_KEY        = '_pllat_meta';
    public const QUEUE_CUSTOM_DATA_FIELD_KEY = '_pllat_custom_data';
    public const META_KEY_QUEUE              = '_pllat_translation_queue';
    public const META_KEY_PROCESSED          = '_pllat_last_processed';
    public const META_KEY_EXCLUDE            = '_pllat_exclude_from_translation';

    protected int $id;
    protected Language_Manager $language_manager;

    public function __construct( int $id, Language_Manager $language_manager ) {
        $this->id               = $id;
        $this->language_manager = $language_manager;
    }

    abstract public function get_title(): string;

    abstract public function get_edit_link(): string;

    abstract public function get_language(): string;

    abstract public function get_translations(): array;

    abstract protected function get_meta( string $key, bool $single = false );

    abstract protected function is_excluded_from_translation_meta(): bool;

    abstract protected function create_translatable_entity( int $id ): self;

    public function get_id(): int {
        return $this->id;
    }

    public function get_missing_translations(): array {
        return \array_diff(
            $this->get_available_languages(),
            \array_keys( $this->get_translations() ),
            array( $this->get_language() ),
        );
    }

    public function get_queue(): array {
        $queue = $this->get_meta( self::META_KEY_QUEUE, true );
        return \is_array( $queue ) ? $queue : array();
    }

    public function get_queue_for_language( string $language ): array {
        $queue = $this->get_queue();
        return $queue[ $language ] ?? array();
    }

    public function get_queue_languages(): array {
        $queue = $this->get_queue();
        return \array_keys( $queue );
    }

    public function get_meta_queue(): array {
        $queue = $this->get_queue();
        return $queue[ self::QUEUE_META_FIELD_KEY ] ?? array();
    }

    public function get_meta_queue_for_language( string $language ): array {
        $queue = $this->get_queue_for_language( $language );
        return $queue[ self::QUEUE_META_FIELD_KEY ] ?? array();
    }

    public function get_custom_data_queue( string $key ): array {
        $queue = $this->get_queue();
        return $queue[ self::QUEUE_CUSTOM_DATA_FIELD_KEY ] ?? array();
    }

    public function get_custom_data_queue_for_language( string $key, string $language ): array {
        $queue = $this->get_queue_for_language( $language );
        if ( ! isset( $queue[ self::QUEUE_CUSTOM_DATA_FIELD_KEY ] ) ) {
            return array();
        }
        if ( ! isset( $queue[ self::QUEUE_CUSTOM_DATA_FIELD_KEY ][ $key ] ) ) {
            return array();
        }
        return $queue[ self::QUEUE_CUSTOM_DATA_FIELD_KEY ][ $key ];
    }

    public function get_last_processed(): ?int {
        return (int) $this->get_meta( self::META_KEY_PROCESSED, true );
    }

    public function is_excluded_from_translation(): bool {
        return $this->is_excluded_from_translation_meta();
    }

    public function get_available_languages(): array {
        $exclude_default_language = false;
        if ( $this->get_language() === $this->language_manager->get_default_language() ) {
            $exclude_default_language = true;
        }
        return $this->language_manager->get_available_languages( $exclude_default_language );
    }

    public function get_translation_by_language( string $language ): ?int {
        $translations = $this->get_translations();
        return $translations[ $language ] ?? null;
    }

    public function get_unprocessed_translations(): array {
        $unprocessed  = array();
        $translations = $this->get_translations();

        if ( empty( $translations ) ) {
            return $unprocessed;
        }

        foreach ( $translations as $language_slug => $translation_id ) {
            try {
                if ( $language_slug === $this->get_language() ) {
                    continue;
                }

                $translation_id = $this->get_translation_by_language( $language_slug );
                if ( ! $translation_id ) {
                    continue;
                }

                $translation_item = $this->create_translatable_entity( $translation_id );

                if ( ! $translation_item->get_last_processed() ) {
                    $unprocessed[] = $language_slug;
                }
            } catch ( \InvalidArgumentException $e ) {
                \error_log(
                    'Error while trying to get unprocessed translations for item ' . $this->id . ': ' . $e->getMessage(),
                );
                continue;
            }
        }
        return $unprocessed;
    }

    public function get_translation_errors(): array {
    }
}

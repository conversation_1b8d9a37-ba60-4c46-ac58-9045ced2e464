<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Token_Encoder_Interface;

class Array_Chunker {

    private $strings;
    private $encoder;
    private $excluded_strings;

    public function __construct(array $strings, Token_Encoder_Interface $encoder) {
        $this->strings = $strings;
        $this->encoder = $encoder;
        $this->excluded_strings = [];
    }

    public function chunk(int $max_tokens): array {
        $chunks = [];
        $current_chunk = [];
        $current_json = '{}';
        $current_token_count = $this->count_tokens($current_json);

        foreach ($this->strings as $key => $value) {
            $item_json = json_encode([$key => $value]);
            $item_token_count = $this->count_tokens($item_json);

            if ($item_token_count > $max_tokens) {
                // If the current item alone exceeds max_tokens, exclude it
                $this->excluded_strings[$key] = $value;
                continue;
            }

            $temp_chunk = $current_chunk;
            $temp_chunk[$key] = $value;
            $temp_json = json_encode($temp_chunk);
            $temp_token_count = $this->count_tokens($temp_json);

            if ($temp_token_count > $max_tokens) {
                if (!empty($current_chunk)) {
                    $chunks[] = [
                        'strings' => $current_chunk,
                        'token_count' => $current_token_count
                    ];
                }
                $current_chunk = [$key => $value];
                $current_json = json_encode($current_chunk);
                $current_token_count = $this->count_tokens($current_json);
            } else {
                $current_chunk = $temp_chunk;
                $current_json = $temp_json;
                $current_token_count = $temp_token_count;
            }
        }

        // Add the last chunk if it's not empty
        if (!empty($current_chunk)) {
            $chunks[] = [
                'strings' => $current_chunk,
                'token_count' => $current_token_count
            ];
        }

        return $chunks;
    }

    public function get_excluded_strings(): array {
        return $this->excluded_strings;
    }

    private function count_tokens(string $json): int {
        return $this->encoder->count_tokens($json);
    }
}

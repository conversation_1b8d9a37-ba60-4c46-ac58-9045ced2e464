<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\ACF;

use EPIC_WP\Polylang_Automatic_AI_Translation\Constants;

class ACF_Manager {

    /**
     * @var array Field types that can be translated
     */
    private $translatable_types = [
        'text',
        'textarea',
        'wysiwyg',
    ];

    public function get_all_acf_post_field_meta_keys(int $post_id): array {
        // Get all meta keys for this post
        $all_meta_keys = get_post_meta($post_id);
        
        if (!$all_meta_keys) {
            return [];
        }

        // Filter keys based on whether they should be translated
        $keys = array_filter(
            array_keys($all_meta_keys),
            function($key) use ($post_id) {

                if (!is_string($key)) {
                    return false;
                }
                
                // Skip system meta fields (those starting with underscores)
                if (strpos($key, '_') === 0) {
                    return false;
                }
                
                if (in_array($key, Constants::get_excluded_copy_meta_fields())) {
                    return false;
                }

                try {
                    $field = $this->assure_field_object($key, $post_id);
                    return $field && $this->should_translate_field($field);
                } catch (\Throwable $e) {
                    return false;
                }
            }
        );
        return $keys;
    }

    /**
     * Get all ACF fields for a specific term
     */
    public function get_all_acf_term_field_meta_keys(int $term_id): array {
        
        // Get all meta keys for this term
        $all_meta_keys = get_term_meta($term_id);
        
        if (!$all_meta_keys) {
            return [];
        }

        // Filter keys based on whether they should be translated
        $keys = array_filter(
            array_keys($all_meta_keys),
            function($key) use ($term_id) {

                if (!is_string($key)) {
                    return false;
                }

                // Skip system meta fields (those starting with underscores)
                if (strpos($key, '_') === 0) {
                    return false;
                }

                if (in_array($key, Constants::get_excluded_copy_meta_fields())) {
                    return false;
                }
                
                try {
                    $id = 'term_' . $term_id;
                    $field = $this->assure_field_object($key, $id);
                    return $field && $this->should_translate_field($field);
                } catch (\Throwable $e) {
                    return false;
                }
            }
        );
        return $keys;
    }

    /**
     * Get the field object and initialize it if it doesn't exist yet
     * 
     * In some cases, the field object is not created yet, but there is a value and an ACF field definition
     * In this case, we need to initialize the field object by updating the field value
     * 
     * For clone fields, ACF generates composite keys like:
     * "field_677bacf1ae708_field_677baca24e935"
     * In such cases, we need to extract the last field key ("field_677baca24e935") to get the correct field object.
     */
    private function assure_field_object(string $key, $id) {
        // First try to get the field object directly
        $field = get_field_object($key, $id);
        
        // If field object doesn't exist, but there is a value and an ACF field definition
        if (empty($field) && get_field($key, $id) !== null) {
            $meta_key = '_' . $key;
            $field_key = is_numeric($id) ? 
                get_post_meta($id, $meta_key, true) : 
                get_term_meta(substr($id, 5), $meta_key, true);
    
            if (empty($field_key)) {
                if (is_numeric($id)) {
                    // $field_key = 'field_';
                    // update_post_meta($id, $meta_key, $field_key);
                    update_field($key, get_field($key, $id), $id);
                } else {
                    // $field_key = 'field_';
                    // update_term_meta(substr($id, 5), $meta_key, $field_key);
                    update_field($key, get_field($key, $id), $id);
                }
                // Retrieve the field object again
                $field = get_field_object($key, $id);
            } else {
                // Extract the last field key from the composite key
                if (strpos($field_key, '_field_') !== false) {
                    $key_parts = explode('_field_', $field_key);
                    $key = 'field_' . end($key_parts);
                    $field = get_field_object($key, $id);
                }
            }
        }
        return $field;
    }
    
    /**
     * Check if a field should be translated
     */
    private function should_translate_field($field) {
        
        if (!in_array($field['type'], $this->translatable_types)) {
            return false;
        }
        
        if (isset($field['translations']) && $field['translations'] !== 'translate') {
            return false;
        }

        return true;
    }
}
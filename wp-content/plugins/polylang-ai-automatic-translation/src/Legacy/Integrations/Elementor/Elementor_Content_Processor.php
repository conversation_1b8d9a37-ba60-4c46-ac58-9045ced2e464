<?php //phpcs:disable
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use PLLAT\Common\Interfaces\Extracts_String;

class Elementor_Content_Processor implements Extracts_String {

    private $language_manager;
    private $string_manager;
    private $merge_manager;

    public function __construct(array $translatable_keys = []) {
        $this->language_manager = new Language_Manager();
        $key_validator = new Key_Validator($translatable_keys);
        $string_sanitizer = new String_Sanitizer();
        $this->string_manager = new String_Manager($key_validator, $string_sanitizer);
        $this->merge_manager = new Merge_Manager($key_validator, $string_sanitizer);
    }

    public function extract_strings(string $content, array $change_hashes = []): array {
        $data = \json_decode($content, true) ?? [];

        return $this->string_manager->extract_strings($data, $change_hashes);
    }

    public function extract_translatable_strings(array $elementor_data, array $change_hashes = []): array {
        return $this->string_manager->extract_strings($elementor_data, $change_hashes);
    }

    public function replace_translated_strings(array $elementor_data, array $strings_data, array $translated_strings): array {
        return $this->string_manager->replace_strings($elementor_data, $strings_data, $translated_strings);
    }

    public function get_changed_strings_hashes(array $old_data, array $new_data): array {
        $old_strings = $this->string_manager->extract_strings($old_data);
        $new_strings = $this->string_manager->extract_strings($new_data);

        $changed_strings_hashes = [];

        // Find strings that are in new_strings but not in old_strings
        $new_strings_hashes = array_keys(array_diff_key($new_strings, $old_strings));

        // Also check for strings that have changed value but kept the same key
        foreach ($new_strings as $hash => $new_string) {
            if (isset($old_strings[$hash]) && $old_strings[$hash]['value'] !== $new_string['value']) {
                $changed_strings_hashes[] = $hash;
            }
        }

        return array_merge($new_strings_hashes, $changed_strings_hashes);
    }

    public function merge_elementor_post_content(array $source_data, array $target_data): array {
        return $this->merge_manager->merge_elementor_data($source_data, $target_data);
    }

    public function get_elementor_post_content(int $post_id, bool $associative = false) {
        $elementor_data = get_post_meta($post_id, '_elementor_data', true);
        if ($elementor_data) {
            return $associative ? json_decode($elementor_data, true) : $elementor_data;
        }
        return null;
    }

    public function update_elementor_post_content(int $post_id, array $elementor_data) {
        $encoded_content = wp_json_encode(
            $elementor_data,
            JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE
        );
        update_post_meta($post_id, '_elementor_data', wp_slash($encoded_content));
    }

    public function get_elementor_post_content_changes(int $post_id, string $language): array {
        $translatable_post = new Translatable_Post($post_id, $this->language_manager);
        return $translatable_post->get_custom_data_queue_for_language('_pllat_elementor_data_changes', $language);
    }

    public function remove_elementor_post_content_changes(int $post_id, string $language) {
        $post_manager = Helpers::get_translatable_post_manager($post_id);
        $post_manager->remove_custom_data_from_queue($language, '_pllat_elementor_data_changes');
    }

    public function sync_template_settings(int $source_id, int $target_id): void {
        // Get template mode from source
        $source_template = get_post_meta($source_id, '_wp_page_template', true);

        if (!empty($source_template)) {
            // Sync the template mode
            update_post_meta($target_id, '_wp_page_template', $source_template);
        }

        // Also sync Elementor's template mode if it exists
        $source_elementor_template = get_post_meta($source_id, '_elementor_template_type', true);
        if (!empty($source_elementor_template)) {
            update_post_meta($target_id, '_elementor_template_type', $source_elementor_template);
        }
    }

    public function is_built_with_elementor(int $post_id): bool {
        return get_post_meta($post_id, '_elementor_edit_mode', true) === 'builder';
    }
}

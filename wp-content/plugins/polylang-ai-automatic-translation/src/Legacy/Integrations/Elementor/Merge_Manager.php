<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor;

class Merge_Manager {
    private $key_validator;
    private $string_sanitizer;

    const SKIP_NESTED_KEYS = array(
        'selected_icon',
    );

    public function __construct( Key_Validator $key_validator, String_Sanitizer $string_sanitizer ) {
        $this->key_validator    = $key_validator;
        $this->string_sanitizer = $string_sanitizer;
    }

    public function merge_elementor_data( array $source_data, array $target_data ): array {
        $merged_data = array();

        foreach ( $source_data as $index => $source_element ) {
            $target_element = $this->find_element_by_id( $target_data, $source_element['id'] );

            $merged_element = $target_element ? $this->merge_element(
                $source_element,
                $target_element,
            ) : $source_element;

            if ( isset( $merged_element['elements'] ) && \is_array( $merged_element['elements'] ) ) {
                $merged_element['elements'] = $this->merge_elementor_data(
                    $merged_element['elements'],
                    $target_element['elements'] ?? array(),
                );
            }

            $merged_data[] = $merged_element;
        }

        return $merged_data;
    }

    private function merge_element( array $source_element, array $target_element ): array {
        $merged_element = $source_element;

        foreach ( $target_element as $key => $value ) {
            if ( 'settings' === $key ) {
                $merged_element['settings'] = $this->merge_settings(
                    $source_element['settings'] ?? array(),
                    $value,
                );
            } elseif ( 'elements' === $key && \is_array( $value ) ) {
                // This will be handled in the main merge_elementor_data function
                continue;
            } elseif ( $this->key_validator->should_translate( $key ) ) {
                $merged_element[ $key ] = $value;
            }
        }

        return $merged_element;
    }

    private function merge_settings( array $source_settings, array $target_settings ): array {
        $merged_settings = $source_settings;

        foreach ( $target_settings as $key => $value ) {
            // Special handling for shortcodes
            if ( 'shortcode' === $key ) {
                $merged_settings[ $key ] = $this->sanitize_string( $value );
                continue;
            }

            // Handle translatable keys
            if ( $this->key_validator->should_translate( $key ) ) {
                $merged_settings[ $key ] = $this->merge_translatable_setting(
                    $value,
                    $source_settings[ $key ] ?? null,
                );
                continue;
            }

            // Skip nested array processing for certain keys
            if ( \in_array( $key, self::SKIP_NESTED_KEYS ) ) {
                continue;
            }

            // Handle nested arrays
            if ( ! \is_array( $value ) || ! isset( $source_settings[ $key ] ) || ! \is_array(
                $source_settings[ $key ] 
            ) ) {
                continue;
            }

            $merged_settings[ $key ] = $this->merge_settings( $source_settings[ $key ], $value );
        }

        return $merged_settings;
    }

    private function merge_translatable_setting( $target_value, $source_value ): mixed {
        if ( \is_array( $target_value ) && \is_array( $source_value ) ) {
            return $this->merge_nested_array( $source_value, $target_value );
        }

        return \is_string( $target_value ) ? $this->sanitize_string( $target_value ) : $target_value;
    }

    private function merge_nested_array( array $source_array, array $target_array ): array {
        $merged_array = array();

        // First, process all source items
        foreach ( $source_array as $index => $source_item ) {
            $target_item    = $target_array[ $index ] ?? null;
            $merged_array[] = $this->merge_single_item( $source_item, $target_item );
        }

        // Then add any new items from target that weren't in source
        foreach ( $target_array as $index => $target_item ) {
            if ( isset( $source_array[ $index ] ) ) {
                continue;
            }

            $merged_array[] = $target_item;
        }

        return $merged_array;
    }

    private function merge_single_item( $source_item, $target_item ) {
        if ( ! $target_item || ! \is_array( $source_item ) || ! \is_array( $target_item ) ) {
            return $source_item;
        }

        $merged_item = $source_item;
        foreach ( $target_item as $nested_key => $nested_value ) {
            if ( ! $this->key_validator->should_translate( $nested_key ) ) {
                continue;
            }

            $merged_item[ $nested_key ] = $nested_value;
        }

        return $merged_item;
    }

    private function find_element_by_id( array $data, string $id ): ?array {
        foreach ( $data as $element ) {
            if ( isset( $element['id'] ) && $element['id'] === $id ) {
                return $element;
            }
            if ( ! isset( $element['elements'] ) || ! \is_array( $element['elements'] ) ) {
                continue;
            }

            $found = $this->find_element_by_id( $element['elements'], $id );
            if ( $found ) {
                return $found;
            }
        }
        return null;
    }

    private function sanitize_string( $string ) {
        return $this->string_sanitizer->sanitize_string( $string );
    }
}

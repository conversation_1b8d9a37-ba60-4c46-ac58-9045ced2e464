<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Posts\Translatable_Post;
use PLLAT\Common\Interfaces\Extracts_String;

/**
 * Elementor_Content_Editor
 */
class Elementor_Content_Editor implements Extracts_String {
    private Language_Manager $language_manager;
    private String_Manager $string_manager;
    private Merge_Manager $merge_manager;

    public function __construct( array $translatable_keys = array() ) {
        $this->language_manager = \xwp_app( 'pllat' )->get( Language_Manager::class );
        $key_validator          = new Key_Validator( $translatable_keys );
        $string_sanitizer       = new String_Sanitizer();
        $this->string_manager   = new String_Manager( $key_validator, $string_sanitizer );
        $this->merge_manager    = new Merge_Manager( $key_validator, $string_sanitizer );
    }

    /**
     * Extract strings from Elementor data
     *
     * @param string $content The raw json elementor data (from _elementor_data post meta) to extract strings from
     * @param array  $change_hashes Hashes of strings that need to be extract only. If set, only the strings with these hashes will be extracted.
     * @return array The extracted strings. An array with hash as key and an array as value containing 'key' and 'value' elements. Example: ['hash123' => ['key' => 'editor', 'value' => 'the string']]
     */
    public function extract_strings( string $content, array $change_hashes = array() ): array {
        $data = \json_decode( $content, true ) ?? array();
        return $this->string_manager->extract_strings( $data, $change_hashes );
    }

    /**
     * Extract strings where the key passes the key validator test to be translated
     *
     * @param array $elementor_data The Elementor data (from _elementor_data post meta) to extract strings from
     * @param array $change_hashes Hashes of strings that need to be extract only. If set, only the strings with these hashes will be extracted.
     * @return array The extracted strings. An array with hash as key and an array as value containing 'key' and 'value' elements. Example: ['hash123' => ['key' => 'editor', 'value' => 'the string']]
     */
    public function extract_translatable_strings( ?array $elementor_data, array $change_hashes = array() ): array {
        return $this->string_manager->extract_strings( $elementor_data ?? array(), $change_hashes );
    }

    /**
     * Replace translated strings in Elementor data
     *
     * @param array $elementor_data The Elementor data (from _elementor_data post meta) to replace strings in
     * @param array $strings_data The strings data to replace. Array with hash as key and an array as value containing 'key' and 'value' elements. Example: ['hash123' => ['key' => 'editor', 'value' => 'the string']]
     * @param array $translated_strings The translated strings to replace. An array with hash as key and the translated string as value. Example: ['hash123' => 'translated string']
     * @return array The updated Elementor data array
     */
    public function replace_translated_strings( ?array $elementor_data, array $strings_data, array $translated_strings ): array {
        return $this->string_manager->replace_strings(
            $elementor_data ?? array(),
            $strings_data,
            $translated_strings,
        );
    }

    /**
     * Get the hashes of strings that have changed by comparing the old and new Elementor data
     *
     * @param array $old_data The old Elementor data
     * @param array $new_data The new Elementor data
     * @return array The hashes of strings that have changed
     */
    public function get_changed_strings_hashes( array $old_data, array $new_data ): array {
        $old_strings = $this->string_manager->extract_strings( $old_data );
        $new_strings = $this->string_manager->extract_strings( $new_data );

        $changed_strings_hashes = array();

        // Find strings that are in new_strings but not in old_strings
        $new_strings_hashes = \array_keys( \array_diff_key( $new_strings, $old_strings ) );

        // Also check for strings that have changed value but kept the same key
        foreach ( $new_strings as $hash => $new_string ) {
            if ( ! isset( $old_strings[ $hash ] ) || $old_strings[ $hash ]['value'] === $new_string['value'] ) {
                continue;
            }
            $changed_strings_hashes[] = $hash;
        }
        return \array_merge( $new_strings_hashes, $changed_strings_hashes );
    }

    /**
     * Merge the source and target Elementor data arrays
     *
     * @param array $source_data The source Elementor data
     * @param array $target_data The target Elementor data
     * @return array The merged Elementor data
     */
    public function merge_elementor_post_content( array $source_data, array $target_data ): array {
        return $this->merge_manager->merge_elementor_data( $source_data, $target_data );
    }

    public function get_elementor_post_content( int $post_id, bool $associative = false ) {
        $elementor_data = \get_post_meta( $post_id, '_elementor_data', true );
        if ( $elementor_data ) {
            return $associative ? \json_decode( $elementor_data, true ) : $elementor_data;
        }
        return null;
    }

    public function update_elementor_post_content( int $post_id, array $elementor_data ) {
        $encoded_content = \wp_json_encode(
            $elementor_data,
            JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE,
        );
        \update_post_meta( $post_id, '_elementor_data', \wp_slash( $encoded_content ) );
    }

    public function get_elementor_post_content_changes( int $post_id, string $language ): array {
        $translatable_post = new Translatable_Post( $post_id, $this->language_manager );
        return $translatable_post->get_custom_data_queue_for_language(
            '_pllat_elementor_data_changes',
            $language,
        );
    }

    public function remove_elementor_post_content_changes( int $post_id, string $language ) {
        $post_manager = Helpers::get_translatable_post_manager( $post_id );
        $post_manager->remove_custom_data_from_queue( $language, '_pllat_elementor_data_changes' );
    }

    public function sync_template_settings( int $source_id, int $target_id ): void {
        // Get template mode from source
        $source_template = \get_post_meta( $source_id, '_wp_page_template', true );

        if ( ! empty( $source_template ) ) {
            // Sync the template mode
            \update_post_meta( $target_id, '_wp_page_template', $source_template );
        }

        // Also sync Elementor's template mode if it exists
        $source_elementor_template = \get_post_meta( $source_id, '_elementor_template_type', true );
        if ( empty( $source_elementor_template ) ) {
            return;
        }

        \update_post_meta( $target_id, '_elementor_template_type', $source_elementor_template );
    }

    public function is_built_with_elementor( int $post_id ): bool {
        return 'builder' === \get_post_meta( $post_id, '_elementor_edit_mode', true );
    }
}

<?php //phpcs:disable

namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;

/**
 * @template TQm of Translatable_Entity_Queue_Manager
 *
 * @mixin TQm
 */
abstract class Translatable_Entity_Manager {

    protected Translatable_Entity $entity;
    protected Translatable_Entity_Queue_Manager $queue_manager;
    protected Translator $translator;
    protected Language_Manager $language_manager;

    public function __construct(Translatable_Entity $entity, Translatable_Entity_Queue_Manager $queue_manager, Translator $translator, Language_Manager $language_manager) {
        $this->entity = $entity;
        $this->queue_manager = $queue_manager;
        $this->language_manager = $language_manager;
        $this->translator = $translator;
    }

    abstract public function process_queue_item(string $language);

    protected function update_translations(array $translations) {
        $translations = [
            ...$translations,
            $this->entity->get_language() => $this->entity->get_id(),
        ];
        $translations = array_merge($translations, $this->entity->get_translations());
        $this->save_entity_translations(array_unique($translations));
    }

    public function mark_processed() {
        $this->update_meta($this->entity->get_id(), Translatable_Entity::META_KEY_PROCESSED, time());
    }

    public function update_exclude_from_translation($exclude = true) {
        if ($exclude) {
            $this->update_meta($this->entity->get_id(), Translatable_Entity::META_KEY_EXCLUDE, true);
        } else {
            $this->delete_meta($this->entity->get_id(), Translatable_Entity::META_KEY_EXCLUDE);
        }
    }

    public function get_translatable_entity() {
        return $this->entity;
    }

    public function prepare_additional_placeholders(array $args = []): array {
        $placeholders = [];

        if (!empty($args['additional_instructions'])) {
            $placeholders['additional_instructions'] = 'Additional instructions for the translator: \n' . $args['additional_instructions'];
        }
        if (!empty($args['website_context'])) {
            $placeholders['website_context'] = 'Additional context to know about before translating: \n' . $args['website_context'];
        }
        return $placeholders;
    }

    public function add_translation_error($error) {

    }

    public function __call($method, $args) {
        if (method_exists($this->queue_manager, $method)) {
            return call_user_func_array([$this->queue_manager, $method], $args);
        }
        throw new \Exception('Method does not exist: ' . $method);
    }

}

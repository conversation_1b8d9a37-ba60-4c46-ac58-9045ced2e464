<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Bulk\Query;

use PLLAT\Translator\Services\Bulk_Config;

class Term_Query {
    /** @var array<string> */
    private array $statuses;

    /** @var Bulk_Config */
    private Bulk_Config $config;

    public function __construct( Bulk_Config $config, array $statuses ) {
        $this->config   = $config;
        $this->statuses = $statuses;
    }

    public function get_translatable_ids( string $taxonomy, array $languages = array(), int $limit = 10000 ): array {
        global $wpdb;

        $default_language_term_id = $this->get_default_language_term_id();

        $sql = $wpdb->prepare(
            "SELECT DISTINCT t.term_id as ID,
                tt_lang.description as language,
                queue_meta.meta_value as translation_queue,
                MAX(tt_translations.description) as translations
            FROM {$wpdb->terms} t
            INNER JOIN {$wpdb->term_taxonomy} tx
                ON (t.term_id = tx.term_id AND tx.taxonomy = %s)
            INNER JOIN {$wpdb->term_relationships} tr_lang
                ON (tr_lang.object_id = t.term_id)
            INNER JOIN {$wpdb->term_taxonomy} tt_lang
                ON (tt_lang.term_taxonomy_id = tr_lang.term_taxonomy_id AND tt_lang.taxonomy = 'term_language')
            LEFT JOIN {$wpdb->termmeta} queue_meta
                ON (t.term_id = queue_meta.term_id AND queue_meta.meta_key = '_pllat_translation_queue')
            LEFT JOIN {$wpdb->termmeta} exclude_meta
                ON (t.term_id = exclude_meta.term_id AND exclude_meta.meta_key = '_pllat_exclude_from_translation')
            LEFT JOIN {$wpdb->term_relationships} tr_translations
                ON (tr_translations.object_id = t.term_id)
            LEFT JOIN {$wpdb->term_taxonomy} tt_translations
                ON (tt_translations.term_taxonomy_id = tr_translations.term_taxonomy_id AND tt_translations.taxonomy = 'term_translations')
            WHERE tt_lang.term_id = %d
            AND (exclude_meta.meta_value IS NULL OR exclude_meta.meta_value = '')
            AND (queue_meta.meta_value IS NOT NULL OR tt_translations.description IS NULL)
            GROUP BY t.term_id, tt_lang.description, queue_meta.meta_value
            LIMIT %d",
            $taxonomy,
            $default_language_term_id,
            $limit,
        );

        $results = $wpdb->get_results( $sql );

        if ( empty( $results ) ) {
            return array();
        }

        $results = \array_map(
            static fn( $result ) => array(
                'ID'                => $result->ID,
                'translations'      => \maybe_unserialize( $result->translations ),
                'translation_queue' => \maybe_unserialize( $result->translation_queue ),
            ),
            $results,
        );

        // If no specific languages requested, return all IDs
        if ( empty( $languages ) ) {
            return \array_column( $results, 'ID' );
        }

        // Initialize array for each requested language
        $language_ids = array();
        foreach ( $languages as $language ) {
            $language_ids[ $language ] = array();
        }

        // Filter and organize results by language
        foreach ( $results as $result ) {
            $translations    = ! empty( $result['translations'] ) ? $result['translations'] : array();
            $queue           = ! empty( $result['translation_queue'] ) ? $result['translation_queue'] : array();
            $queue_languages = \array_keys( $queue );

            foreach ( $languages as $language ) {
                if ( isset( $translations[ $language ] ) || ( ! \in_array( $language, $queue_languages ) && ! empty( $queue ) ) ) {
                    continue;
                }

                $language_ids[ $language ][] = (int) $result['ID'];
            }
        }
        return $language_ids;
    }

    public function get_all_ids( string $taxonomy ): array {
        $args = array(
            'fields'     => 'ids',
            'hide_empty' => false,
            'lang'       => $this->config->get_default_language(),
            'meta_query' => array(
                'compare' => 'NOT EXISTS',
                'key'     => '_pllat_exclude_from_translation',
            ),
            'number'     => 0,
            'order'      => 'ASC',
            'orderby'    => 'term_id',
            'taxonomy'   => $taxonomy,
        );
        \remove_all_filters( 'pre_get_terms' );
        $ids = \get_terms( $args );
        return ! empty( $ids ) ? $ids : array();
    }

    private function get_default_language_term_id(): int {
        return \pll_default_language( 'term_id' ) + 1;
    }
}

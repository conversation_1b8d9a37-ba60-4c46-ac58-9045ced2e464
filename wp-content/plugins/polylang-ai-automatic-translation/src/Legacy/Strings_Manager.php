<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use PLL_MO;
use PLL_Admin_Strings;
use Translation_Entry;
use PLL_Language;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;

class Strings_Manager {

    const PROCESSED_STRINGS_OPTION = 'pllat_processed_strings';
    const AVAILABLE_STRINGS_OPTION = 'pllat_available_strings';

    private PLL_MO $mo;
    private Language_Manager $languages_manager;
    private array $processed_strings = [];

    public function __construct(Language_Manager $languages_manager) {
        $this->mo = new PLL_MO();
        $this->languages_manager = $languages_manager;
        $this->processed_strings = $this->get_processed_strings();
    }

    /**
     * Returns all available strings from a copy of PLL_Admin_Strings::get_strings
     * 
     * Can be filtered by group or returned as a list of strings only
     * 
     * @param bool $string_only (optional) Return only the strings
     * @param string $group (optional) Filter by group
     * @return array
     */
    public function get_strings(bool $string_only = false, string $group = '') : array {
        $strings = $this->get_available_strings();
        if (!empty($group)) {
            $strings = array_filter($strings, function($string) use ($group) {
                return $string['context'] === $group;
            });
        }
        return $string_only ? wp_list_pluck($strings, 'string') : $strings;
    }

    /**
     * Updates a string MO style in the database
     * 
     * @param string $string_key The key of the string to update
     * @param PLL_Language $language The language object
     * @param string $string_value The new value for the string
     */
    public function update_string(string $string_key, PLL_Language $language, string $string_value) {
        $this->mo->import_from_db($language);
        $this->mo->add_entry($this->mo->make_entry($string_key, $string_value));
        $this->mo->export_to_db($language);
    }

    /**
     * Retrieves available translations for a given string in an array of language slugs
     * 
     * @param string $string The string to get translations for
     * @param string $group (optional) Filter by group
     * @return array
     */
    public function get_string_translations(string $string, string $group = '') {
        $languages = [];
        $languages_list = $this->languages_manager->get_languages_list();
        if (empty($languages_list)) {
            return [];
        }
        foreach ($languages_list as $language) {
            $this->mo->import_from_db($language);
            $args['singular'] = $string;
            if (!empty($group)) {
                $args['context'] = $group;
            }
            $translation_entry = new Translation_Entry($args);
            $entry = $this->mo->translate_entry($translation_entry);
            if (!empty($entry->translations[0])) {
                $languages[$language->slug] = $entry->translations[0];
            }
        }
        return $languages;
    }

    /**
     * Retrieves all available groups from the strings
     * 
     * @return array
     */
    public function get_string_groups() : array {
        $strings = $this->get_strings();
        if (empty($strings)) {
            return [];
        }
        return array_values(array_unique(array_column($strings, 'context')));
    }

    /**
     * Retrieves all untranslated strings for a given group and language
     * 
     * @param string $group The group to get untranslated strings for
     * @param string $language The language slug
     * @return array
     */
    public function get_untranslated_strings(string $group, string $language) {
        $strings = $this->get_strings(true, $group);
        if (empty($strings)) {
            return [];
        }
        $untranslated_strings = [];
        foreach ($strings as $string) {
            $translations = $this->get_string_translations($string, $group);
            if (!$this->is_processed($string, $group, $language) && !isset($translations[$language])) {
                $untranslated_strings[] = $string;
            }
        }
        return $untranslated_strings;
    }

    /**
     * Translates a list of strings using a translator
     * 
     * @param Translator $translator The translator object
     * @param array $strings The strings to translate
     * @param string $group The group of the strings
     * @param string $language The language slug
     * @param array $args (optional) Additional arguments
     * @return array
     */
    public function translate_strings(Translator $translator, array $strings, string $group, string $language, array $args = []) {
        $hashed_strings = [];
        foreach ($strings as $string) {
            $hash = $this->generate_string_hash($string, $group, $language);
            $hashed_strings[$hash] = $string;
        }

        $placeholders = [
            'target_language' => $language
        ];
        $translated_strings = $translator->translate_hashed_strings_list($hashed_strings, $placeholders);
        if (empty($translated_strings)) {
            return [];
        }

        $results = [];
        foreach ($translated_strings as $hash => $translated_string) {
            $results[] = [
                'string_key' => $hashed_strings[$hash],
                'translation' => $translated_string,
                'group' => $group,
                'language' => $language,
            ];
        }
        return $results;
    }

    /**
     * Generates a hash for a string based on the string, group, and language
     * 
     * @param string $string The string to hash
     * @param string $group The group of the string
     * @param string $language The language slug
     * @return string
     */
    public function generate_string_hash(string $string, string $group, string $language) {
        return md5($string . $group . $language);
    }

    /**
     * Marks a string as processed so it won't be translated again
     * 
     * @param string $string The string to mark as processed
     * @param string $group The group of the string
     * @param string $language The language slug
     */
    public function mark_as_processed(string $string, string $group, string $language) {
        $hash = $this->generate_string_hash($string, $group, $language);
        $this->update_processed_strings([...$this->processed_strings, $hash]);
    }

    /**
     * Checks if a string has been processed
     * 
     * @param string $string The string to check
     * @param string $group The group of the string
     * @param string $language The language slug
     * @return bool
     */
    public function is_processed(string $string, string $group, string $language) {
        $hash = $this->generate_string_hash($string, $group, $language);
        return in_array($hash, $this->processed_strings);
    }

    /**
     * Retrieves all processed strings
     * 
     * @return array
     */
    public function get_processed_strings() {
        return get_option(self::PROCESSED_STRINGS_OPTION) ?: [];
    }

    /**
     * Updates the processed strings
     * 
     * @param array $processed_strings The processed strings to update
     */
    public function update_processed_strings(array $processed_strings) {
        $this->processed_strings = array_unique($processed_strings);
        update_option(self::PROCESSED_STRINGS_OPTION, $this->processed_strings);
    }

    /**
     * Adds a processed string to the list
     * 
     * @param string $hash The hash of the string to add
     */
    public function add_processed_string(string $hash) {
        $this->update_processed_strings([...$this->processed_strings, $hash]);
    }

    /**
     * Removes a processed string from the list
     * 
     * @param string $hash The hash of the string to remove
     */
    public function remove_processed_string(string $hash) {
        $this->update_processed_strings(array_diff($this->processed_strings, [$hash]));
    }

    /**
     * Stores a copy of PLL_Admin_Strings::get_strings
     * 
     * This method is critical otherwise not all strings are available during ajax and non admin requests
     */
    public function update_available_strings(array $strings) {
        update_option(self::AVAILABLE_STRINGS_OPTION, $strings);
    }

    /**
     * Retrieves a copy of PLL_Admin_Strings::get_strings
     * 
     * This method is critical otherwise not all strings are available during ajax and non admin requests
     */
    public function get_available_strings() {
        return get_option(self::AVAILABLE_STRINGS_OPTION) ?: [];
    }
}

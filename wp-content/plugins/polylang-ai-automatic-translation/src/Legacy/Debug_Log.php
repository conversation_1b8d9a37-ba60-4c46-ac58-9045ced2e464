<?php
declare(strict_types=1);

namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use ErrorException;

/**
 * <PERSON>les writing to, reading from, and clearing the debug log file.
 */
class Debug_Log {
    private const LOG_FILENAME  = 'debug.log';
    private const ALLOWED_TYPES = array( 'error', 'warning', 'info', 'success' );
    private const MAX_READ_SIZE = 20 * 1024; // Max bytes to read from end of file (20KB)

    /**
     * Writes a message to the debug log file.
     *
     * @param string       $message The message to log.
     * @param string       $type    The type of log entry (e.g., 'info', 'error'). Defaults to 'info'.
     * @param array<mixed> $context Optional context data.
     */
    public static function write( string $message, string $type = 'info', array $context = array() ): void {
        try {
            $log_file = self::prepare_log_file();
        } catch ( \ErrorException $e ) {
            \error_log( 'Debug Log Write Error: ' . $e->getMessage() );
            return;
        }

        if ( ! \in_array( $type, self::ALLOWED_TYPES, true ) ) {
            $type = 'info';
        }

        // Format the message
        $timestamp = \gmdate( 'd-M-Y H:i:s' ) . ' UTC'; // Use UTC time
        $log_entry = '[' . $timestamp . '] [' . \strtoupper( $type ) . '] ' . $message . PHP_EOL;
        if ( array() !== $context ) {
            $log_entry .= '[Context] ' . \print_r( $context, true ) . PHP_EOL;
        }

        // Append to the log file
        // Use error suppression only if essential, logging handled in prepare_log_file
        \file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );
    }

    /**
     * Reads the last part of the debug log file.
     *
     * @return string The log content, potentially truncated, or an empty string if not found/empty/unreadable.
     */
    public static function read(): string {
        $log_file = self::get_log_file_path();
        if ( null === $log_file || ! \is_readable( $log_file ) ) {
            return '';
        }

        $file_size = @\filesize( $log_file );
        if ( false === $file_size || 0 === $file_size ) {
            return ''; // Error getting size or empty file
        }

        // Read only the last part
        $offset  = \max( 0, $file_size - self::MAX_READ_SIZE );
        $content = @\file_get_contents( $log_file, false, null, $offset );

        if ( false === $content ) {
            \error_log( 'Failed to read debug log file: ' . $log_file );
            return '';
        }

        return $offset > 0 ? "... (truncated)\n" . $content : $content;
    }

    /**
     * Clears the debug log file.
     *
     * @return bool True on success, false on failure.
     */
    public static function clear(): bool {
        $log_file = self::get_log_file_path();
        if ( null === $log_file || ! \is_writable( $log_file ) ) {
            if ( null !== $log_file ) {
                \error_log( 'Debug log file is not writable, cannot clear: ' . $log_file );
            }
            return false;
        }

        $cleared = @\file_put_contents( $log_file, '' );

        if ( false === $cleared ) {
            \error_log( 'Failed to clear debug log file: ' . $log_file );
        }

        return false !== $cleared;
    }

    /**
     * Checks if the log file exists and is readable.
     *
     * @return bool
     */
    public static function log_exists(): bool {
        $log_file = self::get_log_file_path();
        return null !== $log_file && \is_readable( $log_file );
    }

    /**
     * Gets the size of the log file in bytes.
     *
     * @return int|null Size in bytes, or null if file doesn't exist/is unreadable/error.
     */
    public static function get_log_size(): ?int {
        $log_file = self::get_log_file_path(); // Get path, don't force creation
        if ( null === $log_file || ! \is_readable( $log_file ) ) {
            return null;
        }

        $size = @\filesize( $log_file );

        return false === $size ? null : $size;
    }

    /**
     * Gets the configured log directory path.
     *
     * @return string Log directory path.
     * @throws ErrorException If constant PLLAT_PLUGIN_LOG_DIR is not defined.
     */
    private static function get_log_directory(): string {
        if ( ! \defined( 'PLLAT_PLUGIN_LOG_DIR' ) ) {
            throw new \ErrorException( 'PLLAT_PLUGIN_LOG_DIR constant is not defined.' );
        }
        return PLLAT_PLUGIN_LOG_DIR;
    }

    /**
     * Gets the full path to the log file.
     * Returns null if the directory constant isn't defined or directory doesn't exist.
     *
     * @return string|null Path to the log file, or null if directory is invalid.
     */
    private static function get_log_file_path(): ?string {
        try {
            $log_dir = self::get_log_directory();
        } catch ( \ErrorException $e ) {
            \error_log( 'Debug Log Path Error: ' . $e->getMessage() );
            return null;
        }
        // Check if dir exists here simplifies logic elsewhere
        if ( ! \is_dir( $log_dir ) ) {
            // Only log error if directory truly doesn't exist
            if ( ! @\file_exists( $log_dir ) ) { // Suppress potential open_basedir warning
                \error_log( 'Debug log directory does not exist: ' . $log_dir );
            }
            return null;
        }
        return $log_dir . '/' . self::LOG_FILENAME;
    }

    /**
     * Ensures the log directory and file are ready for writing.
     *
     * @return string Path to the log file.
     * @throws ErrorException If directory or file cannot be created or made writable.
     */
    private static function prepare_log_file(): string {
        $log_dir  = self::get_log_directory(); // Throws if constant not defined
        $log_file = $log_dir . '/' . self::LOG_FILENAME;

        // Ensure directory exists and is writable
        if ( ! \is_dir( $log_dir ) ) {
            $created = @\mkdir( $log_dir, 0755, true );
            if ( ! $created && ! \is_dir( $log_dir ) ) {
                throw new \ErrorException( 'Failed to create log directory: ' . $log_dir );
            }
            @\file_put_contents( $log_dir . '/.htaccess', "Deny from all\n" );
            @\file_put_contents( $log_dir . '/index.php', "<?php\n// Silence is golden." );
        }

        if ( ! \is_writable( $log_dir ) ) {
            throw new \ErrorException( 'Log directory is not writable: ' . $log_dir );
        }

        // Ensure file exists and is writable
        if ( ! \file_exists( $log_file ) ) {
            if ( false === @\touch( $log_file ) || false === @\chmod( $log_file, 0644 ) ) {
                throw new \ErrorException( 'Failed to create log file: ' . $log_file );
            }
        }

        if ( ! \is_writable( $log_file ) ) {
            throw new \ErrorException( 'Log file is not writable: ' . $log_file );
        }

        return $log_file;
    }
}

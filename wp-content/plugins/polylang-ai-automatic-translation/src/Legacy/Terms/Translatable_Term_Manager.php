<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Terms;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity_Queue_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translatable_Entity_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Constants;
use WP_Term;

class Translatable_Term_Manager extends Translatable_Entity_Manager {

    public function __construct(Translatable_Entity $entity, Translatable_Entity_Queue_Manager $queue_manager, Translator $translator, Language_Manager $language_manager) {
        parent::__construct($entity, $queue_manager, $translator, $language_manager);
    }

    public function process_queue_item(string $language, array $args = []) {

        // Ensure parent translation before processing the current term
        $parent_id = $this->ensure_parent_translation($language);

        // Determine if we're updating an existing translation or creating a new one
        $updating_id = $this->entity->get_translation_by_language($language);
        $updating = (bool) $updating_id;

        // Field keys can be set virtually in bulk force mode
        if (!empty($args['force_mode'])) {
            $field_keys = $this->queue_manager->get_available_fields();
        } else {
            $field_keys = $this->entity->get_queue_for_language($language);
        }

        $translation_input = $this->prepare_translation_input($language, $field_keys, $args);

        // Prepares placeholders for the prompt containing additional instructions and website context
        $additional_placeholders = $this->prepare_additional_placeholders($args);

        if (!empty($translation_input)) {

            $translation = $this->translate_to($language, $translation_input, $additional_placeholders);
            $term_arr = $translation->get_all();
            if (!empty($parent_id)) {
                $term_arr['parent'] = $parent_id;
            }

            if (!$updating) {
                $updating_id = $this->copy($language, $term_arr);
                $this->language_manager->set_term_language($updating_id, $language);
            }
            
            $translatable_term = new Translatable_Term($updating_id, $this->language_manager);
            $term_manager = new self(
                $translatable_term, 
                $this->queue_manager, 
                $this->translator, 
                $this->language_manager
            );

            if ($updating) {
                $updated_id = $term_manager->update($term_arr);
            } else {
                $updated_id = $updating_id;
            }
                        
            do_action(
                'pllat_term_translated', 
                $updated_id, 
                $this->entity->get_id(), 
                $language, 
                $this->translator
            );
        } else {
            $term_manager = $this;
            $updated_id = $updating_id;
        }

        // Translate term meta
        if (!empty($args['force_mode'])) {
            $meta_keys = $this->queue_manager->get_available_meta_fields();
        } else {
            $meta_keys = $this->entity->get_meta_queue_for_language($language);
        }

        $meta_translation_input = $this->prepare_meta_translation_input($language, $meta_keys, $updating);
        if (!empty($meta_translation_input)) {
            $meta_translation = $this->translate_meta_to($language, $meta_translation_input, $additional_placeholders);
            $meta_translations = $meta_translation->get_all();
            if (!empty($meta_translations)) {
                foreach ($meta_translations as $key => $value) {
                    update_term_meta($updated_id, $key, $value);
                }
                do_action(
                    'pllat_term_meta_translated', 
                    $updated_id, 
                    $this->entity->get_id(), 
                    $language, 
                    $this->translator
                );
            }
        }

        do_action(
            'pllat_queue_term_before_translation_processed', 
            $this->entity->get_id(), 
            $updated_id, 
            $this->entity->get_language(), 
            $language,
            $args
        );

        $this->update_translations([$language => $updated_id]);
        $this->queue_manager->remove_from_queue($language);
        $this->mark_processed();
        $term_manager->mark_processed();
    
        do_action(
            'pllat_queue_term_translation_processed', 
            $this->entity->get_id(), 
            $updated_id, 
            $language
        );
    }

    protected function translate_to(string $target_language, array $translation_input = [], array $placeholders = []): Translation {
        $source_language = $this->entity->get_language();
        return $this->translator->translate_term(
            $source_language, 
            $target_language, 
            $translation_input,
            $placeholders
        );
    }

    protected function translate_meta_to(string $target_language, array $translation_input = [], array $placeholders = []): Term_Meta_Translation {
        $source_language = $this->entity->get_language();
        return $this->translator->translate_term_meta(
            $source_language, 
            $target_language, 
            $translation_input,
            $placeholders
        );
    }

    protected function prepare_translation_input(string $language, array $field_keys = [], array $args = []): array {
        $translation_input = [];
        $term_object = get_term($this->entity->get_id());
        if ($term_object && !is_wp_error($term_object)) {
            $source_array = get_object_vars($term_object);
            foreach ($field_keys as $index => $field_key) {
                if ($index !== Translatable_Entity::QUEUE_META_FIELD_KEY && !empty($source_array[$field_key])) {
                    $translation_input[$field_key] = $source_array[$field_key];
                }
            }
            $available_fields = Term_Translation::get_available_fields_for($this->entity->get_id());
            $translation_input = array_intersect_key($translation_input, array_flip($available_fields));
        }
        return apply_filters('pllat_term_translation_input', $translation_input, $this->entity, $language, $args);
    }

    protected function prepare_meta_translation_input(string $language, array $meta_keys = []): array {
        $translation_input = [];
        $source_array = Helpers::get_flatten_term_meta($this->entity->get_id());
        foreach ($meta_keys as $meta_key) {
            if (empty($source_array[$meta_key])) continue;
            $translation_input[$meta_key] = $source_array[$meta_key];
        }
        $available_fields = Term_Meta_Translation::get_available_fields_for($this->entity->get_id());
        $translation_input = array_intersect_key($translation_input, array_flip($available_fields));
        return apply_filters('pllat_term_meta_translation_input', $translation_input, $this->entity, $language);
    }

    protected function update(array $term_arr = []) {

        $term_id = $this->entity->get_id();
        if (empty($term_arr)) return $term_id;
        
        $term = get_term($term_id);
        if (is_wp_error($term)) {
            throw new \Exception($term->get_error_message());
        }
        if (!empty($term_arr['slug'])) {
            $term_arr['slug'] = wp_unique_term_slug($term_arr['slug'], $term);
        }
        $updated_term = wp_update_term($term_id, $term->taxonomy, $term_arr);
        if (!is_wp_error($updated_term)) {
            return $updated_term['term_id'];
        }
    }

    protected function copy(string $language, array $term_arr = []) {

        $term = get_term($this->entity->get_id());  
        if (is_wp_error($term)) {
            throw new \Exception($term->get_error_message());
        }

        // Make sure there is a unique slug
        if (empty($term_arr['slug'])) {
            $base_slug = $term->slug;
        } else {
            $base_slug = $term_arr['slug'];
        }
        
        // Add language code to make slug unique
        $term_arr['slug'] = $base_slug . '-' . $language;

        // If term with this slug exists, make it unique
        $term_arr['slug'] = wp_unique_term_slug($term_arr['slug'], $term);

        $new_term = wp_insert_term($term_arr['name'], $term->taxonomy, $term_arr);

        if (is_wp_error($new_term)) {
            throw new \Exception($new_term->get_error_message());
        }

        $new_term_id = $new_term['term_id'];

        // Copy term meta, excluding any fields that should not be copied
        $term_meta = get_term_meta($this->entity->get_id());
        $excluded_meta = Constants::get_excluded_copy_meta_fields();
        
        foreach ($term_meta as $meta_key => $meta_values) {
            if (in_array($meta_key, $excluded_meta)) continue;
            foreach ($meta_values as $meta_value) {
                add_term_meta($new_term_id, $meta_key, $meta_value);
            }
        }
        return $new_term_id;
    }

    protected function update_meta($id, $meta_key, $meta_value) {
        update_term_meta($id, $meta_key, $meta_value);
    }

    protected function delete_meta($id, $meta_key) {
        delete_term_meta($id, $meta_key);
    }

    protected function save_entity_translations(array $translations) {
        $this->language_manager->save_term_translations($translations);
    }

    public function ensure_parent_translation(string $target_language) {

        $current_language = $this->entity->get_language();
        $main_language = $this->language_manager->get_default_language();

        // Get the parent of the current term
        $current_term_parent_id = wp_get_term_taxonomy_parent_id($this->entity->get_id(), $this->entity->get_taxonomy());

        // If there's no parent, we're done
        if (!$current_term_parent_id) 
            return;

        // Get translations of the parent term
        $parent_translations = $this->language_manager->get_term_translations($current_term_parent_id);

        // If the parent is not translated to the target language, translate it
        if (empty($parent_translations[$target_language])) {

            // Use the current language as the source for translation
            $source_parent_id = $current_term_parent_id;

            $parent_term_manager = Helpers::get_translatable_term_manager($source_parent_id);
            $parent_term_manager->populate_queue();
            $parent_term_manager->tidy_queue();      
            $parent_term_manager->process_queue_item($target_language);
            return $parent_term_manager->entity->get_translation_by_language($target_language);
        }
    }
}
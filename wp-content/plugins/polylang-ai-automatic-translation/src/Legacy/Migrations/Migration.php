<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Migrations;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;

class Migration implements Migration_Interface {

    protected $language_manager;

    public function __construct() {
        $this->language_manager = new Language_Manager();
    }

    protected function get_all_post_ids() {
        $args = [
            'numberposts' => -1,
            'post_type' => 'any',
            'post_status' => 'any',
            'fields' => 'ids',
        ];

        remove_all_filters('pre_get_posts');

        $posts = get_posts($args);
        return $posts;
    }

    protected function get_all_terms_ids() {
        $args = [
            'taxonomy' => 'any',
            'hide_empty' => false,
            'fields' => 'ids',
        ];

        remove_all_filters('pre_get_terms');

        $terms = get_terms($args);
        if (is_wp_error($terms)) {
            return [];
        }
        return $terms;
    }

    public function run() {
    }
}
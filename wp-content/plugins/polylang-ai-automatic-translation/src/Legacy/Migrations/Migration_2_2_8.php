<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Migrations;

use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;

class Migration_2_2_8 extends Migration {

    public function run() {

        $post_ids = $this->get_all_post_ids();

        if (!empty($post_ids)) {

            foreach ($post_ids as $post_id) {

                $post_meta = Helpers::get_flatten_post_meta($post_id);

                if (isset($post_meta['pllat_exclude_from_translation'])) {
                    update_post_meta($post_id, '_pllat_exclude_from_translation', $post_meta['pllat_exclude_from_translation']);
                    delete_post_meta($post_id, 'pllat_exclude_from_translation');
                }

                if (isset($post_meta['pllat_is_processed'])) {
                    update_post_meta($post_id, '_pllat_last_processed', $post_meta['pllat_is_processed']);
                    delete_post_meta($post_id, 'pllat_is_processed');
                }

                if (isset($post_meta['_pllat_changed_post_data'])) {
                    $changed_post_data = maybe_unserialize($post_meta['_pllat_changed_post_data']);
                    delete_post_meta($post_id, '_pllat_changed_post_data');
                }

                if (isset($post_meta['_pllat_changed_post_meta_data'])) {
                    $changed_post_meta_data = maybe_unserialize($post_meta['_pllat_changed_post_meta_data']);
                    delete_post_meta($post_id, '_pllat_changed_post_meta_data');
                }

                if (isset($post_meta['pllat_translation_queue'])) {
                    $translation_queue = maybe_unserialize($post_meta['pllat_translation_queue']);
                    if (!empty($translation_queue)) {
                        $post_manager = Helpers::get_translatable_post_manager($post_id);
                        $available_languages = $this->language_manager->get_available_languages(true);

                        if (!empty($changed_post_data)) {
                            foreach ($available_languages as $language) {
                                $post_manager->add_to_queue($language, $changed_post_data);
                            }
                        }
                        if (!empty($changed_post_meta_data)) {
                            foreach ($available_languages as $language) {
                                $post_manager->add_meta_to_queue($language, $changed_post_meta_data);
                            }
                        }
                    }
                    delete_post_meta($post_id, 'pllat_translation_queue');
                }
            }
        }

        $term_ids = $this->get_all_terms_ids();

        if (!empty($term_ids)) {

            foreach ($term_ids as $term_id) {

                $term_meta = Helpers::get_flatten_term_meta($term_id);

                if (isset($term_meta['pllat_exclude_from_translation'])) {
                    update_term_meta($term_id, '_pllat_exclude_from_translation', $term_meta['pllat_exclude_from_translation']);
                    delete_term_meta($term_id, 'pllat_exclude_from_translation');
                }

                if (isset($term_meta['pllat_is_processed'])) {
                    update_term_meta($term_id, '_pllat_last_processed', $term_meta['pllat_is_processed']);
                    delete_term_meta($term_id, 'pllat_is_processed');
                }

                if (isset($term_meta['pllat_changed_data'])) {
                    $changed_term_data = maybe_unserialize($term_meta['pllat_changed_data']);
                    delete_term_meta($term_id, 'pllat_changed_data');
                }

                if (isset($term_meta['pllat_translation_queue'])) {
                    $translation_queue = maybe_unserialize($term_meta['pllat_translation_queue']);
                    if (!empty($translation_queue)) {
                        $term_manager = Helpers::get_translatable_term_manager($term_id);
                        $available_languages = $this->language_manager->get_available_languages(true);
                        if (!empty($changed_term_data)) {
                            foreach ($available_languages as $language) {
                                $term_manager->add_to_queue($language, $changed_term_data);
                            }
                        }
                    }
                    delete_term_meta($term_id, 'pllat_translation_queue');
                }
            }
        }

    }
}
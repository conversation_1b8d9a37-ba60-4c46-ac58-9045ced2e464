<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

/**
 * Decorates waiting messages with fun, random suggestions
 * 
 * @since 1.0.0
 */
class Waiting_Message_Decorator {
    /**
     * Array of fun suggestions to append to waiting messages
     *
     * @var array
     */
    private $fun_suggestions = [];

    /**
     * Constructor
     */
    public function __construct() {
        $this->fun_suggestions = [
            pll__('Take some time to grab a cup of coffee.'),
            pll__('Perfect time to stretch your legs.'),
            pll__('Maybe a good moment to fill your water bottle?'),
            pll__('This is a good moment to check your posture.'),
            pll__('A good moment to take a deep breath!'),
            pll__('This is a great time to pet your cat (or dog).'),
            pll__('A good moment to close your eyes and count to 10. It helps with eye strain!'),        
        ];
    }

    /**
     * Get a random fun suggestion
     *
     * @return string Random suggestion
     */
    public function get_random_suggestion() {
        $index = array_rand($this->fun_suggestions);
        return $this->fun_suggestions[$index];
    }

    /**
     * Decorate a waiting message with a fun suggestion
     *
     * @param string $message Original waiting message
     * @return string Decorated message
     */
    public function decorate_message($message) {
        return $message . ' <strong><i>' . $this->get_random_suggestion() . '</i></strong>';
    }
} 
<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor;

abstract class Abstract_Content_String_Editor {
    /**
     * Check if the string is a shortcode
     *
     * @param string $string The string to check
     * @return bool True if the string is a shortcode, false otherwise
     */
    protected function is_shortcode( string $string ): bool {
        // Check if the string starts with a shortcode pattern
        if ( \preg_match( '/^\[\/?[a-zA-Z0-9_-]+(\s+[^\]]+)?/', $string ) ) {
            return true;
        }

        // Use WordPress's shortcode detection if available
        if ( \function_exists( 'get_shortcode_regex' ) ) {
            $shortcode_regex = \get_shortcode_regex();
            // Check for strings that start with a shortcode pattern
            if ( \preg_match( '/^\[(' . $shortcode_regex . ')/', $string ) ) {
                return true;
            }
        }
        return false;
    }

    /**
     * Replace a string while preserving its original whitespace
     *
     * @param string $text The text to replace
     * @param string $replacement The replacement string
     * @return string The replaced string
     */
    protected function replace_string( string $text, string $replacement ): string {
        // Preserve leading and trailing whitespace from the original text
        $leading  = '';
        $trailing = '';

        // Extract leading whitespace
        if ( ' ' === \substr( $text, 0, 1 ) ) {
            $leading = ' ';
        }

        // Extract trailing whitespace
        if ( ' ' === \substr( $text, -1 ) ) {
            $trailing = ' ';
        }

        // Apply the whitespace to the replacement
        return $leading . $replacement . $trailing;
    }

    /**
     * Check if the string is valid for extraction
     *
     * @param string $string The string to check
     * @return bool True if the string is valid, false otherwise
     */
    protected function is_valid_string( string $string ): bool {
        $trimmed = \trim( $string );

        // Exclude shortcodes, we handle this somewhere else
        if ( $this->is_shortcode( $trimmed ) ) {
            return false;
        }

        if ( \in_array( $trimmed, $this->get_excluded_strings(), true ) ) {
            return false;
        }

        return '' !== $trimmed && $this->is_min_length( $trimmed );
    }

    protected function is_min_length( string $string ): bool {
        return \strlen( $string ) > 3;
    }

    protected function get_excluded_strings(): array {
        return array( '&nbsp;' );
    }
}

<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor;

/**
 * Handles the extraction and replacement of translatable strings in Gutenberg blocks
 */
class WP_Block_Content_String_Editor extends Abstract_Content_String_Editor {
    /**
     * Array of keys that are allowed to be translated
     *
     * @var array
     */
    private array $translatable_keys;

    /**
     * Array of strings found in the content
     *
     * @var array<int, string>
     */
    private array $extracted_strings = array();

    /**
     * Array of strings found in the content
     *
     * @var array<int, string>
     */
    private array $string_replacements = array();

    /**
     * Constructor
     *
     * @param array $translatable_keys Array of keys that are allowed to be translated
     */
    public function __construct( array $translatable_keys = array() ) {
        $this->translatable_keys = $translatable_keys;
    }

    /**
     * @return array<int, string>
     */
    public function get_extracted_strings(): array {
        return \array_values( \array_unique( $this->extracted_strings ) );
    }

    /**
     * @return array<int, string>
     */
    public function get_string_replacements(): array {
        return $this->string_replacements;
    }

    /**
     * Checks if a key is allowed to be translated
     *
     * @param string $key The key to check
     * @return bool True if the key is allowed to be translated, false otherwise
     */
    private function is_translatable_key( string $key ): bool {
        // When keys contain these words, they are likely to contain translatable text
        $whitelisted_indicators = array(
            'text',
            'title',
            'heading',
            'content',
            'description',
            'author',
            'name',
            'subtitle',
            'caption',
            'excerpt',
            'quote',
            'label',
            'text',
            'alt',
            'value',
            'content',
        );

        // Keys that are not translatable without a doubt (exact match, case sensitive)
        $blacklisted_keys = array(
            'name',          // Usually used for internal identifiers
            'type',          // Block type identifiers
            'className',     // CSS classes
            'anchor',        // HTML anchors
            'rel',           // Link attributes
            'linkTarget',    // Link targets
            'style',         // CSS styles
            'id',            // HTML/DOM identifiers
            'blockName',     // Internal block identifiers
            'tagName',       // HTML element type
            'namespace',     // Block namespace
            'align',         // Block alignment
            'mode',          // Block display mode
            'level',         // Heading levels etc
            'lock',          // Block locking settings
            'ref',           // React/DOM references
            'role',          // ARIA roles
            'method',        // Form methods
            'action',        // Form actions
            'target',        // Link/form targets
            'class',         // CSS classes (alternative to className)
            'isValid',       // Block validation status
            'templateLock',  // Template locking
            'version',        // Block version
        );

        // Indicators for keys that are not likely to contain translatable text
        $blacklisted_indicators = array(
            'align',        // alignment attributes
            'position',     // positioning
            'order',        // ordering
            'width',        // dimensions
            'height',
            'size',
            'spacing',      // layout spacing
            'padding',      // CSS-like properties
            'margin',
            'border',
            'color',        // styling
            'background',
            'justify',
            'layout',       // layout configurations
            'orientation',  // display orientation
            'direction',    // text/layout direction
            'style',        // styling variations
            'flex',         // flexbox properties
            'grid',         // grid properties
            'gap',          // spacing between items
            'offset',       // positioning offsets
            'columns',      // column count/layout
            'url',          // links/media sources
            'link',         // link properties
            'href',          // link destinations
            'file',
        );

        // Skip WordPress meta fields (should be checked early)
        if ( \str_starts_with( $key, '_' ) ) {
            return false;
        }

        // Keys that are not translatable without a doubt (exact match, case sensitive)
        if ( \in_array( $key, $blacklisted_keys ) ) {
            return false;
        }

        // Matches keys that contain indicators that are not likely to contain translatable text
        foreach ( $blacklisted_indicators as $indicator ) {
            if ( false !== \stripos( \strtolower( $key ), \strtolower( $indicator ) ) ) {
                return false;
            }
        }

        // Keys that are explicitly set to be translatable (exact match, case sensitive)
        if ( \in_array( $key, $this->translatable_keys ) ) {
            return true;
        }

        // Matches keys that are very likely to contain translatable text (case insensitive)
        foreach ( $whitelisted_indicators as $indicator ) {
            if ( false !== \stripos( \strtolower( $key ), \strtolower( $indicator ) ) ) {
                return true;
            }
        }
        return false;
    }

    public function extract_strings( string $content ): array {
        $blocks = \parse_blocks( $content );
        $this->extract_strings_from_blocks( $blocks );
        return $this->get_extracted_strings();
    }

    public function replace_strings( string $content, array $search_replace_pairs ): string {
        if ( empty( $content ) || empty( $search_replace_pairs ) ) {
            return $content;
        }

        $translations_lookup = array();
        foreach ( $search_replace_pairs as $pair ) {
            $translations_lookup[ $pair['search'] ] = $pair['replace'];
        }

        $blocks = \parse_blocks( $content );
        $blocks = $this->replace_strings_in_blocks( $blocks, $translations_lookup );

        return \serialize_blocks( $blocks );
    }

    private function extract_strings_from_blocks( array $blocks ): void {
        foreach ( $blocks as $block ) {
            if ( ! empty( $block['attrs'] ) ) {
                $this->extract_strings_recursive( $block['attrs'] );
            }

            // Handle inner blocks recursively
            if ( empty( $block['innerBlocks'] ) ) {
                continue;
            }

            $this->extract_strings_from_blocks( $block['innerBlocks'] );
        }
    }

    private function replace_strings_in_blocks( array $blocks, array $translations_lookup ): array {
        foreach ( $blocks as &$block ) {
            if ( ! empty( $block['attrs'] ) ) {
                $this->replace_strings_recursive( $block['attrs'], $translations_lookup );
            }

            if ( empty( $block['innerBlocks'] ) ) {
                continue;
            }

            $block['innerBlocks'] = $this->replace_strings_in_blocks(
                $block['innerBlocks'],
                $translations_lookup,
            );
        }
        return $blocks;
    }

    private function extract_strings_recursive( $data ): void {
        if ( ! \is_object( $data ) && ! \is_array( $data ) ) {
            return;
        }

        foreach ( $data as $key => $value ) {
            if ( $this->is_translatable_key( $key ) && \is_string( $value ) && $this->is_valid_string(
                $value 
            ) ) {
                $this->extracted_strings[] = \wp_unslash( $value );
                continue;
            }

            // Skip non-object/array values
            if ( ! \is_object( $value ) && ! \is_array( $value ) ) {
                continue;
            }

            $this->extract_strings_recursive( $value );
        }
    }

    private function replace_strings_recursive( array &$data, array $translations_lookup ): void {
        foreach ( $data as $key => &$value ) {
            if ( $this->is_translatable_key( $key ) && \is_string( $value ) && $this->is_valid_string( $value ) ) {
                if ( isset( $translations_lookup[ $value ] ) ) {
                    $sanitized                   = $this->sanitize_string( $translations_lookup[ $value ] );
                    $value                       = $this->encode_block_content( $sanitized );
                    $this->string_replacements[] = $value;
                }
                continue;
            }
            if ( ! \is_array( $value ) ) {
                continue;
            }

            $this->replace_strings_recursive( $value, $translations_lookup );
        }
    }

    private function sanitize_string( $string ) {
        if ( ! \is_string( $string ) ) {
            return '';
        }

        // First decode all HTML entities to UTF-8
        $string = \html_entity_decode( $string, ENT_QUOTES | ENT_HTML5, 'UTF-8' );

        // Then normalize the UTF-8 string
        $string = \Normalizer::normalize( $string, \Normalizer::FORM_C );

        return $string;
    }

    private function encode_block_content( $value ) {
        return \trim(
            \wp_json_encode(
                $value,
                JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE,
            ),
            '"',
        );
    }

}

<?php
/**
 * App class file.
 *
 * @package Polylang AI Automatic Translation
 */

namespace PLLAT;

use EPIC_WP\Polylang_Automatic_AI_Translation\Debug_Log;
use SureCart\WP\Surecart_Module;
use XWP\DI\Decorators\Action;
use XWP\DI\Decorators\Module;
use XWP\DI\Interfaces\On_Initialize;

/**
 * Main application class.
 */
#[Module(
    hook: 'pll_init',
    priority: 0,
    imports: array(
        Core\Core_Module::class,
        Surecart_Module::class,
        Admin\Admin_Module::class,
        Status\Status_Module::class,
        Test\Test_Module::class,
        Translator\Translator_Module::class,
        Link\Link_Module::class,
    ),
)]
class App implements On_Initialize {
    /**
     * Can we initialize the module.
     *
     * @return bool
     */
    public static function can_initialize(): bool {
        return ! \pllat_is_pll_deactivating() && \pll_default_language();
    }

    /**
     * Get the module configuration.
     *
     * @return array<string,mixed>
     */
    public static function configure(): array {
        return array(
            'app.includes' => \DI\value(
                array(
                    'action-scheduler',
                    'post-translation',
                    'term-translation',
                    'single-post-translation',
                    'post-translation-changes',
                    'single-term-translation',
                    'term-translation-changes',
                    'menu-translation',
                    'custom-fields',
                    'migrations',
                    'block-editor',
                    'string-translation',
                    'translation',
                ),
            ),
            'app.name'     => \DI\factory(
                static fn() => \__( 'Polylang AI Automatic Translation', 'polylang-ai-autotranslate' ),
            ),

        );
    }

    /**
     * Register error handler for this plugin.
     */
    public function on_initialize(): void {
        /**
         * Fires when the plugin is initialized.
         *
         * @param string $default_language The default language.
         */
        \do_action( 'pllat_loaded', \pll_default_language() );

        //phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_set_error_handler
        \set_error_handler(
            static function ( $level, $error, $file, $line ) {
                $plugin_id = 'polylang-ai-automatic-translation';
                if ( false !== \strpos( $file, $plugin_id ) ) {
                    $message = $error . ' in ' . $file . ' on line ' . $line;
                    Debug_Log::write( $message, 'error' );
                }
                return false;
            },
        );

        \register_shutdown_function(
            static function () {
                $error     = \error_get_last();
                $plugin_id = 'polylang-ai-automatic-translation';
                if ( ! isset( $error['file'] ) || false === \strpos( $error['file'], $plugin_id ) ) {
                    return;
                }

                $message = $error['message'] . ' in ' . $error['file'] . ' on line ' . $error['line'];
                Debug_Log::write( $message, 'error' );
            },
        );
    }

    // #[Action( tag: 'wp', context: Action::CTX_FRONTEND )]
    public function check(): void {
        /**
         * Rewrite object for the current request.
         *
         * @var \WP_Rewrite $wp_rewrite
         */
        global $wp_rewrite;

        \dump( $wp_rewrite );
        die;
    }

    /**
     * Include files required for the plugin.
     *
     * @param  bool          $licensed  License status.
     * @param  string        $basedir  Base directory of the plugin.
     * @param  array<string> $includes List of files to include.
     */
    #[Action(
        tag: 'init',
        priority: 0,
        invoke: Action::INV_PROXIED,
        args: 0,
        params: array( 'app.licensed', 'app.path', 'app.includes' ),
    )]
    public function include_files( bool $licensed, string $basedir, array $includes ): void {
        if ( ! $licensed ) {
            return;
        }

        $basedir = \untrailingslashit( $basedir );

        foreach ( $includes as $include ) {
            require_once "{$basedir}/includes/{$include}.php";
        }

        if ( \pllat_has_wc() ) {
            require_once "{$basedir}/includes/product-variation-translations.php";
        }

        //phpcs:ignore SlevomatCodingStandard.ControlStructures.EarlyExit.EarlyExitNotUsed
        if ( \pllat_has_elementor() ) {
            require_once "{$basedir}/integrations/elementor.php";
        }
    }
}

<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

class Term_Translation_Handler {

    private static $instance;

    private function __construct() {
        add_action('pllat_term_translation_input', [$this, 'filter_term_translation_input'], 10, 4);
    }

    public function filter_term_translation_input($translation_input, $entity, $language, $args) {
        if (!empty($args['force_mode'])) {
            $translation_input = array_filter($translation_input, fn($key) => $key !== 'slug');
        }
        return $translation_input;
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

Term_Translation_Handler::get_instance();

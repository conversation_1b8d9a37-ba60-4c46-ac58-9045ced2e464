<?php

use EPIC_WP\Polylang_Automatic_AI_Translation\Content_String_Editor\WP_Block_Content_String_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;

/**
 * Handles Gutenberg block translation
 */
class Block_Editor_Handler {
    private static $instance = null;
    private WP_Block_Content_String_Editor $block_editor;

    private function __construct() {
        $this->block_editor = new WP_Block_Content_String_Editor(
            Settings::get_wp_block_translatable_keys(),
        );
        add_filter(
            'pllat_large_content_translation_relevant_strings',
            array( $this, 'extract_wp_block_strings' ),
            10,
            2,
        );
        add_filter(
            'pllat_content_after_large_content_translation',
            array( $this, 'replace_wp_block_strings' ),
            10,
            2,
        );
    }

    public function extract_wp_block_strings( array $strings, string $content ): array {
        $block_strings = $this->block_editor->extract_strings( $content );
        return array_merge( $strings, $block_strings );
    }

    public function replace_wp_block_strings( string $content, array $search_replace_pairs ): string {
        $search_replace_pairs = $this->filter_search_replace_pairs(
            $search_replace_pairs,
            $this->block_editor->get_extracted_strings(),
        );
        return $this->block_editor->replace_strings( $content, $search_replace_pairs );
    }

    /**
     * Filters the search/replace pairs to only include pairs where the search string also exists in the extracted strings
     *
     * @param array $search_replace_pairs The search/replace pairs to filter
     * @param array $extracted_strings The extracted strings
     * @return array The filtered search/replace pairs
     */
    private function filter_search_replace_pairs( array $search_replace_pairs, array $extracted_strings ): array {
        return array_filter(
            $search_replace_pairs,
            static fn( $pair ) => \in_array( $pair['search'], $extracted_strings ),
        );
    }

    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

Block_Editor_Handler::get_instance();

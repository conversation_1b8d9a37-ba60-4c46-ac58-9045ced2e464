<?php
class Action_Scheduler_Handler {
    private static $instance = null;

    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action( 'rest_api_init', array( $this, 'register_rest_endpoint' ) );
    }

    /**
     * Register custom rest endpoint to trigger immediate translation
     *
     * @return void
     */
    public function register_rest_endpoint(): void {
        register_rest_route(
            'pllat/v1',
            '/action-scheduler/trigger',
            array(
                'callback'            => array( $this, 'trigger_action_scheduler' ),
                'methods'             => 'POST',
                'permission_callback' => array( $this, 'is_valid_request' ),
            ),
        );
    }

    /**
     * Trigger single translation
     *
     * @return void
     */
    public function trigger_action_scheduler( \WP_REST_Request $request ): \WP_REST_Response {
        if ( class_exists( 'ActionScheduler_QueueRunner' ) ) {
            error_log( 'Action scheduler triggered' );
            \ActionScheduler_QueueRunner::instance()->run();
        }
        return new \WP_REST_Response( 'Action scheduler triggered', 200 );
    }

    /**
     * Check if the request is valid by verifying the nonce
     *
     * @param \WP_REST_Request $request The request object
     * @return bool Whether the request is valid
     */
    public function is_valid_request( \WP_REST_Request $request ): bool {
        // Allow any logged-in user to trigger action scheduler
        if ( ! is_user_logged_in() ) {
            return false;
        }

        // Verify the REST nonce from the X-WP-Nonce header
        $nonce = $request->get_header( 'X-WP-Nonce' );
        return $nonce && wp_verify_nonce( $nonce, 'wp_rest' );
    }
}

Action_Scheduler_Handler::get_instance();

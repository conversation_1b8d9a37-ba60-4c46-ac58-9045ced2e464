<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Translatable_Term;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Prompt_Handler;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\OpenAI\Token_Encoder;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use EPIC_WP\Polylang_Automatic_AI_Translation\Terms\Term_Translation_Background_Processor;

class Single_Term_Translation_Handler extends Abstract_Single_Translation_Handler {

    private static $instance = null;
    private $background_processor;

    private function __construct() {

        parent::__construct();
        
        $this->background_processor = new Term_Translation_Background_Processor();

        $this->register_ajax_hooks();
        $this->register_hooks();
    }

    private function register_ajax_hooks() {
        add_action('wp_ajax_pllat_queue_term_translations', [$this, 'handle_ajax_queue_term_translations']);
        add_action('wp_ajax_pllat_process_term_translation', [$this, 'handle_ajax_process_term_translation']);
        add_action('wp_ajax_pllat_check_term_translation_status', [$this, 'handle_ajax_check_translation_status']);
        add_action('wp_ajax_pllat_cancel_term_translations', [$this, 'handle_ajax_cancel_term_translations']);
        add_action('wp_ajax_pllat_clear_translation_error', [$this, 'handle_ajax_clear_translation_error']);
        add_action('wp_ajax_pllat_clear_term_translation_error', [$this, 'handle_ajax_clear_translation_error']);
        add_action('wp_ajax_pllat_get_term_translation_errors', [$this, 'handle_ajax_get_translation_errors']);
    }

    private function register_hooks() {
        add_action('admin_init', [$this, 'add_admin_init_hooks']);
        add_action('edited_term', [$this, 'save_term_meta'], 10, 2);
    }

    /**
     * Handles AJAX request to queue term translations
     */
    public function handle_ajax_queue_term_translations() {

        try {
            
            if (empty($_POST['id'])) {
                wp_send_json_error(['message' => pll__('No term ID provided.')]);
                wp_die();
            }
            
            if (empty($_POST['languages']) || !is_array($_POST['languages'])) {
                wp_send_json_error(['message' => pll__('No languages provided.')]);
                wp_die();
            }
            
            $term_id = (int) $_POST['id'];
            $languages = $_POST['languages'];
            
            // Clear any previous translation errors
            $this->background_processor->clear_latest_error($term_id);
            $this->background_processor->clear_translation_timestamps($term_id);
            
            $queued_languages = [];
            $args = [];
            
            // Check whether there are additional ai instructions set
            if (!empty($_POST['instructions'])) {
                $args['additional_instructions'] = sanitize_textarea_field($_POST['instructions']);
            }

            // Check whether there is ai website context set
            $website_context = Settings::get_website_ai_context();
            if (!empty($website_context)) {
                $args['website_context'] = $website_context;
            }

            $term_manager = Helpers::get_translatable_term_manager($term_id);
            $translatable_term = $term_manager->get_translatable_entity();

            if ($translatable_term->is_excluded_from_translation()) {
                wp_send_json([
                    'message' => pll__('Term is excluded from translation so is skipped.')
                ]);
                wp_die();
            }

            $force_mode = isset($_POST['force']) && $_POST['force'] === 'true';
            if ($force_mode) {
                $args['force_mode'] = true;
            }

            // Make sure all missing languages are queued
            $term_manager->populate_queue();

            // Make sure the queue only contains active languages
            $term_manager->tidy_queue();

            // Get only the languages that are in the queue
            $queue_languages = $force_mode ? $languages : $translatable_term->get_queue_languages();

            // Make sure the queue only contains requested languages
            $queue_languages = array_intersect($queue_languages, $languages);

            // Fire action when all languages are queued for a term
            do_action('pllat_single_term_translation_languages_queued', intval($_POST['id']), $queue_languages);
            
            wp_send_json_success([
                'translation_queue' => $queue_languages
            ]);
            
        } catch (\Error $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            wp_send_json_error(['message' => $e->getMessage()]);
        }
        wp_die();
    }

    public function handle_ajax_process_term_translation() {

        try {

            if (empty($_POST['id']) || empty($_POST['entity'])) {
                wp_send_json_error(['message' => pll__('No term ID or taxonomy provided.')]);
                wp_die();
            }
    
            if (empty($_POST['language_slug'])) {
                wp_send_json_error(['message' => pll__('No language slug provided.')]);
                wp_die();
            }
            
            $term_id = $_POST['id'];
            $language_slug = $_POST['language_slug'];
            $args = [];

            // Check whether there are additional ai instructions set
            if (!empty($_POST['instructions'])) {
                $args['additional_instructions'] = sanitize_textarea_field($_POST['instructions']);
            } 

            // Check whether there is ai website context set
            $website_context = Settings::get_website_ai_context();
            if (!empty($website_context)) {
                $args['website_context'] = $website_context;
            }

            // Add force mode to args to manage later in the term manager when processing queue item
            $force_mode = isset($_POST['force']) && $_POST['force'] === 'true';
            if ($force_mode) {
                $args['force_mode'] = true;
            }

            // Use term_action_scheduler to schedule the translation
            $action_id = $this->background_processor->schedule_term_translation(
                $term_id,
                $language_slug,
                $args
            );

            if (!$action_id) {
                throw new \Exception('Failed to schedule translation.');
            }

            wp_send_json_success([
                'scheduled' => $language_slug,
            ]);

        } catch (\Error $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            wp_send_json_error(['message' => $e->getMessage()]);
        }
        wp_die();
    }

    /**
     * Gets the translation context
     * 
     * @return string
     */
    protected function get_translation_context() {
        return 'term';
    }
    
    /**
     * Handles AJAX request to check the translation status
     */
    public function handle_ajax_check_translation_status() {
        
        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No term ID provided.')]);
            wp_die();
        }
        
        $term_id = (int) $_POST['id'];
        $status = $this->background_processor->get_term_translation_status($term_id);
        
        wp_send_json_success([
            'status' => $status['status'],
            'pending_languages' => $status['pending_languages'],
            'processing_languages' => $status['processing_languages'],
            'processing_timestamps' => $status['processing_timestamps'],
            'errors' => $status['errors'],
            'has_error' => !empty($status['errors']),
        ]);
        
        wp_die();
    }

    /**
     * Handles AJAX request to cancel term translations
     */
    public function handle_ajax_cancel_term_translations() {

        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No term ID provided.')]);
            wp_die();
        }
        
        try {
            $term_id = $_POST['id'];
            $cancelled_count = $this->background_processor->cancel_term_translations($term_id);
            
            wp_send_json_success([
                'cancelled_count' => $cancelled_count,
                'message' => sprintf(pll__('Cancelled %d translation tasks.'), $cancelled_count)
            ]);
        } catch (\Exception $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            wp_send_json_error(['message' => $e->getMessage()]);
        } catch (\Throwable $e) {
            Helpers::log($e->getMessage() . ' - ' . $e->getTraceAsString(), 'error');
            wp_send_json_error(['message' => \pll__('The error message is logged in the plugin log file (Admin > Languages > AI Translation > Download log files). Please share with the plugin author.')]);
        }
        wp_die();
    }

    /**
     * Handles AJAX request to clear translation error
     */
    public function handle_ajax_clear_translation_error() {

        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No term ID provided.')]);
            wp_die();
        }
        
        $term_id = (int) $_POST['id'];
        $this->background_processor->clear_latest_error($term_id);
        
        wp_send_json_success(['message' => pll__('Error cleared successfully.')]);
        wp_die();
    }

    /**
     * Handles AJAX request to get translation errors
     */
    public function handle_ajax_get_translation_errors() {
        if (empty($_POST['id'])) {
            wp_send_json_error(['message' => pll__('No term ID provided.')]);
            wp_die();
        }
        
        $term_id = (int) $_POST['id'];
        $error = $this->background_processor->get_latest_error($term_id);
        
        wp_send_json_success([
            'has_error' => !empty($error),
            'error_message' => $error
        ]);
        
        wp_die();
    }

    public function save_term_meta($term_id, $tt_id) {
        $term_manager = Helpers::get_translatable_term_manager($term_id);
        if (!empty($_POST['pllat_exclude_from_translation'])) {
            $term_manager->update_exclude_from_translation(true);
        } else {
            $term_manager->update_exclude_from_translation(false);
        }
    }

    public function add_admin_init_hooks() {
        $taxonomies = Helpers::get_activate_taxonomies();
        foreach ($taxonomies as $taxonomy) {
            add_action($taxonomy . '_edit_form', [$this, 'add_term_translation_metabox'], 20, 2);
        }
        
        // Add action to enqueue admin scripts
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
    }

    public function enqueue_admin_scripts() {
        $screen = get_current_screen();
        if (empty($screen) || !in_array($screen->base, ['term', 'edit-tags'])) {
            return;
        }
        $this->enqueue_translation_admin_scripts();
    }

    /**
     * Add a metabox to the term edit screen
     */
    public function add_term_translation_metabox($term, $taxonomy) {

        $term_id = $term->term_id;
        $translatable_term = new Translatable_Term($term_id, $this->language_manager);
        $queue = $translatable_term->get_queue();

        $is_debug_mode = Settings::is_debug_mode();

        if ($is_debug_mode) {
            $available_fields = Term_Translation::get_available_fields_for($term_id);
            $available_meta_fields = Term_Meta_Translation::get_available_fields_for($term_id);
        }

        $exclude_from_translation = $translatable_term->is_excluded_from_translation();

        $active_translation_api = Settings::get_active_translation_api();
        $active_translation_api_key = Settings::get_active_translation_api_key($active_translation_api);

        $current_language = pll_get_term_language($term_id);
        $available_languages = array_diff($this->language_manager->get_available_languages(), [$current_language]);
        $included_languages = array_keys($queue);
        $all_checked = count($included_languages) === count($available_languages);
        $type = 'term';
        $entity = $taxonomy;
        $id = $term_id;

        include PLLAT_PLUGIN_DIR . 'templates/admin/single-translation.php';
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

Single_Term_Translation_Handler::get_instance();
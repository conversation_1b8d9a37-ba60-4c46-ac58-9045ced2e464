<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use PLL_Admin_Strings;

class String_Translation_Handler {

    private static $instance = null;

    private $strings_manager;

    public function __construct() {

        $this->strings_manager = new Strings_Manager(xwp_app( 'pllat' )->get( Language_Manager::class ));

        if (is_admin() && !wp_doing_ajax()) {
            add_action('wp_loaded', [$this, 'maybe_store_strings']);
        }
        add_action('wp_ajax_test_available_strings', [$this, 'handle_ajax_test_available_strings']);
    }

    /**
     * Copies the strings from PLL_Admin_Strings to make them available in ajax and non-admin requests
     */
    public function maybe_store_strings() {
        $strings = PLL_Admin_Strings::get_strings();
        $this->strings_manager->update_available_strings($strings);
    }

    /**
     * Test whether the available strings are available during ajax requests
     */
    public function handle_ajax_test_available_strings() {  
        $strings = $this->strings_manager->get_available_strings();
        wp_send_json_success($strings);
    }    
    
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}

String_Translation_Handler::get_instance();


<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

class Translation_Handler {

    private static $instance;
    private $language_manager;

    private function __construct() {
        $this->language_manager = xwp_app( 'pllat' )->get( Language_Manager::class );
        add_filter('pllat_translation_placeholders', [$this, 'convert_language_codes'], 10, 3);
    }
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function convert_language_codes($placeholders) {
        if (!empty($placeholders['source_language'])) {
            $language_data = $this->language_manager->get_language_data(strtolower($placeholders['source_language']));
            $placeholders['source_language'] = $language_data['name'] . ' (' . $placeholders['source_language'] . ')';
        }
        if (!empty($placeholders['target_language'])) {
            $language_data = $this->language_manager->get_language_data(strtolower($placeholders['target_language']));
            $placeholders['target_language'] = $language_data['name'] . ' (' . $placeholders['target_language'] . ')';
        }
        return $placeholders;
    }

}

Translation_Handler::get_instance();

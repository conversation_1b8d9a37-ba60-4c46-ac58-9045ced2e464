<?php
namespace EPIC_WP\Polylang_Automatic_AI_Translation;

use EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\ACF\ACF_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Post_Meta_Translation;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translations\Term_Meta_Translation;

class Custom_Fields {

    private static $instance = null;
    private $acf_manager;

    public function __construct() {

        add_filter('pllat_available_post_meta_translation_fields_for', [$this, 'include_activated_post_meta_fields'], 10, 2);
        add_filter('pllat_available_term_meta_translation_fields_for', [$this, 'include_activated_term_meta_fields'], 10, 2);

        if ($this->is_acf_active()) {
            $this->acf_manager = new ACF_Manager();

            add_action('pllat_post_meta_translated', [$this, 'copy_acf_post_field_meta'], 10, 2);
            add_action('pllat_term_meta_translated', [$this, 'copy_acf_term_field_meta'], 10, 2);
        }
    }

    public function include_activated_post_meta_fields($fields, $post_id): array {

        $fields = array_merge(
            $fields,
            $this->get_activated_post_fields()
        );

        if ($this->is_acf_active() && !empty($post_id)) {
            $fields = array_merge($fields, $this->acf_manager->get_all_acf_post_field_meta_keys($post_id));
        }
        return $fields;
    }

    public function include_activated_term_meta_fields($fields, $term_id): array {
        $fields = array_merge(
            $fields,
            $this->get_activated_term_fields()
        );

        if ($this->is_acf_active() && !empty($term_id)) {
            $fields = array_merge($fields, $this->acf_manager->get_all_acf_term_field_meta_keys($term_id));
        }
        return $fields;
    }

    public function copy_acf_post_field_meta($target_id, $source_id) {

        // If no Polylang pro, copy the supporting meta data from the source post to the target post
        if (property_exists(PLL(), 'sync_post_model' )) {
            return;
        }

        // Get all acf meta data from the source post
        $all_acf_meta = \acf_get_meta($source_id);

        // Get the available translatable meta keys
        $translatable_meta_keys = Post_Meta_Translation::get_available_fields_for($source_id);

        // Get the meta data that is not meant for translation, so the supporting meta data
        $acf_field_meta_keys = array_diff(array_keys($all_acf_meta), $translatable_meta_keys);

        // Update the target post with the supporting acf meta data 
        foreach ($acf_field_meta_keys as $acf_field_meta_key) {
            update_post_meta($target_id, $acf_field_meta_key, $all_acf_meta[$acf_field_meta_key]);
        }
    }

    public function copy_acf_term_field_meta($target_id, $source_id) {

        if (property_exists(PLL(), 'sync_term_model' )) {
            return;
        }

        // Get all acf meta data from the source term
        $all_acf_meta = \acf_get_meta('term_' . $source_id);

        // Get the available translatable meta keys
        $translatable_meta_keys = Term_Meta_Translation::get_available_fields_for($source_id);

        // Get the meta data that is not meant for translation, so the supporting meta data
        $acf_field_meta_keys = array_diff(array_keys($all_acf_meta), $translatable_meta_keys);

        // Update the target term with the supporting acf meta data 
        foreach ($acf_field_meta_keys as $acf_field_meta_key) {
            update_term_meta($target_id, $acf_field_meta_key, $all_acf_meta[$acf_field_meta_key]);
        }
    }


    private function is_acf_active(): bool {
        return class_exists('ACF');
    }

    private function get_activated_post_fields(): array {
        return get_option('pllat_activated_post_type_fields', []);
    }

    private function get_activated_term_fields(): array {
        return get_option('pllat_activated_term_fields', []);
    }

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

}

Custom_Fields::get_instance();
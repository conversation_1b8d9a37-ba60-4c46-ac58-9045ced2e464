<?php
use EPIC_WP\Polylang_Automatic_AI_Translation\Helpers;
use EPIC_WP\Polylang_Automatic_AI_Translation\Integrations\Elementor\Elementor_Content_Editor;
use EPIC_WP\Polylang_Automatic_AI_Translation\Language_Manager;
use EPIC_WP\Polylang_Automatic_AI_Translation\Settings;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\LLM_Translator;
use EPIC_WP\Polylang_Automatic_AI_Translation\Translator\Translator_Factory;

class Elementor_Integration {
    private static $instance = null;
    private $language_manager;
    private $content_editor;

    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->language_manager = xwp_app( 'pllat' )->get( Language_Manager::class );

        $translatable_keys    = Settings::get_elementor_translatable_keys();
        $this->content_editor = new Elementor_Content_Editor( $translatable_keys );

        add_action( 'elementor/document/before_save', array( $this, 'handle_elementor_save_before' ), 10 );
        add_action( 'elementor/editor/after_save', array( $this, 'handle_elementor_save_after' ), 10, 2 );

        add_filter(
            'pllat_available_post_translation_fields_for',
            array( $this, 'filter_available_post_translation_fields_for' ),
            10,
            2,
        );
        add_action(
            'pllat_queue_post_before_translation_processed',
            array( $this, 'handle_elementor_post_translation' ),
            10,
            6,
        );
        add_action( 'pllat_after_populate_queue', array( $this, 'populate_elementor_queue' ), 10, 2 );
    }

    public function handle_elementor_save_before( $document ) {
        if ( ! $document instanceof \Elementor\Core\Base\Document ) {
            return;
        }
        $post_id = $document->get_main_id();

        if ( ! pll_is_translated_post_type( get_post_type( $post_id ) ) ) {
            return;
        }
        $elementor_content = $this->content_editor->get_elementor_post_content( $post_id );
        set_transient( 'pllat_before_elementor_content_' . $post_id, $elementor_content );
    }

    public function handle_elementor_save_after( $post_id, $editor_data ) {

        // Make sure the post type is allowed for translation
        if ( ! pll_is_translated_post_type( get_post_type( $post_id ) ) ) {
            return;
        }
    
        $changes = array();
    
        // Get the elementor data before the save
        $previous_data = get_transient( 'pllat_before_elementor_content_' . $post_id );
    
        // If there is no old elementor data, just extract all translatable strings from the editor data
        if ( ! $previous_data ) {
            $changes = array_keys( $this->content_editor->extract_translatable_strings( $editor_data ) );
        }
    
        // If there is old elementor data, get only the changed strings hashes
        if ( $previous_data ) {
            $changes = $this->content_editor->get_changed_strings_hashes(
                json_decode( $previous_data, true ),
                $editor_data,
            );
            delete_transient( 'pllat_before_elementor_content_' . $post_id );
        }
        
        // If there are no changes, do nothing
        if ( ! $changes ) {
            return;
        }

        // Add the changes to the translation queue
        $post_manager = Helpers::get_translatable_post_manager( $post_id );
        foreach ( $this->language_manager->get_available_languages() as $language ) {
            $post_manager->add_custom_data_to_queue( $language, '_pllat_elementor_data_changes', $changes );
        }
    }

    public function handle_elementor_post_translation( int $post_id, int $translated_id, string $source_language, string $target_language, bool $updating, array $args ) {
        // If the post is not built with Elementor, do nothing
        if ( ! $this->content_editor->is_built_with_elementor( $post_id ) ) {
            return;
        }

        // Sync template settings first
        $this->content_editor->sync_template_settings( $post_id, $translated_id );

        // Get the elementor data of the source post
        $source_elementor_data = $this->content_editor->get_elementor_post_content( $post_id, true );

        // Get the elementor data of the target post
        $target_elementor_data = $this->content_editor->get_elementor_post_content( $translated_id, true );

        if ( ! empty( $target_elementor_data ) ) {

            // Merge the source and target elementor content to keep the changes made in the source post
            $elementor_data = $this->content_editor->merge_elementor_post_content(
                $source_elementor_data,
                $target_elementor_data,
            );

        } else {
            $elementor_data = $source_elementor_data;
        }

        // If the translation is not forced, get the hashes of the changed strings from the source post
        if ( empty( $args['force_mode'] ) ) {

            // Get the hashes of the changed strings from the source post
            $change_hashes = $this->content_editor->get_elementor_post_content_changes(
                $post_id,
                $target_language,
            );

            // Not a new translation and no changes to translate, do nothing
            if ( empty( $change_hashes ) && $updating ) {
                return;
            }

            $strings_data = $this->content_editor->extract_translatable_strings(
                $source_elementor_data,
                $change_hashes,
            );

        } else {
            $strings_data = $this->content_editor->extract_translatable_strings( $source_elementor_data );
        }

        // Create an array with hashes as keys and the original strings as values for translation
        $hashed_strings = $this->prepare_strings_for_translation( $strings_data );

        // Translate the hashed strings array into a new array with hashed strings as keys and translated strings as values
        $translated_strings = $this->translate_strings( $hashed_strings, $source_language, $target_language );

        // Replace the translated strings in the target post content using the hashes
        $updated_elementor_data = $this->content_editor->replace_translated_strings(
            $elementor_data,
            $strings_data,
            $translated_strings,
        );

        // Update the target post content with the merged translated json content
        $this->content_editor->update_elementor_post_content( $translated_id, $updated_elementor_data );

        // Clear the Elementor cache for the translated post
        $this->clear_elementor_cache( $translated_id );

        // Remove the hashes of the changed strings from the target post content changes
        $this->content_editor->remove_elementor_post_content_changes( $translated_id, $target_language );
    }

    public function filter_available_post_translation_fields_for( $fields, $post_id ) {
        if ( $this->content_editor->is_built_with_elementor( $post_id ) ) {
            $fields = array_diff( $fields, array( 'post_content' ) );
        }
        return $fields;
    }

    public function filter_elementor_post_translation_input( $translation_input, $post ) {
        if ($this->content_editor->is_built_with_elementor( $post->get_id()  ) && ! empty( $translation_input['post_content'] )) {
            unset( $translation_input['post_content'] );
        }
        return $translation_input;
    }

    public function populate_elementor_queue( int $post_id, array $languages ) {
        // If the post is not built with Elementor, do nothing
        if ( ! $this->content_editor->is_built_with_elementor( $post_id ) ) {
            return;
        }

        if ( empty( $languages ) ) {
            return;
        }

        $post_manager      = Helpers::get_translatable_post_manager( $post_id );
        $elementor_content = $this->content_editor->get_elementor_post_content( $post_id, true );
        $strings_data      = $this->content_editor->extract_translatable_strings( $elementor_content );
        $change_hashes     = array_keys( $strings_data );
        foreach ( $languages as $language ) {
            $post_manager->add_custom_data_to_queue(
                $language,
                '_pllat_elementor_data_changes',
                $change_hashes,
            );
        }
    }

    private function prepare_strings_for_translation( array $strings_data ) {
        $hashed_strings = array();
        foreach ( $strings_data as $hash => $item ) {
            $hashed_strings[ $hash ] = $item['value'];
        }
        return $hashed_strings;
    }

    private function translate_strings( array $hashed_strings, string $source_language, string $target_language ) {
        $translator = Translator_Factory::create_translator( Settings::get_active_translation_api() );
        if ( $translator instanceof LLM_Translator ) {
            $placeholders       = array(
                'source_language' => $source_language,
                'target_language' => $target_language,
            );
            $translated_strings = $translator->translate_hashed_strings_list( $hashed_strings, $placeholders );
        } else {
            $translated_strings = $translator->translate_hashed_strings_list(
                $hashed_strings,
                $source_language,
                $target_language,
            );
        }
        return $translated_strings;
    }

    private function clear_elementor_cache( int $post_id ) {
        // Check if Elementor Pro is active
        if ( class_exists( '\ElementorPro\Plugin' ) ) {
            $document = \ElementorPro\Plugin::elementor()->documents->get( $post_id );
            if ( $document ) {
                $document->delete_main_meta( '_elementor_css' );
            }
        }
        // Clear Elementor's general cache
        if ( ! class_exists( '\Elementor\Plugin' ) ) {
            return;
        }

        \Elementor\Plugin::$instance->files_manager->clear_cache();
    }
}

Elementor_Integration::get_instance();

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ 962:
/*!******************************!*\
  !*** ./scripts/nav-menus.ts ***!
  \******************************/
/***/ (() => {

eval("jQuery(document).ready(function ($) {\n  var objectId = $(\"#nav-menu-meta-object-id\").val();\n  var $bodyContent = \"#post-body-content\";\n  var $menuSettings = $(\".menu-settings\", $bodyContent);\n  var loadTranslationSection = function loadTranslationSection() {\n    $.get(ajaxurl, {\n      action: \"pllat_get_translation_section_html\",\n      object_id: objectId\n    }, function (html) {\n      $menuSettings.before(html);\n    });\n  };\n  loadTranslationSection();\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTYyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9zY3JpcHRzL25hdi1tZW51cy50cz85YzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImpRdWVyeShkb2N1bWVudCkucmVhZHkoZnVuY3Rpb24gKCQpIHtcbiAgdmFyIG9iamVjdElkID0gJChcIiNuYXYtbWVudS1tZXRhLW9iamVjdC1pZFwiKS52YWwoKTtcbiAgdmFyICRib2R5Q29udGVudCA9IFwiI3Bvc3QtYm9keS1jb250ZW50XCI7XG4gIHZhciAkbWVudVNldHRpbmdzID0gJChcIi5tZW51LXNldHRpbmdzXCIsICRib2R5Q29udGVudCk7XG4gIHZhciBsb2FkVHJhbnNsYXRpb25TZWN0aW9uID0gZnVuY3Rpb24gbG9hZFRyYW5zbGF0aW9uU2VjdGlvbigpIHtcbiAgICAkLmdldChhamF4dXJsLCB7XG4gICAgICBhY3Rpb246IFwicGxsYXRfZ2V0X3RyYW5zbGF0aW9uX3NlY3Rpb25faHRtbFwiLFxuICAgICAgb2JqZWN0X2lkOiBvYmplY3RJZFxuICAgIH0sIGZ1bmN0aW9uIChodG1sKSB7XG4gICAgICAkbWVudVNldHRpbmdzLmJlZm9yZShodG1sKTtcbiAgICB9KTtcbiAgfTtcbiAgbG9hZFRyYW5zbGF0aW9uU2VjdGlvbigpO1xufSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///962\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__[962]();
/******/ 	
/******/ })()
;
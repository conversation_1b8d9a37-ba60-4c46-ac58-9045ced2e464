/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ 93:
/*!**************************!*\
  !*** ./scripts/admin.ts ***!
  \**************************/
/***/ (() => {

eval("function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\njQuery(document).ready(function ($) {\n  $(\".js-help-tip\").each(function () {\n    var tooltip = $('<div class=\"custom-tooltip\">' + $(this).data(\"tooltip\") + \"</div>\").appendTo(\"body\");\n    $(this).hover(function (e) {\n      // mouseenter\n      var pos = $(this).offset();\n      tooltip.css({\n        top: pos.top - tooltip.outerHeight() - 10,\n        left: pos.left - tooltip.outerWidth() / 2 + $(this).outerWidth() / 2\n      }).fadeIn(0);\n    }, function () {\n      // mouseleave\n      tooltip.fadeOut(0);\n    });\n  });\n});\n\n/**\n * Polylang Automatic AI Translation Admin JavaScript\n */\n(function ($) {\n  \"use strict\";\n\n  /**\n   * Manages the translator API fields, their visibility, and AJAX saving\n   */\n  var TranslatorAPIFieldsManager = /*#__PURE__*/function () {\n    /**\n     * Initialize selectors and bind events\n     */\n    function TranslatorAPIFieldsManager() {\n      _classCallCheck(this, TranslatorAPIFieldsManager);\n      this.initSelectors();\n      this.initEventHandlers();\n      this.initUI();\n    }\n\n    /**\n     * Define all selectors used in the class\n     */\n    return _createClass(TranslatorAPIFieldsManager, [{\n      key: \"initSelectors\",\n      value: function initSelectors() {\n        this.selectors = {\n          // Main API selector\n          translatorApiField: \"#pllat_translator_api\",\n          // OpenAI fields\n          openAiWrapper: \"#pllat_openai_api_key_wrapper\",\n          openAiApiKey: \"#pllat_openai_api_key\",\n          openAiModel: \"#pllat_openai_translation_model\",\n          // OpenRouter fields\n          openRouterWrapper: \"#pllat_openrouter_api_key_wrapper\",\n          openRouterApiKey: \"#pllat_openrouter_api_key\",\n          openRouterModel: \"#pllat_openrouter_translation_model\",\n          freeModelNotice: \"#pllat-free-model-notice\",\n          // Form and status elements\n          saveStatus: \".js-pllat-save-status\",\n          spinnerIcon: \".js-pllat-spinner\",\n          modelContainer: \".js-model-container\"\n        };\n      }\n\n      /**\n       * Bind all event handlers\n       */\n    }, {\n      key: \"initEventHandlers\",\n      value: function initEventHandlers() {\n        var self = this;\n\n        // Handle API selector change\n        $(this.selectors.translatorApiField).on(\"change\", function () {\n          var selectedApi = $(this).val();\n          self.handleApiSelectorChange(selectedApi);\n          if (selectedApi) {\n            self.loadModels(selectedApi);\n          }\n          self.saveSettings();\n        });\n\n        // Handle OpenAI field changes\n        $(this.selectors.openAiApiKey).on(\"change blur\", function () {\n          self.saveSettings();\n        });\n        $(this.selectors.openAiModel).on(\"change\", function () {\n          self.saveSettings();\n        });\n\n        // Handle OpenRouter field changes\n        $(this.selectors.openRouterApiKey).on(\"change blur\", function () {\n          self.saveSettings();\n        });\n        $(this.selectors.openRouterModel).on(\"change\", function () {\n          self.saveSettings();\n          self.updateFreeModelNotice();\n        });\n\n        // Initial check for free model notice\n        this.updateFreeModelNotice();\n      }\n\n      /**\n       * Initialize UI based on current settings\n       */\n    }, {\n      key: \"initUI\",\n      value: function initUI() {\n        var selectedApi = $(this.selectors.translatorApiField).val();\n        this.handleApiSelectorChange(selectedApi);\n      }\n\n      /**\n       * Show/hide API key and model fields based on selected API\n       */\n    }, {\n      key: \"handleApiSelectorChange\",\n      value: function handleApiSelectorChange(selectedApi) {\n        // Hide all API key and model wrappers first\n        $(this.selectors.openAiWrapper).hide();\n        $(this.selectors.openRouterWrapper).hide();\n        $(this.selectors.freeModelNotice).hide();\n\n        // Show the selected API's fields if any API is selected\n        if (selectedApi) {\n          switch (selectedApi) {\n            case \"openai\":\n              $(this.selectors.openAiWrapper).show();\n              break;\n            case \"openrouter\":\n              $(this.selectors.openRouterWrapper).show();\n              this.updateFreeModelNotice();\n              break;\n          }\n        }\n      }\n\n      /**\n       * Load models for the selected API\n       */\n    }, {\n      key: \"loadModels\",\n      value: function loadModels(apiType) {\n        var self = this;\n        var apiKey = apiType === \"openai\" ? $(this.selectors.openAiApiKey).val() : $(this.selectors.openRouterApiKey).val();\n\n        // Show loading indicator in the model container\n        var modelSelector = apiType === \"openai\" ? this.selectors.openAiModel : this.selectors.openRouterModel;\n        $(modelSelector).prop(\"disabled\", true);\n        $(this.selectors.spinnerIcon).addClass(\"is-active\");\n        // Request models via AJAX\n        $.ajax({\n          url: PLLAT_Admin.ajax_url,\n          type: \"POST\",\n          data: {\n            action: \"pllat_get_translation_api_models\",\n            translator_api: apiType,\n            api_key: apiKey,\n            nonce: PLLAT_Admin.nonce\n          },\n          success: function success(response) {\n            if (response.success && response.data) {\n              $(modelSelector).html(response.data.html);\n            } else {\n              // Show error message\n              var errorMsg = response.data ? response.data.message : \"Failed to load models\";\n              $(self.selectors.saveStatus).text(\"Error: \" + errorMsg);\n            }\n          },\n          error: function error() {\n            $(self.selectors.saveStatus).text(\"Error: Failed to load models\");\n          },\n          complete: function complete() {\n            $(modelSelector).prop(\"disabled\", false);\n            $(self.selectors.spinnerIcon).removeClass(\"is-active\");\n          }\n        });\n      }\n\n      /**\n       * Save settings via AJAX\n       */\n    }, {\n      key: \"saveSettings\",\n      value: function saveSettings() {\n        var self = this;\n        var selectedApi = $(this.selectors.translatorApiField).val();\n\n        // Show saving indicator\n        $(this.selectors.spinnerIcon).addClass(\"is-active\");\n        $(this.selectors.saveStatus).text(\"Saving...\");\n        var data = {\n          action: \"pllat_save_translator_api_settings\",\n          translator_api: selectedApi,\n          nonce: PLLAT_Admin.nonce\n        };\n\n        // Add API-specific fields to the data\n        if (selectedApi === \"openai\") {\n          data.api_key = $(this.selectors.openAiApiKey).val();\n          data.model = $(this.selectors.openAiModel).val();\n        } else if (selectedApi === \"openrouter\") {\n          data.api_key = $(this.selectors.openRouterApiKey).val();\n          data.model = $(this.selectors.openRouterModel).val();\n        }\n\n        // Send AJAX request\n        $.post(PLLAT_Admin.ajax_url, data).done(function (response) {\n          if (response.success) {\n            $(self.selectors.saveStatus).text(\"Settings saved!\");\n\n            // If models were updated, refresh the page\n            if (response.data && response.data.models_updated) {\n              setTimeout(function () {\n                window.location.reload();\n              }, 1000);\n            }\n          } else {\n            $(self.selectors.saveStatus).text(\"Error: \" + (response.data ? response.data.message : \"Unknown error\"));\n          }\n        }).fail(function () {\n          $(self.selectors.saveStatus).text(\"Error: Failed to save settings\");\n        }).always(function () {\n          $(self.selectors.spinnerIcon).removeClass(\"is-active\");\n\n          // Clear status message after 3 seconds\n          setTimeout(function () {\n            $(self.selectors.saveStatus).text(\"\");\n          }, 3000);\n        });\n      }\n\n      /**\n       * Show/hide free model notice based on selected model\n       */\n    }, {\n      key: \"updateFreeModelNotice\",\n      value: function updateFreeModelNotice() {\n        var selectedModel = $(this.selectors.openRouterModel).val();\n        var isFreeModel = selectedModel && selectedModel.includes(\":free\");\n        $(this.selectors.freeModelNotice).toggle(isFreeModel);\n      }\n    }]);\n  }(); // Initialize the translator API fields manager on document ready\n  $(document).ready(function () {\n    if ($(\"#pllat_translator_api\").length) {\n      new TranslatorAPIFieldsManager();\n    }\n  });\n})(jQuery);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///93\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__[93]();
/******/ 	
/******/ })()
;
(()=>{"use strict";var e={n:s=>{var t=s&&s.__esModule?()=>s.default:()=>s;return e.d(t,{a:t}),t},d:(s,t)=>{for(var a in t)e.o(t,a)&&!e.o(s,a)&&Object.defineProperty(s,a,{enumerable:!0,get:t[a]})},o:(e,s)=>Object.prototype.hasOwnProperty.call(e,s)};const s=window.wp.element,t={status:"stopped",isLoading:!1,languages:[],postTypes:[],taxonomies:[],stringGroups:[],settings:{languages:[],postTypes:[],taxonomies:[],stringGroups:[],termFilters:{},instructions:"",bulkSize:500,forceMode:!1},stats:{},error:null,logs:[],actionSchedulerHealthCheck:{cronType:"",isAvailable:!1,issues:[]}},a=(e,s)=>{switch(s.type){case"SET_STATUS":return["stopped","running","activated"].includes(s.payload)?{...e,status:s.payload}:e;case"SET_IS_LOADING":return{...e,isLoading:s.payload};case"SET_POST_TYPES":return{...e,postTypes:s.payload};case"SET_TAXONOMIES":return{...e,taxonomies:s.payload};case"SET_LANGUAGES":return{...e,languages:s.payload};case"SET_STRING_GROUPS":return{...e,stringGroups:s.payload};case"SET_STATS":return{...e,stats:s.payload};case"SET_ERROR":return{...e,error:s.payload};case"UPDATE_SETTINGS":return{...e,settings:{...e.settings,...s.payload}};case"TOGGLE_POST_TYPE":return{...e,settings:{...e.settings,postTypes:e.settings.postTypes.includes(s.payload)?e.settings.postTypes.filter((e=>e!==s.payload)):[...e.settings.postTypes,s.payload]}};case"TOGGLE_TAXONOMY":return{...e,settings:{...e.settings,taxonomies:e.settings.taxonomies.includes(s.payload)?e.settings.taxonomies.filter((e=>e!==s.payload)):[...e.settings.taxonomies,s.payload]}};case"TOGGLE_STRING_GROUP":return{...e,settings:{...e.settings,stringGroups:e.settings.stringGroups.includes(s.payload)?e.settings.stringGroups.filter((e=>e!==s.payload)):[...e.settings.stringGroups,s.payload]}};case"SET_ALL_POST_TYPES":return{...e,settings:{...e.settings,postTypes:s.payload?e.postTypes.map((e=>e.slug)):[]}};case"SET_ALL_TAXONOMIES":return{...e,settings:{...e.settings,taxonomies:s.payload?e.taxonomies:[]}};case"SET_ALL_STRING_GROUPS":return{...e,settings:{...e.settings,stringGroups:s.payload?e.stringGroups:[]}};case"SET_LOGS":return{...e,logs:s.payload};case"SET_ACTION_SCHEDULER_HEALTH_CHECK":return{...e,actionSchedulerHealthCheck:{...e.actionSchedulerHealthCheck,isAvailable:s.payload.is_available,cronType:s.payload.cron_type,issues:s.payload.issues}};case"SET_ERRORS":return{...e,errors:s.payload};case"UPDATE_TERM_FILTERS":return{...e,settings:{...e.settings,termFilters:s.payload}};default:return e}},l=window.ReactJSXRuntime,n=(0,s.createContext)(),r=({children:e})=>{const[r,i]=(0,s.useReducer)(a,t);return(0,l.jsx)(n.Provider,{value:{state:r,dispatch:i},children:e})},i=()=>(0,s.useContext)(n),o=window.wp.apiFetch;var c=e.n(o);const d=()=>{const{dispatch:e}=i(),t=(0,s.useCallback)((async()=>{try{e({type:"SET_IS_LOADING",payload:!0});const s=await c()({path:"/pllat/v1/bulk-translator",method:"GET"});e({type:"SET_STATUS",payload:s.status}),e({type:"SET_LANGUAGES",payload:s.languages}),e({type:"SET_POST_TYPES",payload:s.postTypes}),e({type:"SET_TAXONOMIES",payload:s.taxonomies}),e({type:"SET_STRING_GROUPS",payload:s.stringGroups}),e({type:"SET_ERRORS",payload:s.errors}),s.settings&&e({type:"UPDATE_SETTINGS",payload:s.settings}),s.stats&&e({type:"SET_STATS",payload:s.stats}),s.logs&&e({type:"SET_LOGS",payload:s.logs}),s.actionSchedulerHealthCheck&&e({type:"SET_ACTION_SCHEDULER_HEALTH_CHECK",payload:s.actionSchedulerHealthCheck})}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e]),a=(0,s.useCallback)((async s=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/settings/bulk-size",method:"POST",data:{bulkSize:s}}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),l=(0,s.useCallback)((async s=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/settings/additional-instructions",method:"POST",data:{additionalInstructions:s}}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),n=(0,s.useCallback)((async s=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/settings/force-mode",method:"POST",data:{forceMode:s}}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),r=(0,s.useCallback)((async s=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/settings/languages",method:"POST",data:{languages:s}}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),o=(0,s.useCallback)((async(s,a)=>{try{e({type:"SET_IS_LOADING",payload:!0});const l="postTypes"===s?"post-types":"stringGroups"===s?"string-groups":s;await c()({path:`/pllat/v1/bulk-translator/settings/${l}`,method:"POST",data:{[s]:a}}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),d=(0,s.useCallback)((e=>o("postTypes",e)),[o]),p=(0,s.useCallback)((e=>o("taxonomies",e)),[o]),u=(0,s.useCallback)((e=>o("stringGroups",e)),[o]),m=(0,s.useCallback)((async s=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/settings/action-scheduler",method:"POST",data:s}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),g=(0,s.useCallback)((async()=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/start",method:"POST"}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),x=(0,s.useCallback)((async()=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/stop",method:"POST"}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]),h=(0,s.useCallback)((async()=>{const s=await c()({path:"/pllat/v1/bulk-translator/stats",method:"GET"});e({type:"SET_STATS",payload:s})}),[e]),y=(0,s.useCallback)((async()=>{const s=await c()({path:"/pllat/v1/bulk-translator/logs",method:"GET"});e({type:"SET_LOGS",payload:s})}),[e]),b=(0,s.useCallback)((async()=>{const s=await c()({path:"/pllat/v1/bulk-translator/realtime",method:"GET"});e({type:"SET_LOGS",payload:s.logs}),e({type:"SET_STATS",payload:s.stats}),e({type:"SET_STATUS",payload:s.status}),e({type:"SET_ERRORS",payload:s.errors})}),[e]),S=(0,s.useCallback)((async()=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/prepare",method:"POST"}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}}),[e,t]),j=(0,s.useCallback)((async s=>{try{return await c()({path:`/pllat/v1/bulk-translator/taxonomy-terms?post_type=${s}`,method:"GET"})}catch(s){return e({type:"SET_ERROR",payload:s.message}),[]}}),[e]),T=(0,s.useCallback)((async s=>{try{e({type:"SET_IS_LOADING",payload:!0}),await c()({path:"/pllat/v1/bulk-translator/settings/term-filters",method:"POST",data:{termFilters:s}}),await t()}catch(s){e({type:"SET_ERROR",payload:s.message})}finally{e({type:"SET_IS_LOADING",payload:!1})}}),[e,t]);return{fetchState:t,saveLanguageSettings:r,savePostTypeSettings:d,saveTaxonomySettings:p,saveStringGroupSettings:u,saveBulkSize:a,saveAdditionalInstructions:l,saveActionSchedulerSettings:m,start:g,stop:x,fetchStats:h,prepare:S,fetchLogs:y,fetchRealtimeUpdates:b,saveForceMode:n,fetchTaxonomyTerms:j,saveTermFilters:T}},p=({clickHandler:e})=>(0,l.jsx)("button",{className:"!px-5 !py-2 !rounded-lg !font-semibold button button-primary shadow-md cursor-pointer",onClick:e,children:"Start bulk translation"}),u=({clickHandler:e})=>(0,l.jsx)("button",{className:"!px-5 !py-2 !rounded-lg !font-semibold button shadow-md cursor-pointer !bg-red-500 !border-red-500 !text-white",onClick:e,children:"Stop bulk translation"}),m=({clickHandler:e})=>(0,l.jsx)("button",{className:"!px-5 !py-2 !rounded-lg !font-semibold button cursor-pointer",onClick:e,children:"Refresh"}),g=()=>{const{state:e}=i(),{start:s,stop:t,prepare:a}=d();return(0,l.jsxs)("div",{className:"flex items-center justify-between gap-8 mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"flex items-center gap-2",children:(0,l.jsx)("div",{className:"!font-semibold !text-3xl",children:"AI Bulk Translator"})}),(0,l.jsx)("p",{children:"Please select the languages, post types, taxonomies and string groups you would like to translate in bulk.\n          Then click the start button to let the AI do its magic."})]}),(0,l.jsx)("div",{children:(0,l.jsxs)("div",{className:"flex gap-2",children:["stopped"==e.status&&(0,l.jsx)(m,{clickHandler:async()=>{await a()}}),"stopped"===e.status&&(0,l.jsx)(p,{clickHandler:async()=>{await s()}})||(0,l.jsx)(u,{clickHandler:async()=>{await t()}})]})})]})},x=({icon:e,title:t,children:a,open:n=!1})=>{const[r,i]=(0,s.useState)(n);return(0,l.jsxs)("div",{children:[(0,l.jsxs)("button",{className:"w-full px-5 py-4 text-left font-medium border-none flex items-center justify-between rounded-lg shadow transition-all cursor-pointer text-black bg-white",onClick:()=>i(!r),children:[(0,l.jsxs)("span",{className:"flex items-center text-[15px]",children:[(0,l.jsx)("span",{className:`dashicons dashicons-${e} mr-2 text-[#787c82]`}),t]}),(0,l.jsx)("span",{className:"dashicons dashicons-arrow-down-alt2",style:{transform:r?"rotate(180deg)":"rotate(0)"}})]}),(0,l.jsx)("div",{className:"mt-1 p-4 bg-white rounded-lg "+(r?"":"hidden"),children:a})]})},h=()=>{const{state:e}=i(),{saveLanguageSettings:s}=d();if(!e.languages?.length)return null;const t=e.languages.length>0&&e.languages.every((s=>(e.settings.languages||[]).includes(s.slug)));return(0,l.jsx)("div",{children:(0,l.jsxs)("div",{className:"gap-1 flex flex-wrap",children:[(0,l.jsxs)("label",{htmlFor:"pllat_language_select_all",className:"js-language-select-all-container p-3 flex gap-1 items-center rounded-lg bg-gray-200 cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",id:"pllat_language_select_all",className:"js-language-select-all !m-0 !mr-1",checked:t,onChange:async t=>{const a=e.languages.map((e=>e.slug)),l=t.target.checked?a:[];await s(l)},disabled:"running"===e.status||"activated"===e.status}),(0,l.jsx)("span",{className:"js-text",children:t?"Deselect all":"Select all"})]}),e.languages.map((t=>(0,l.jsxs)("label",{htmlFor:`pllat_include_language_checkbox_${t.slug}`,className:"p-3 flex gap-1 items-center rounded-lg bg-gray-200 cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",id:`pllat_include_language_checkbox_${t.slug}`,className:"js-included-language-checkbox !m-0 !mr-1",checked:(e.settings.languages||[]).includes(t.slug),onChange:()=>(async t=>{const a=e.settings.languages||[],l=a.includes(t.slug)?a.filter((e=>e!==t.slug)):[...a,t.slug];await s(l)})(t),disabled:"running"===e.status||"activated"===e.status}),(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("img",{src:t.flag,alt:t.name})}),(0,l.jsxs)("span",{children:[t.name," (",t.slug.toUpperCase(),")"]})]},t.slug)))]})})},y=({type:e,items:s,settingsKey:t,saveSettings:a,getLabel:n=e=>"object"==typeof e?e.label:e,getValue:r=e=>"object"==typeof e?e.slug:e,getContent:o=()=>null,renderExtra:c=()=>null})=>{const{state:d,dispatch:p}=i(),{settings:u}=d,m=u[t]||[],g=m.length>0;return(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"mb-3",children:(0,l.jsxs)("label",{className:"flex items-center p-3 border border-solid rounded border-[#c3c4c7] bg-gray-200 hover:border-[#999] transition-colors",children:[(0,l.jsx)("input",{type:"checkbox",className:"!m-0 !mr-2",checked:g,onChange:async t=>{p({type:`SET_ALL_${e.toUpperCase()}`,payload:t.target.checked});const l=t.target.checked?s.map((e=>r(e))):[];await a(l)},disabled:"running"===d.status||"activated"===d.status}),(0,l.jsx)("span",{className:"text-[13px] font-medium",children:g?"Deselect all":"Select all"})]})}),(0,l.jsx)("ul",{className:"space-y-2",children:s.map(((s,t)=>{const i=r(s),u=m.includes(i),g=o(s);return(0,l.jsxs)("li",{children:[(0,l.jsxs)("div",{className:"flex items-center p-3 border border-solid rounded border-[#c3c4c7] bg-gray-50 hover:border-[#999] transition-colors",children:[(0,l.jsx)("input",{type:"checkbox",id:`${e}_checkbox_${t}`,className:"!m-0 !mr-2",value:i,checked:u,onChange:()=>(async s=>{const t=r(s);p({type:`TOGGLE_${e.toUpperCase()}`,payload:t});const l=m.includes(t)?m.filter((e=>e!==t)):[...m,t];await a(l)})(s),disabled:"running"===d.status||"activated"===d.status}),(0,l.jsx)("label",{className:"text-[13px] flex-grow",htmlFor:`${e}_checkbox_${t}`,children:n(s)}),c(s)]}),g&&(0,l.jsx)("div",{className:"mt-2 mb-4 p-2 border-l-2 border-gray-200",children:g})]},i)}))})]})},b=window.wp.components,S=({postType:e})=>{const{state:t,dispatch:a}=i(),{fetchTaxonomyTerms:n,saveTermFilters:r}=d(),[o,c]=(0,s.useState)([]),[p,u]=(0,s.useState)(!1),{termFilters:m={}}=t.settings,g=m[e]||{};(0,s.useEffect)((()=>{x()}),[e]);const x=async()=>{try{u(!0);const s=await n(e);c(s)}catch(e){console.error("Error loading taxonomy terms:",e)}finally{u(!1)}},h=e=>(g[e.slug]||[]).map((s=>{const t=e.terms.find((e=>e.id===s));return t?t.name:null})).filter(Boolean),y=e=>e.terms.map((e=>e.name));return p?(0,l.jsx)("div",{className:"p-4 text-center",children:"Loading..."}):0===o.length?(0,l.jsx)("div",{className:"p-4 text-center",children:"No filterable taxonomies available for this post type."}):(console.log(o),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"text-xs text-gray-500 mb-3 bg-blue-50 p-3 rounded-md",children:[(0,l.jsx)("strong",{children:"Filter by taxonomy terms:"})," Posts that match with"," ",(0,l.jsx)("strong",{children:"ANY"})," of the selected terms within each taxonomy will be included in translation. When you select terms, only posts with at least one of these terms will be included in this bulk translation."]}),o.map((s=>s.terms.length>0&&(0,l.jsxs)("div",{className:"border border-solid rounded border-[#c3c4c7] p-4",children:[(0,l.jsxs)("div",{className:"mb-3 font-medium flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-md",children:s.label}),(0,l.jsxs)("span",{className:"text-xs text-gray-500",children:["(",s.slug,")"]})]}),(0,l.jsx)("div",{className:"pllat-token-field",children:(0,l.jsx)(b.FormTokenField,{value:h(s),suggestions:y(s),onChange:t=>(async(s,t)=>{const l=t.map((e=>{const t=s.terms.find((s=>s.name===e));return t?t.id:null})).filter(Boolean),n={...m};n[e]||(n[e]={}),l.length>0?n[e][s.slug]=l:delete n[e][s.slug],0===Object.keys(n[e]).length&&delete n[e],a({type:"UPDATE_TERM_FILTERS",payload:n}),await r(n)})(s,t),placeholder:`Search and select ${s.label.toLowerCase()}...`,maxSuggestions:5,tokenizeOnSpace:!1,label:!1,disabled:"running"===t.status||"activated"===t.status,__experimentalExpandOnFocus:!1,__experimentalShowHowTo:!1})}),(0,l.jsx)("div",{className:"pllat-terms-info mt-2 text-xs text-gray-500 flex justify-between",children:(0,l.jsxs)("span",{children:[s.terms.length," ",s.label.toLowerCase()," ","available"]})})]},s.slug)))]}))},j=()=>{const{state:e}=i(),{savePostTypeSettings:t}=d(),[a,n]=(0,s.useState)({});return(0,l.jsx)(y,{type:"POST_TYPE",items:e.postTypes,settingsKey:"postTypes",saveSettings:t,getLabel:e=>`${e.label} (${e.slug})`,getContent:s=>{const t=e.settings.postTypes.includes(s.slug),n=a[s.slug];return t&&n?(0,l.jsx)(S,{postType:s.slug}):null},renderExtra:s=>{const t=e.settings.postTypes.includes(s.slug),r=a[s.slug];return t?(0,l.jsx)("button",{type:"button",className:"ml-auto text-gray-500 hover:text-gray-700 focus:outline-none",onClick:e=>{var t;e.stopPropagation(),t=s.slug,n((e=>({...e,[t]:!e[t]})))},"aria-label":r?"Collapse":"Expand",children:(0,l.jsx)("span",{className:"dashicons dashicons-arrow-down-alt2",style:{transform:r?"rotate(180deg)":"rotate(0deg)",transition:"transform 0.2s ease-in-out"}})}):null}})},T=()=>{const{state:e}=i(),{saveTaxonomySettings:s}=d();return(0,l.jsx)(y,{type:"TAXONOMY",items:e.taxonomies,settingsKey:"taxonomies",saveSettings:s,getLabel:e=>`${e.label} (${e.slug})`})},N=()=>{const{state:e}=i(),{saveStringGroupSettings:s}=d();return(0,l.jsx)(y,{type:"STRING_GROUP",items:e.stringGroups,settingsKey:"stringGroups",saveSettings:s,getLabel:e=>`${e.label} (${e.slug})`})},_=()=>{const{state:e,dispatch:s}=i(),{settings:t}=e,{saveBulkSize:a,saveAdditionalInstructions:n,saveForceMode:r}=d(),o="running"===e.status||"activated"===e.status;return(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{htmlFor:"additional_instructions",className:"text-[13px] font-medium mb-2 block",children:"Additional Instructions (optional):"}),(0,l.jsx)("p",{className:"text-[12px] text-[#787c82] mb-2",children:"Add specific instructions for the AI translator (up to 1000 characters), such as brand names that should not be translated or whether to write formal or informal."}),(0,l.jsx)("textarea",{id:"additional_instructions",name:"additional_instructions",className:"w-full p-3 border border-[#c3c4c7] rounded",rows:"4",maxLength:"1000",placeholder:"E.g. The name of our brand is Green Fields. Never translate this name.",value:t.additionalInstructions||"",onChange:e=>{const t=e.target.value;s({type:"UPDATE_SETTINGS",payload:{additionalInstructions:t}})},onBlur:async()=>{await n(t.additionalInstructions||"")},disabled:o})]}),(0,l.jsx)("div",{className:"my-6 border-solid border-b border-[#dcdcde] opacity-30"}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{className:"text-[13px] font-medium mb-2 block",children:"Bulk Size"}),(0,l.jsx)("p",{className:"text-[12px] text-[#787c82] mb-2",children:"Maximum translations per content type in this cycle (up to 3000 items)."}),(0,l.jsx)("input",{type:"number",name:"bulk_size",value:t.bulkSize||1e3,onChange:e=>{const t=parseInt(e.target.value,10);t>=1&&t<=3e3&&s({type:"UPDATE_SETTINGS",payload:{bulkSize:t}})},onBlur:async()=>{await a(t.bulkSize||1e3)},className:"!px-3 !py-1 !rounded !border !border-[#c3c4c7] !w-24",min:"1",max:"3000",disabled:o})]}),(0,l.jsx)("div",{className:"my-6 border-solid border-b border-[#dcdcde] opacity-30"}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center mb-2",children:[(0,l.jsx)("input",{type:"checkbox",className:"!m-0 !mr-2",checked:t.forceMode||!1,onChange:async e=>{const s=e.target.checked;await r(s)},disabled:o,id:"force_mode_checkbox"}),(0,l.jsx)("label",{htmlFor:"force_mode_checkbox",className:"text-[13px] font-medium",children:pllat.strings.settings.forceMode})]}),(0,l.jsx)("p",{className:"text-[12px] text-[#787c82]",children:pllat.strings.settings.forceModeHelp})]})]})},f=()=>{const{state:e,dispatch:s}=i(),{settings:t}=e,{saveActionSchedulerSettings:a}=d(),n=(e,a)=>{const l=parseInt(a,10);isNaN(l)||s({type:"UPDATE_SETTINGS",payload:{actionScheduler:{...t.actionScheduler,[e]:l}}})},r=async e=>{await a({[e]:t.actionScheduler[e]})},o="running"===e.status||"activated"===e.status;return(0,l.jsx)("div",{children:(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("label",{className:"text-[13px] font-medium mb-2 block",children:"Action Scheduler Settings"}),(0,l.jsx)("p",{className:"text-[12px] text-[#787c82] mb-6",children:"Configure how Action Scheduler processes the bulk translation queue. Changes will take effect after stopping and restarting the bulk translation."}),(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-5",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"text-[13px] font-medium mb-2 block",children:"Time Limit (seconds)"}),(0,l.jsx)("p",{className:"text-[12px] text-[#787c82] mb-2",children:"Maximum execution time per batch (10-300)."}),(0,l.jsx)("input",{type:"number",value:t.actionScheduler?.time_limit||30,onChange:e=>n("time_limit",e.target.value),onBlur:()=>r("time_limit"),className:"!px-3 !py-1 !rounded !border !border-[#c3c4c7] !w-24",min:"10",max:"300",disabled:o})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"text-[13px] font-medium mb-2 block",children:"Batch Size"}),(0,l.jsx)("p",{className:"text-[12px] text-[#787c82] mb-2",children:"Number of actions to process per batch (1-100)."}),(0,l.jsx)("input",{type:"number",value:t.actionScheduler?.batch_size||25,onChange:e=>n("batch_size",e.target.value),onBlur:()=>r("batch_size"),className:"!px-3 !py-1 !rounded !border !border-[#c3c4c7] !w-24",min:"1",max:"100",disabled:o})]})]})]})})},v=()=>{const{state:e}=i(),{postTypes:s,taxonomies:t,stringGroups:a}=e;return(0,l.jsxs)("div",{className:"col-span-2",children:[(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(x,{id:"languages",icon:"translation",title:"Languages",open:!0,children:(0,l.jsx)(h,{})})}),s?.length>0&&(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(x,{id:"post-types",icon:"admin-post",title:"Post Types",children:(0,l.jsx)(j,{})})}),t?.length>0&&(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(x,{id:"taxonomies",icon:"category",title:"Taxonomies",children:(0,l.jsx)(T,{})})}),a?.length>0&&(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(x,{id:"strings",icon:"editor-alignleft",title:"Text Strings",children:(0,l.jsx)(N,{})})}),(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(x,{id:"settings",icon:"admin-generic",title:"Additional Settings",open:!0,children:(0,l.jsx)(_,{})})}),(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(x,{id:"action-scheduler",icon:"performance",title:"Performance Settings",children:(0,l.jsx)(f,{})})})]})},E=(window.React,({status:e})=>{let s=(0,l.jsxs)("span",{children:["not ",(0,l.jsx)("u",{children:"activated"})," and is ",(0,l.jsx)("u",{children:"not running"}),"."]});return"activated"==e?s="waiting for scheduled translations to be processed in the background..":"running"==e&&(s="is currently running.."),"running"==e?(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("img",{src:`${window.pllat.plugin_url}/assets/images/ring-resize.svg`,className:"w-4",alt:"loading"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-semibold",children:"Status:"})," ",(0,l.jsx)("span",{className:"text-green-600",children:s})]})]}):"activated"==e?(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("img",{src:`${window.pllat.plugin_url}/assets/images/3-dots-bounce.svg`,className:"w-4",alt:"loading"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-semibold",children:"Status:"})," ",(0,l.jsx)("span",{children:s})]})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-semibold",children:"Status:"})," ",(0,l.jsx)("span",{children:s})]})}),w=({stats:e,status:s})=>{const t=e?.total||0,a=e?.processed||0,n=t>0?Math.round(a/t*100):0;return(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-[#c3c4c7] shadow mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"dashicons dashicons-chart-bar mr-2 text-gray-600"}),(0,l.jsx)("h3",{className:"text-lg font-semibold mt-0 mb-0",children:"Progress"})]}),(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)(E,{status:s})})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-4",children:(0,l.jsx)("div",{className:"bg-blue-600 h-4 rounded-full transition-all duration-500",style:{width:`${n}%`}})}),(0,l.jsxs)("div",{className:"flex justify-between mt-2 text-sm text-gray-600",children:[(0,l.jsxs)("span",{children:[n,"%"]}),(0,l.jsxs)("span",{children:[(0,l.jsx)("strong",{children:e?.total-e?.processed||0})," remaining,"," ",(0,l.jsx)("strong",{children:e?.processed||0})," translated",(0,l.jsxs)("span",{className:"text-gray-400 ml-2",children:["(Total: ",e?.total||0,")"]})]})]})]})},k=({type:e,title:s,stats:t})=>(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-[#c3c4c7] shadow",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold mb-4 pb-3 mt-0 border-b border-gray-200 flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:`dashicons ${(()=>{const s={post:"dashicons-admin-post",taxonomy:"dashicons-category",string:"dashicons-editor-alignleft"};return s[e]||s.post})()} mr-2 text-gray-600`}),s.charAt(0).toUpperCase()+s.slice(1)]}),(0,l.jsx)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full",children:"post"===e?"Post Type":"taxonomy"===e?"Taxonomy":"Text String"})]}),(0,l.jsx)("div",{className:"space-y-3",children:Object.entries(t).map((([e,s])=>{const t=(e=>window.pllat.languages.find((s=>e===s.slug)))(e),a=s.total||0,n=s.processed||0,r=a>0?n/a*100:0;return(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[t?.flag&&(0,l.jsx)("img",{src:t.flag,className:"w-5",alt:t.label}),(0,l.jsx)("span",{className:"font-medium",children:t?.label||e}),(0,l.jsxs)("span",{className:"text-gray-500",children:["(",e.toUpperCase(),")"]})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2 mr-3",children:(0,l.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${r}%`}})}),(0,l.jsxs)("span",{className:"text-sm",children:[(0,l.jsx)("strong",{children:s.total-s.processed||0})," ","remaining, ",(0,l.jsx)("strong",{children:s.processed||0})," translated",(0,l.jsxs)("span",{className:"text-gray-400 ml-2",children:["(Total: ",s.total||0,")"]})]})]})]},e)}))})]}),O=({logs:e})=>(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-[#c3c4c7] shadow mt-6",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold mb-4 mt-0 flex items-center",children:[(0,l.jsx)("span",{className:"dashicons dashicons-text-page mr-2 text-gray-600"}),"Bulk translation Logs"]}),(0,l.jsx)("div",{className:"bg-gray-100 p-6 rounded-lg h-[200px] overflow-y-auto font-mono text-xs border border-gray-300",children:window.pllat.canLog?Array.isArray(e)&&e.length>0?e.map((e=>(0,l.jsx)("div",{className:"mb-1",dangerouslySetInnerHTML:{__html:e}},e))):"Translation logs will appear here..":"Logs are disabled, because the log file is not writable. Please check the file permissions of wp-content/uploads/polylang-ai-automatic-translation/bulk-logs/"})]}),I=()=>{const{state:e}=i(),{stats:s}=e;return s?(0,l.jsxs)("div",{className:"col-span-4",children:[(0,l.jsx)(w,{stats:s,status:e.status}),(0,l.jsxs)("div",{className:"space-y-8 mb-4",children:[Object.entries(s.posts||{}).map((([e,s])=>(0,l.jsx)(k,{type:"post",title:e,stats:s},`post-${e}`))),Object.entries(s.terms||{}).map((([e,s])=>(0,l.jsx)(k,{type:"taxonomy",title:e,stats:s},`taxonomy-${e}`))),Object.entries(s.strings||{}).map((([e,s])=>(0,l.jsx)(k,{type:"string",title:e,stats:s},`string-${e}`)))]}),(0,l.jsx)("div",{children:(0,l.jsx)(O,{logs:e.logs})})]}):null},G=()=>{const{state:e}=i();return e.isLoading?(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-lg flex items-center gap-3",children:[(0,l.jsx)("span",{className:"spinner mx-0 is-active"}),(0,l.jsx)("span",{className:"text-gray-700",children:"Loading..."})]})}):null},A=({errors:e,className:s})=>e&&0!==e.length?(0,l.jsx)("div",{className:`bg-red-100 text-red-800 p-4 rounded-md !border-solid !border-red-200 mb-4 ${s||""}`,children:e.map(((e,s)=>(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,l.jsx)("span",{className:"dashicons dashicons-warning"}),(0,l.jsx)("span",{children:e.message||e}),e.timestamp&&(0,l.jsxs)("span",{className:"text-sm text-red-600",children:["(",new Date(1e3*e.timestamp).toLocaleString(),")"]})]},s)))}):null,R=()=>{const{fetchState:e,fetchRealtimeUpdates:t}=d(),{state:a}=i(),{status:n,stats:r,actionSchedulerHealthCheck:o,errors:c}=a;return(0,s.useEffect)((()=>{if(e(),"activated"===n||"running"===n){const e=setInterval((()=>{t()}),1e3);return()=>clearInterval(e)}}),[e,n,t]),(0,s.useEffect)((()=>{r.total>0&&r.total===r.processed&&e()}),[r.total,r.processed,e]),(0,l.jsxs)("div",{className:"bulk-translator-app",children:[(0,l.jsx)(G,{}),(0,l.jsx)(g,{}),o&&o.issues?.length>0&&(0,l.jsx)(A,{errors:o.issues}),(0,l.jsx)(A,{errors:c}),(0,l.jsxs)("div",{className:"grid grid-cols-6 gap-6",children:[(0,l.jsx)(v,{}),(0,l.jsx)(I,{})]})]})},L=()=>(0,l.jsx)(r,{children:(0,l.jsx)(R,{})});(0,s.render)((0,l.jsx)(L,{}),document.getElementById("pllat_bulk_translator"))})();
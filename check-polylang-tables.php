<?php
/**
 * Check Polylang database tables and setup
 */

// Load WordPress
require_once('wp-config.php');

global $wpdb;

echo "=== Polylang Database Check ===\n\n";

// Check if Polylang plugin is active
$active_plugins = get_option('active_plugins', array());
$polylang_active = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'polylang') !== false) {
        $polylang_active = true;
        echo "✅ Polylang plugin is active: $plugin\n";
        break;
    }
}

if (!$polylang_active) {
    echo "❌ Polylang plugin is not active\n";
}

// Check Polylang functions
echo "\n=== Polylang Functions ===\n";
$functions = [
    'PLL', 'pll_register_string', 'pll_set_string_translation', 
    'pll__', 'pll_e', 'pll_current_language', 'pll_the_languages'
];

foreach ($functions as $func) {
    $status = function_exists($func) ? '✅' : '❌';
    echo "$status $func\n";
}

// Check database tables
echo "\n=== Database Tables ===\n";
$tables = ['polylang_strings', 'polylang_mlt', 'polylang_mo'];

foreach ($tables as $table) {
    $full_table_name = $wpdb->prefix . $table;
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table_name'") == $full_table_name;
    $status = $exists ? '✅' : '❌';
    echo "$status $full_table_name\n";
    
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $full_table_name");
        echo "   └─ Records: $count\n";
    }
}

// Check Polylang options
echo "\n=== Polylang Options ===\n";
$polylang_options = get_option('polylang');
if ($polylang_options) {
    echo "✅ Polylang options exist\n";
    echo "   Version: " . ($polylang_options['version'] ?? 'Unknown') . "\n";
    echo "   Default language: " . ($polylang_options['default_lang'] ?? 'Not set') . "\n";
} else {
    echo "❌ Polylang options not found\n";
}

// Check languages
echo "\n=== Languages ===\n";
$languages = $wpdb->get_results("
    SELECT t.term_id, t.name, t.slug
    FROM {$wpdb->terms} t
    JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
    WHERE tt.taxonomy = 'language'
");

if ($languages) {
    echo "✅ Languages found:\n";
    foreach ($languages as $lang) {
        echo "   - {$lang->name} ({$lang->slug}) [ID: {$lang->term_id}]\n";
    }
} else {
    echo "❌ No languages found\n";
}

// Check if we can create tables manually
echo "\n=== Manual Table Creation Check ===\n";
if (!$wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}polylang_strings'")) {
    echo "❌ polylang_strings table missing - attempting to create...\n";
    
    // Create polylang_strings table
    $sql = "CREATE TABLE {$wpdb->prefix}polylang_strings (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        string longtext NOT NULL,
        context varchar(255) NOT NULL DEFAULT '',
        multiline tinyint(1) NOT NULL DEFAULT 0,
        PRIMARY KEY (id),
        KEY name (name),
        KEY context (context)
    ) {$wpdb->get_charset_collate()};";
    
    $result = $wpdb->query($sql);
    if ($result !== false) {
        echo "✅ polylang_strings table created successfully\n";
    } else {
        echo "❌ Failed to create polylang_strings table: " . $wpdb->last_error . "\n";
    }
}

if (!$wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}polylang_mlt'")) {
    echo "❌ polylang_mlt table missing - attempting to create...\n";
    
    // Create polylang_mlt table
    $sql = "CREATE TABLE {$wpdb->prefix}polylang_mlt (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        object_id bigint(20) unsigned NOT NULL,
        term_id bigint(20) unsigned NOT NULL,
        object_type varchar(20) NOT NULL,
        PRIMARY KEY (id),
        UNIQUE KEY object_id (object_id, term_id, object_type),
        KEY term_id (term_id)
    ) {$wpdb->get_charset_collate()};";
    
    $result = $wpdb->query($sql);
    if ($result !== false) {
        echo "✅ polylang_mlt table created successfully\n";
    } else {
        echo "❌ Failed to create polylang_mlt table: " . $wpdb->last_error . "\n";
    }
}

echo "\n=== Check Complete ===\n";
